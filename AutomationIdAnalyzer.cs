using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using CognitiveDrive.CoreUIA;

/// <summary>
/// Analyzes AutomationId reliability across HET files to determine actuator readiness.
/// This is critical for determining if we can reliably target UI elements for actions.
/// </summary>
class AutomationIdAnalyzer
{
    private class AnalysisResult
    {
        public string Application { get; set; } = "";
        public int TotalElements { get; set; }
        public int ActionableElements { get; set; }
        public int ElementsWithAutomationId { get; set; }
        public int ElementsWithUniqueAutomationId { get; set; }
        public int ElementsWithNameOnly { get; set; }
        public int ElementsWithNeitherIdNorName { get; set; }
        public double AutomationIdReliability => ActionableElements > 0 ? (double)ElementsWithAutomationId / ActionableElements * 100 : 0;
        public double UniqueIdReliability => ActionableElements > 0 ? (double)ElementsWithUniqueAutomationId / ActionableElements * 100 : 0;
        public List<string> ActionableControlTypes { get; set; } = new();
        public List<string> ProblematicElements { get; set; } = new();
    }

    static void Main(string[] args)
    {
        Console.WriteLine("=== CognitiveDrive AutomationId Reliability Analysis ===");
        Console.WriteLine("Analyzing HET files to determine actuator prerequisite readiness.");
        Console.WriteLine("This determines if we can reliably target UI elements for actions.");
        Console.WriteLine();

        var hetFiles = new[]
        {
            ("VS Code", "vscode_automation_analysis.json"),
            ("Notepad", "notepad_automation_analysis.json"),
            ("Brave Browser", "brave_automation_analysis.json")
        };

        var results = new List<AnalysisResult>();

        foreach (var (appName, fileName) in hetFiles)
        {
            if (File.Exists(fileName))
            {
                Console.WriteLine($"Analyzing {appName}...");
                var result = AnalyzeHETFile(appName, fileName);
                results.Add(result);
                PrintAnalysisResult(result);
                Console.WriteLine();
            }
            else
            {
                Console.WriteLine($"⚠️  {fileName} not found - skipping {appName}");
            }
        }

        // Overall assessment
        PrintOverallAssessment(results);
    }

    static AnalysisResult AnalyzeHETFile(string appName, string fileName)
    {
        try
        {
            var jsonContent = File.ReadAllText(fileName);
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };

            var hetRoot = JsonSerializer.Deserialize<UiaElementNode>(jsonContent, jsonOptions);
            if (hetRoot == null)
            {
                throw new Exception("Failed to deserialize HET");
            }

            var result = new AnalysisResult { Application = appName };
            AnalyzeNode(hetRoot, result, new HashSet<string>());

            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error analyzing {fileName}: {ex.Message}");
            return new AnalysisResult { Application = appName };
        }
    }

    static void AnalyzeNode(UiaElementNode node, AnalysisResult result, HashSet<string> seenAutomationIds)
    {
        result.TotalElements++;

        // Define actionable control types that an actuator would target
        var actionableTypes = new[]
        {
            "Button", "Edit", "ComboBox", "ListItem", "MenuItem", "TabItem",
            "CheckBox", "RadioButton", "Hyperlink", "Text", "TreeItem",
            "DataItem", "HeaderItem", "Slider", "ProgressBar"
        };

        bool isActionable = actionableTypes.Contains(node.ControlType);
        
        if (isActionable)
        {
            result.ActionableElements++;
            result.ActionableControlTypes.Add(node.ControlType);

            // Analyze AutomationId reliability
            bool hasAutomationId = !string.IsNullOrEmpty(node.AutomationId);
            bool hasName = !string.IsNullOrEmpty(node.Name);

            if (hasAutomationId)
            {
                result.ElementsWithAutomationId++;
                
                // Check if AutomationId is unique
                if (!seenAutomationIds.Contains(node.AutomationId))
                {
                    result.ElementsWithUniqueAutomationId++;
                    seenAutomationIds.Add(node.AutomationId);
                }
                else
                {
                    result.ProblematicElements.Add($"Duplicate AutomationId: '{node.AutomationId}' ({node.ControlType})");
                }
            }
            else if (hasName)
            {
                result.ElementsWithNameOnly++;
            }
            else
            {
                result.ElementsWithNeitherIdNorName++;
                result.ProblematicElements.Add($"No ID or Name: {node.ControlType} at bounds {node.BoundingRectangle}");
            }
        }

        // Recursively analyze children
        if (node.Children != null)
        {
            foreach (var child in node.Children)
            {
                AnalyzeNode(child, result, seenAutomationIds);
            }
        }
    }

    static void PrintAnalysisResult(AnalysisResult result)
    {
        Console.WriteLine($"📊 {result.Application} Analysis Results:");
        Console.WriteLine($"   Total Elements: {result.TotalElements}");
        Console.WriteLine($"   Actionable Elements: {result.ActionableElements}");
        Console.WriteLine($"   Elements with AutomationId: {result.ElementsWithAutomationId} ({result.AutomationIdReliability:F1}%)");
        Console.WriteLine($"   Elements with Unique AutomationId: {result.ElementsWithUniqueAutomationId} ({result.UniqueIdReliability:F1}%)");
        Console.WriteLine($"   Elements with Name Only: {result.ElementsWithNameOnly}");
        Console.WriteLine($"   Elements with Neither ID nor Name: {result.ElementsWithNeitherIdNorName}");

        if (result.ProblematicElements.Any())
        {
            Console.WriteLine($"   ⚠️  Problematic Elements ({result.ProblematicElements.Count}):");
            foreach (var problem in result.ProblematicElements.Take(5))
            {
                Console.WriteLine($"      - {problem}");
            }
            if (result.ProblematicElements.Count > 5)
            {
                Console.WriteLine($"      ... and {result.ProblematicElements.Count - 5} more");
            }
        }

        // Actuator readiness assessment
        if (result.UniqueIdReliability >= 80)
        {
            Console.WriteLine($"   ✅ ACTUATOR READY: {result.UniqueIdReliability:F1}% unique AutomationId reliability");
        }
        else if (result.UniqueIdReliability >= 50)
        {
            Console.WriteLine($"   ⚠️  ACTUATOR CAUTION: {result.UniqueIdReliability:F1}% unique AutomationId reliability");
        }
        else
        {
            Console.WriteLine($"   ❌ ACTUATOR NOT READY: {result.UniqueIdReliability:F1}% unique AutomationId reliability");
        }
    }

    static void PrintOverallAssessment(List<AnalysisResult> results)
    {
        Console.WriteLine("=== OVERALL ACTUATOR READINESS ASSESSMENT ===");
        
        if (!results.Any())
        {
            Console.WriteLine("❌ No applications analyzed - cannot determine readiness");
            return;
        }

        var avgReliability = results.Average(r => r.UniqueIdReliability);
        var totalActionable = results.Sum(r => r.ActionableElements);
        var totalWithUniqueId = results.Sum(r => r.ElementsWithUniqueAutomationId);

        Console.WriteLine($"Average Unique AutomationId Reliability: {avgReliability:F1}%");
        Console.WriteLine($"Total Actionable Elements Analyzed: {totalActionable}");
        Console.WriteLine($"Total with Unique AutomationId: {totalWithUniqueId}");
        Console.WriteLine();

        if (avgReliability >= 70)
        {
            Console.WriteLine("🎯 VERDICT: READY FOR ACTUATOR SPRINT");
            Console.WriteLine("The foundation is strong enough for reliable UI element targeting.");
        }
        else if (avgReliability >= 40)
        {
            Console.WriteLine("⚠️  VERDICT: PROCEED WITH CAUTION");
            Console.WriteLine("Actuator will need fallback strategies for element targeting.");
        }
        else
        {
            Console.WriteLine("❌ VERDICT: NOT READY FOR ACTUATOR SPRINT");
            Console.WriteLine("Foundation requires strengthening before implementing actions.");
        }

        Console.WriteLine();
        Console.WriteLine("RECOMMENDATIONS:");
        Console.WriteLine("- Implement coordinate-based fallback for elements without AutomationId");
        Console.WriteLine("- Use Name property as secondary identifier");
        Console.WriteLine("- Consider relative positioning for unreliable elements");
        Console.WriteLine("- Implement element validation before action execution");
    }
}

{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj", "projectName": "CognitiveDrive.Tests.Integration", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "NUnit": {"target": "Package", "version": "[3.14.0, )"}, "NUnit.Analyzers": {"target": "Package", "version": "[3.9.0, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.5.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}
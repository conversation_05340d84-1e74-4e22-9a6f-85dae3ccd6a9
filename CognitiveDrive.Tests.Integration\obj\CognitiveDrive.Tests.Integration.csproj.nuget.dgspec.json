{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj", "projectName": "CognitiveDrive.Core", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj", "projectName": "CognitiveDrive.Infrastructure", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj", "projectName": "CognitiveDrive.Tests.Integration", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\CognitiveDrive.Tests.Integration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Tests.Integration\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "NUnit": {"target": "Package", "version": "[3.14.0, )"}, "NUnit.Analyzers": {"target": "Package", "version": "[3.9.0, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.5.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}
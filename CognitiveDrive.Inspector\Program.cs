using System;
using System.CommandLine;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Automation;
using CognitiveDrive.CoreUIA;

namespace CognitiveDrive.Inspector
{
    /// <summary>
    /// Main program class for the Cognitive Drive Inspection Tool
    /// </summary>
    class Program
    {
        /// <summary>
        /// Main entry point for the application
        /// </summary>
        /// <param name="args">Command line arguments</param>
        /// <returns>Exit code</returns>
        static async Task<int> Main(string[] args)
        {
            // Create the root command
            var rootCommand = new RootCommand("Cognitive Drive Inspection Tool");

            // Create the inspect command
            var inspectCommand = new Command("inspect", "Inspect UI elements.");
            rootCommand.AddCommand(inspectCommand);

            // Create the global command
            var globalCommand = new Command("global", "Inspects any running process.");
            var processOption = new Option<string>("--process", "Name of the process to inspect (e.g., 'notepad').") 
            { 
                IsRequired = true 
            };
            globalCommand.AddOption(processOption);

            // Set handler for global command
            globalCommand.SetHandler((processName) =>
            {
                try
                {
                    Console.WriteLine($"Inspecting process: {processName}");
                    Console.WriteLine("Searching for window...");

                    // Find the window by process name
                    AutomationElement rootElement = UiaScanner.FindWindowByProcessName(processName);
                    
                    Console.WriteLine("Window found! Scanning UI tree...");
                    Console.WriteLine(new string('=', 50));

                    // Build the UI tree
                    StringBuilder stringBuilder = new StringBuilder();
                    UiaScanner.GetElementTreeRaw(rootElement, stringBuilder, 0);

                    // Print the result
                    Console.WriteLine(stringBuilder.ToString());
                    Console.WriteLine(new string('=', 50));
                    Console.WriteLine("Scan complete.");
                }
                catch (ArgumentException ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                    Console.WriteLine($"Make sure the process '{processName}' is running.");
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Unexpected error: {ex.Message}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                }
            }, processOption);

            // Create the harness command
            var harnessCommand = new Command("harness", "Launches and inspects a new process.");
            var executableOption = new Option<string>("--executable", "Full path to the executable to launch.") 
            { 
                IsRequired = true 
            };
            harnessCommand.AddOption(executableOption);

            // Set handler for harness command
            harnessCommand.SetHandler(async (executablePath) =>
            {
                Process? launchedProcess = null;
                try
                {
                    Console.WriteLine($"Launching executable: {executablePath}");

                    // Validate the executable path
                    if (!File.Exists(executablePath))
                    {
                        Console.WriteLine($"Error: Executable not found at path '{executablePath}'");
                        return;
                    }

                    // Launch the process
                    ProcessStartInfo startInfo = new ProcessStartInfo
                    {
                        FileName = executablePath,
                        UseShellExecute = true
                    };

                    launchedProcess = Process.Start(startInfo);
                    
                    if (launchedProcess == null)
                    {
                        Console.WriteLine("Error: Failed to start the process.");
                        return;
                    }

                    Console.WriteLine($"Process launched with PID: {launchedProcess.Id}");
                    Console.WriteLine("Waiting for process to initialize...");

                    // Wait for the process to initialize its main window
                    await Task.Delay(3000); // Wait 3 seconds

                    // Additional wait and retry logic for main window
                    for (int i = 0; i < 5; i++)
                    {
                        launchedProcess.Refresh();
                        if (launchedProcess.MainWindowHandle != IntPtr.Zero)
                            break;
                        await Task.Delay(1000); // Wait additional 1 second per retry
                    }

                    // Get the process name (without .exe extension)
                    string processName = Path.GetFileNameWithoutExtension(executablePath);
                    
                    Console.WriteLine($"Inspecting launched process: {processName}");
                    Console.WriteLine("Searching for window...");

                    // Find the window by process name
                    AutomationElement rootElement = UiaScanner.FindWindowByProcessName(processName);
                    
                    Console.WriteLine("Window found! Scanning UI tree...");
                    Console.WriteLine(new string('=', 50));

                    // Build the UI tree
                    StringBuilder stringBuilder = new StringBuilder();
                    UiaScanner.GetElementTreeRaw(rootElement, stringBuilder, 0);

                    // Print the result
                    Console.WriteLine(stringBuilder.ToString());
                    Console.WriteLine(new string('=', 50));
                    Console.WriteLine("Scan complete.");
                }
                catch (ArgumentException ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Unexpected error: {ex.Message}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                }
                finally
                {
                    // Terminate the launched process
                    if (launchedProcess != null && !launchedProcess.HasExited)
                    {
                        try
                        {
                            Console.WriteLine("Terminating launched process...");
                            launchedProcess.Kill();
                            launchedProcess.WaitForExit(5000); // Wait up to 5 seconds for clean exit
                            Console.WriteLine("Process terminated.");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: Could not terminate process cleanly: {ex.Message}");
                        }
                    }
                }
            }, executableOption);

            // Add commands to inspect command
            inspectCommand.AddCommand(globalCommand);
            inspectCommand.AddCommand(harnessCommand);

            // Invoke the root command with the provided arguments
            return await rootCommand.InvokeAsync(args);
        }
    }
}

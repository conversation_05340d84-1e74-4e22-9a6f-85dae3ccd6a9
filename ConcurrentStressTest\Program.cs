﻿using System.Collections.Concurrent;
using System.Diagnostics;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Core.Interfaces;
using CognitiveDrive.Infrastructure;
using CognitiveDrive.Infrastructure.Interfaces;

/// <summary>
/// THE CRUCIBLE - Suite 3: Multi-Target Concurrent Stress Test
/// This test validates performance and thread-safety under parallel workload.
/// </summary>
class ConcurrentStressTest
{
    private static readonly ConcurrentBag<ScanResult> _results = new();

    static async Task Main(string[] args)
    {
        Console.WriteLine("🎯 THE CRUCIBLE - SUITE 3: MULTI-TARGET CONCURRENT STRESS TEST");
        Console.WriteLine("===============================================================");
        Console.WriteLine("Testing concurrent scanning of multiple applications...\n");

        // Target applications for concurrent scanning
        var targetApps = new[]
        {
            ("Microsoft Edge", "msedge"),
            ("Google Chrome", "chrome"),
            ("Visual Studio Code", "Code"),
            ("Windows Explorer", "explorer")
        };

        // Initialize scanner components
        var configuration = new ScannerConfiguration
        {
            MaxScanDepth = 15,
            MaxElementsPerScan = 1000,
            PropertyExtractionTimeout = TimeSpan.FromSeconds(2),
            ChildEnumerationTimeout = TimeSpan.FromSeconds(10)
        };

        var propertyExtractor = new WindowsUiaPropertyExtractor(configuration);
        var processManager = new WindowsProcessManager();
        var scanner = new WindowsUiaScanner(propertyExtractor, processManager, configuration);

        Console.WriteLine("🚀 Discovering available processes...");
        var availableProcesses = await scanner.GetAvailableProcessesAsync();

        if (!availableProcesses.IsSuccess)
        {
            Console.WriteLine($"❌ Failed to get available processes: {availableProcesses.ErrorMessage}");
            return;
        }

        // Find target processes that are actually running
        var targetProcesses = new List<(string name, ProcessUiInfo process)>();

        foreach (var (appName, processName) in targetApps)
        {
            var matchingProcess = availableProcesses.Value!.FirstOrDefault(p =>
                p.ProcessName.Contains(processName, StringComparison.OrdinalIgnoreCase));

            if (matchingProcess != null)
            {
                targetProcesses.Add((appName, matchingProcess));
                Console.WriteLine($"✅ Found {appName}: PID {matchingProcess.ProcessId}");
            }
            else
            {
                Console.WriteLine($"⚠️ {appName} not running, skipping");
            }
        }

        if (targetProcesses.Count < 2)
        {
            Console.WriteLine("❌ Need at least 2 running applications for concurrent test");
            return;
        }

        Console.WriteLine($"\n🎯 Starting concurrent scan of {targetProcesses.Count} applications...");

        // Record system state before test
        var initialMemory = GC.GetTotalMemory(false);
        var overallStopwatch = Stopwatch.StartNew();

        // Create concurrent tasks for each target process
        var scanTasks = targetProcesses.Select(async (target, index) =>
        {
            var (appName, processInfo) = target;
            var taskStopwatch = Stopwatch.StartNew();

            Console.WriteLine($"🔍 [{index + 1}] Starting scan of {appName} (PID: {processInfo.ProcessId})");

            try
            {
                var scanResult = await scanner.ScanProcessAsync(processInfo.ProcessId);
                taskStopwatch.Stop();

                var result = new ScanResult
                {
                    AppName = appName,
                    ProcessId = processInfo.ProcessId,
                    StartTime = overallStopwatch.Elapsed - taskStopwatch.Elapsed,
                    EndTime = overallStopwatch.Elapsed,
                    Duration = taskStopwatch.Elapsed,
                    Success = scanResult.IsSuccess,
                    ElementCount = scanResult.IsSuccess ? scanResult.Value!.TotalDescendantCount + 1 : 0,
                    ErrorMessage = scanResult.ErrorMessage,
                    TaskId = index + 1
                };

                _results.Add(result);

                if (scanResult.IsSuccess)
                {
                    Console.WriteLine($"✅ [{index + 1}] {appName} completed: {result.ElementCount} elements in {result.Duration.TotalMilliseconds:F0}ms");
                }
                else
                {
                    Console.WriteLine($"❌ [{index + 1}] {appName} failed: {scanResult.ErrorMessage}");
                }

                return result;
            }
            catch (Exception ex)
            {
                taskStopwatch.Stop();
                var result = new ScanResult
                {
                    AppName = appName,
                    ProcessId = processInfo.ProcessId,
                    StartTime = overallStopwatch.Elapsed - taskStopwatch.Elapsed,
                    EndTime = overallStopwatch.Elapsed,
                    Duration = taskStopwatch.Elapsed,
                    Success = false,
                    ElementCount = 0,
                    ErrorMessage = ex.Message,
                    TaskId = index + 1
                };

                _results.Add(result);
                Console.WriteLine($"❌ [{index + 1}] {appName} exception: {ex.Message}");
                return result;
            }
        }).ToArray();

        // Wait for all concurrent scans to complete
        var results = await Task.WhenAll(scanTasks);
        overallStopwatch.Stop();

        // Performance verdict
        Console.WriteLine($"\n🎯 CONCURRENT STRESS TEST VERDICT:");
        var successfulScans = results.Where(r => r.Success).ToArray();
        var successRate = (double)successfulScans.Length / results.Length;

        Console.WriteLine($"✅ PASSED: Concurrent scanning completed!");
        Console.WriteLine($"   - Applications tested: {results.Length}");
        Console.WriteLine($"   - Success rate: {successRate * 100:F1}%");
        Console.WriteLine($"   - Total duration: {overallStopwatch.Elapsed.TotalSeconds:F2}s");

        if (successfulScans.Length > 0)
        {
            var totalElements = successfulScans.Sum(r => r.ElementCount);
            Console.WriteLine($"   - Total elements: {totalElements}");
        }

        scanner.Dispose();
    }
}

public class ScanResult
{
    public string AppName { get; set; } = string.Empty;
    public int ProcessId { get; set; }
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public bool Success { get; set; }
    public int ElementCount { get; set; }
    public string? ErrorMessage { get; set; }
    public int TaskId { get; set; }
}

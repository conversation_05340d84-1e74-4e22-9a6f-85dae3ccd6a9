{"GeneratedCodeFiles": [], "GeneratedXamlFiles": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\App.xaml", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\MainWindow.xaml"], "GeneratedXbfFiles": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\App.xbf", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\MainWindow.xbf"], "GeneratedXamlPagesFiles": [], "MSBuildLogEntries": [{"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 23:49:20:  90 perfXC_StartPass2, CognitiveDrive.Chariot"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 23:49:20: 105 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 23:49:20: 168 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 23:49:20: 175 perfXC_RestoredGeneratedPass2CodeFileBackup"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 23:49:20: 179 perfXC_RestoredTypeInfoBackup"}]}
{"GeneratedCodeFiles": [], "GeneratedXamlFiles": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\App.xaml", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\MainWindow.xaml"], "GeneratedXbfFiles": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\App.xbf", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Chariot\\obj\\Debug\\net8.0-windows10.0.19041.0\\MainWindow.xbf"], "GeneratedXamlPagesFiles": [], "MSBuildLogEntries": [{"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 00:24:39: 115 perfXC_StartPass2, CognitiveDrive.Chariot"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 00:24:39: 171 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 00:24:39: 404 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 00:24:39: 429 perfXC_RestoredGeneratedPass2CodeFileBackup"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 00:24:39: 444 perfXC_RestoredTypeInfoBackup"}]}
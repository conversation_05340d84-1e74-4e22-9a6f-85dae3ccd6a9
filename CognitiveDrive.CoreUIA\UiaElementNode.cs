using System.Text.Json.Serialization;
using System.Windows;

namespace CognitiveDrive.CoreUIA
{
    /// <summary>
    /// Represents a structured, serializable UI element node in the Hierarchical Element Tree (HET).
    /// This class is designed for JSON serialization and forms the foundation of the structured UI data model.
    /// </summary>
    public class UiaElementNode
    {
        /// <summary>
        /// Gets or sets the name of the UI element.
        /// This typically corresponds to the display name or accessible name of the element.
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the programmatic name of the control type.
        /// Examples include "ControlType.Button", "ControlType.Window", "ControlType.Edit", etc.
        /// </summary>
        [JsonPropertyName("controlType")]
        public string ControlType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the unique Automation ID of the element.
        /// This is used by automation tools to uniquely identify UI elements.
        /// May be empty if no automation ID is assigned to the element.
        /// </summary>
        [Json<PERSON>ropertyName("automationId")]
        public string AutomationId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets a value indicating whether the UI element is enabled.
        /// Disabled elements typically cannot be interacted with by users.
        /// </summary>
        [JsonPropertyName("isEnabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether the UI element is offscreen.
        /// Offscreen elements are not visible to users but may still be part of the UI tree.
        /// </summary>
        [JsonPropertyName("isOffscreen")]
        public bool IsOffscreen { get; set; } = false;

        /// <summary>
        /// Gets or sets the bounding rectangle of the UI element.
        /// This represents the physical location and size of the element on screen.
        /// Can be null for virtualized UI elements that do not have a physical location,
        /// such as certain data items in virtualized lists or offscreen elements.
        /// </summary>
        [JsonPropertyName("boundingRectangle")]
        public Rect? BoundingRectangle { get; set; }

        /// <summary>
        /// Gets or sets the list of child nodes that form the hierarchical structure.
        /// Each child represents a UI element that is contained within this element.
        /// This property enables the tree-like structure of the UI element hierarchy.
        /// </summary>
        [JsonPropertyName("children")]
        public List<UiaElementNode> Children { get; set; } = new List<UiaElementNode>();

        /// <summary>
        /// Initializes a new instance of the UiaElementNode class.
        /// </summary>
        public UiaElementNode()
        {
        }

        /// <summary>
        /// Gets the total count of all descendant nodes (children, grandchildren, etc.).
        /// This is useful for understanding the complexity of the UI tree.
        /// </summary>
        [JsonIgnore]
        public int TotalDescendantCount
        {
            get
            {
                int count = Children.Count;
                foreach (var child in Children)
                {
                    count += child.TotalDescendantCount;
                }
                return count;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this node has a physical location on screen.
        /// Returns true if BoundingRectangle is not null and not empty.
        /// </summary>
        [JsonIgnore]
        public bool HasPhysicalLocation => BoundingRectangle.HasValue && !BoundingRectangle.Value.IsEmpty;

        /// <summary>
        /// Returns a string representation of this UI element node for debugging purposes.
        /// </summary>
        /// <returns>A string containing the key properties of this node.</returns>
        public override string ToString()
        {
            var location = HasPhysicalLocation ? $"at {BoundingRectangle}" : "no physical location";
            return $"{ControlType} '{Name}' (ID: {AutomationId}) - {location} - {Children.Count} children";
        }
    }
}

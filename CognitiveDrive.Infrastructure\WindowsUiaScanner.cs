using System.Diagnostics;
using System.Windows.Automation;
using CognitiveDrive.Core.Interfaces;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Infrastructure.Interfaces;

namespace CognitiveDrive.Infrastructure;

/// <summary>
/// Production-grade Windows UI Automation scanner implementation.
/// Provides robust, high-performance UI element discovery with comprehensive error handling,
/// performance optimization, and resource management.
/// </summary>
public sealed class WindowsUiaScanner : IUiaScanner, IDisposable
{
    private readonly IUiaPropertyExtractor _propertyExtractor;
    private readonly IProcessManager _processManager;
    private readonly ScannerConfiguration _configuration;
    private readonly ScannerStatistics _statistics;
    private bool _disposed;

    public WindowsUiaScanner(
        IUiaPropertyExtractor propertyExtractor,
        IProcessManager processManager,
        ScannerConfiguration configuration)
    {
        _propertyExtractor = propertyExtractor ?? throw new ArgumentNullException(nameof(propertyExtractor));
        _processManager = processManager ?? throw new ArgumentNullException(nameof(processManager));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _statistics = new ScannerStatistics();
    }

    /// <summary>
    /// Gets the scanner performance statistics.
    /// </summary>
    public ScannerStatistics Statistics => _statistics;

    public async Task<ScanResult<UiaElementNode>> ScanProcessAsync(int processId, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        return await ScanResult<UiaElementNode>.ExecuteAsync(async () =>
        {
            _statistics.IncrementScanAttempts();

            var processInfo = await _processManager.GetProcessInfoAsync(processId, cancellationToken);
            if (processInfo == null)
            {
                throw new ArgumentException($"Process with ID {processId} not found or has no UI", nameof(processId));
            }

            if (!processInfo.HasVisibleUi)
            {
                throw new InvalidOperationException($"Process {processId} ({processInfo.ProcessName}) has no visible UI");
            }

            var rootElement = AutomationElement.FromHandle(processInfo.MainWindowHandle);
            if (rootElement == null)
            {
                throw new InvalidOperationException($"Could not get automation element for process {processId}");
            }

            var result = await BuildElementTreeAsync(rootElement, processId, cancellationToken);
            _statistics.IncrementSuccessfulScans();
            _statistics.AddElementCount(result.TotalDescendantCount + 1);

            return result;
        });
    }

    public async Task<ScanResult<UiaElementNode>> ScanProcessByNameAsync(string processName, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        if (string.IsNullOrWhiteSpace(processName))
            throw new ArgumentException("Process name cannot be null or empty", nameof(processName));

        var processes = await _processManager.FindProcessesByNameAsync(processName, cancellationToken);
        if (!processes.Any())
        {
            return ScanResult<UiaElementNode>.Failure($"No processes found with name '{processName}'", TimeSpan.Zero);
        }

        var processWithUi = processes.FirstOrDefault(p => p.HasVisibleUi);
        if (processWithUi == null)
        {
            return ScanResult<UiaElementNode>.Failure($"No processes with name '{processName}' have visible UI", TimeSpan.Zero);
        }

        return await ScanProcessAsync(processWithUi.ProcessId, cancellationToken);
    }

    public async Task<ScanResult<UiaElementNode>> ScanWindowAsync(IntPtr windowHandle, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        if (windowHandle == IntPtr.Zero)
            throw new ArgumentException("Window handle cannot be zero", nameof(windowHandle));

        return await ScanResult<UiaElementNode>.ExecuteAsync(async () =>
        {
            var rootElement = AutomationElement.FromHandle(windowHandle);
            if (rootElement == null)
            {
                throw new InvalidOperationException($"Could not get automation element for window handle {windowHandle}");
            }

            var processId = await _processManager.GetProcessIdFromWindowAsync(windowHandle, cancellationToken);
            return await BuildElementTreeAsync(rootElement, processId, cancellationToken);
        });
    }

    public async Task<ScanResult<ProcessUiInfo>> GetProcessUiInfoAsync(int processId, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        return await ScanResult<ProcessUiInfo>.ExecuteAsync(async () =>
        {
            var processInfo = await _processManager.GetProcessInfoAsync(processId, cancellationToken);
            if (processInfo == null)
            {
                throw new ArgumentException($"Process with ID {processId} not found", nameof(processId));
            }

            return processInfo;
        });
    }

    public async Task<ScanResult<IReadOnlyList<ProcessUiInfo>>> GetAvailableProcessesAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        return await ScanResult<IReadOnlyList<ProcessUiInfo>>.ExecuteAsync(async () =>
        {
            var processes = await _processManager.GetAllProcessesWithUiAsync(cancellationToken);
            return (IReadOnlyList<ProcessUiInfo>)processes.ToList();
        });
    }

    private async Task<UiaElementNode> BuildElementTreeAsync(AutomationElement rootElement, int processId, CancellationToken cancellationToken)
    {
        var rootNode = await CreateElementNodeAsync(rootElement, processId, cancellationToken);
        
        // Use iterative approach to prevent stack overflow on deep UI trees
        var processingQueue = new Queue<(AutomationElement element, UiaElementNode parentNode, int depth)>();
        processingQueue.Enqueue((rootElement, rootNode, 0));

        var walker = TreeWalker.RawViewWalker;
        var processedElements = 0;

        while (processingQueue.Count > 0 && !cancellationToken.IsCancellationRequested)
        {
            var (currentElement, currentNode, depth) = processingQueue.Dequeue();

            // Respect maximum depth configuration
            if (depth >= _configuration.MaxScanDepth)
                continue;

            // Respect maximum elements configuration
            if (processedElements >= _configuration.MaxElementsPerScan)
                break;

            try
            {
                var children = await GetChildElementsAsync(walker, currentElement, cancellationToken);
                
                foreach (var child in children)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        var childNode = await CreateElementNodeAsync(child, processId, cancellationToken);
                        currentNode.Children.Add(childNode);
                        
                        // Queue child for processing its descendants
                        processingQueue.Enqueue((child, childNode, depth + 1));
                        processedElements++;
                    }
                    catch (ElementNotAvailableException)
                    {
                        // Element became unavailable, skip it
                        _statistics.IncrementSkippedElements();
                    }
                    catch (Exception ex)
                    {
                        // Create error node for problematic elements
                        var errorNode = CreateErrorNode(ex, processId);
                        currentNode.Children.Add(errorNode);
                        _statistics.IncrementErrorElements();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but continue processing
                var errorNode = CreateErrorNode(ex, processId);
                currentNode.Children.Add(errorNode);
                _statistics.IncrementErrorElements();
            }
        }

        return rootNode;
    }

    private async Task<UiaElementNode> CreateElementNodeAsync(AutomationElement element, int processId, CancellationToken cancellationToken)
    {
        var node = new UiaElementNode
        {
            ProcessId = processId,
            CapturedAt = DateTimeOffset.UtcNow
        };

        // Extract properties safely with timeout
        using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        timeoutCts.CancelAfter(_configuration.PropertyExtractionTimeout);

        var nameResult = await _propertyExtractor.GetNameAsync(element, timeoutCts.Token);
        node.Name = nameResult.IsSuccess ? nameResult.Value ?? string.Empty : string.Empty;

        var controlTypeResult = await _propertyExtractor.GetControlTypeAsync(element, timeoutCts.Token);
        node.ControlType = controlTypeResult.IsSuccess ? controlTypeResult.Value ?? "ControlType.Custom" : "ControlType.Custom";

        var automationIdResult = await _propertyExtractor.GetAutomationIdAsync(element, timeoutCts.Token);
        node.AutomationId = automationIdResult.IsSuccess ? automationIdResult.Value ?? string.Empty : string.Empty;

        var isEnabledResult = await _propertyExtractor.GetIsEnabledAsync(element, timeoutCts.Token);
        node.IsEnabled = isEnabledResult.IsSuccess && isEnabledResult.Value;

        var isOffscreenResult = await _propertyExtractor.GetIsOffscreenAsync(element, timeoutCts.Token);
        node.IsOffscreen = isOffscreenResult.IsSuccess && isOffscreenResult.Value;

        var boundsResult = await _propertyExtractor.GetBoundingRectangleAsync(element, timeoutCts.Token);
        if (boundsResult.IsSuccess && boundsResult.Value.HasValue)
        {
            var rect = boundsResult.Value.Value;
            if (!rect.IsEmpty && rect.Width > 0 && rect.Height > 0)
            {
                node.BoundingRectangle = new ElementBounds
                {
                    X = rect.X,
                    Y = rect.Y,
                    Width = rect.Width,
                    Height = rect.Height
                };
            }
        }

        return node;
    }

    private async Task<List<AutomationElement>> GetChildElementsAsync(TreeWalker walker, AutomationElement parent, CancellationToken cancellationToken)
    {
        return await Task.Run(() =>
        {
            var children = new List<AutomationElement>();
            
            try
            {
                var child = walker.GetFirstChild(parent);
                while (child != null && !cancellationToken.IsCancellationRequested)
                {
                    children.Add(child);
                    try
                    {
                        child = walker.GetNextSibling(child);
                    }
                    catch (ElementNotAvailableException)
                    {
                        break;
                    }
                }
            }
            catch (ElementNotAvailableException)
            {
                // Parent element became unavailable
            }

            return children;
        }, cancellationToken);
    }

    private static UiaElementNode CreateErrorNode(Exception exception, int processId)
    {
        return new UiaElementNode
        {
            Name = "<Error>",
            ControlType = "ControlType.Custom",
            AutomationId = $"<Error: {exception.GetType().Name}>",
            IsEnabled = false,
            IsOffscreen = true,
            ProcessId = processId,
            CapturedAt = DateTimeOffset.UtcNow,
            Properties = new Dictionary<string, object>
            {
                ["ErrorMessage"] = exception.Message,
                ["ErrorType"] = exception.GetType().FullName ?? "Unknown"
            }
        };
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WindowsUiaScanner));
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
        }
    }
}

using System.Windows;
using System.Windows.Automation;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Core.Interfaces;

namespace CognitiveDrive.Infrastructure.Interfaces;

/// <summary>
/// Defines the contract for safely extracting properties from UI Automation elements.
/// Provides timeout-based, resilient property extraction with comprehensive error handling.
/// </summary>
public interface IUiaPropertyExtractor
{
    /// <summary>
    /// Safely extracts the name property from a UI element.
    /// </summary>
    /// <param name="element">The UI element</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The element name or null if not available</returns>
    Task<ScanResult<string?>> GetNameAsync(AutomationElement element, CancellationToken cancellationToken = default);

    /// <summary>
    /// Safely extracts the control type from a UI element.
    /// </summary>
    /// <param name="element">The UI element</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The control type string</returns>
    Task<ScanResult<string?>> GetControlTypeAsync(AutomationElement element, CancellationToken cancellationToken = default);

    /// <summary>
    /// Safely extracts the automation ID from a UI element.
    /// </summary>
    /// <param name="element">The UI element</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The automation ID or null if not available</returns>
    Task<ScanResult<string?>> GetAutomationIdAsync(AutomationElement element, CancellationToken cancellationToken = default);

    /// <summary>
    /// Safely extracts the enabled state from a UI element.
    /// </summary>
    /// <param name="element">The UI element</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the element is enabled</returns>
    Task<ScanResult<bool>> GetIsEnabledAsync(AutomationElement element, CancellationToken cancellationToken = default);

    /// <summary>
    /// Safely extracts the offscreen state from a UI element.
    /// </summary>
    /// <param name="element">The UI element</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the element is offscreen</returns>
    Task<ScanResult<bool>> GetIsOffscreenAsync(AutomationElement element, CancellationToken cancellationToken = default);

    /// <summary>
    /// Safely extracts the bounding rectangle from a UI element.
    /// </summary>
    /// <param name="element">The UI element</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The bounding rectangle or null if not available</returns>
    Task<ScanResult<Rect?>> GetBoundingRectangleAsync(AutomationElement element, CancellationToken cancellationToken = default);

    /// <summary>
    /// Safely extracts a custom property from a UI element.
    /// </summary>
    /// <param name="element">The UI element</param>
    /// <param name="property">The automation property to extract</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The property value or null if not available</returns>
    Task<ScanResult<object?>> GetPropertyAsync(AutomationElement element, AutomationProperty property, CancellationToken cancellationToken = default);
}

/// <summary>
/// Defines the contract for managing process information and discovery.
/// </summary>
public interface IProcessManager
{
    /// <summary>
    /// Gets detailed information about a specific process.
    /// </summary>
    /// <param name="processId">The process ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Process UI information or null if not found</returns>
    Task<ProcessUiInfo?> GetProcessInfoAsync(int processId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Finds all processes with the specified name.
    /// </summary>
    /// <param name="processName">The process name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching processes</returns>
    Task<IReadOnlyList<ProcessUiInfo>> FindProcessesByNameAsync(string processName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all processes that have visible UI elements.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of processes with UI</returns>
    Task<IReadOnlyList<ProcessUiInfo>> GetAllProcessesWithUiAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the process ID that owns the specified window.
    /// </summary>
    /// <param name="windowHandle">The window handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The process ID</returns>
    Task<int> GetProcessIdFromWindowAsync(IntPtr windowHandle, CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration settings for the UI scanner.
/// </summary>
public sealed class ScannerConfiguration
{
    /// <summary>
    /// Gets or sets the maximum depth to scan in the UI tree.
    /// </summary>
    public int MaxScanDepth { get; set; } = 50;

    /// <summary>
    /// Gets or sets the maximum number of elements to scan per operation.
    /// </summary>
    public int MaxElementsPerScan { get; set; } = 10000;

    /// <summary>
    /// Gets or sets the timeout for property extraction operations.
    /// </summary>
    public TimeSpan PropertyExtractionTimeout { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Gets or sets the timeout for child element enumeration.
    /// </summary>
    public TimeSpan ChildEnumerationTimeout { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// Gets or sets a value indicating whether to include disabled elements.
    /// </summary>
    public bool IncludeDisabledElements { get; set; } = true;

    /// <summary>
    /// Gets or sets a value indicating whether to include offscreen elements.
    /// </summary>
    public bool IncludeOffscreenElements { get; set; } = true;

    /// <summary>
    /// Gets or sets the retry count for transient failures.
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// Gets or sets the delay between retries.
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMilliseconds(100);
}

/// <summary>
/// Performance and operational statistics for the scanner.
/// </summary>
public sealed class ScannerStatistics
{
    private long _scanAttempts;
    private long _successfulScans;
    private long _skippedElements;
    private long _errorElements;
    private long _totalElementsScanned;

    /// <summary>
    /// Gets the total number of scan attempts.
    /// </summary>
    public long ScanAttempts => _scanAttempts;

    /// <summary>
    /// Gets the number of successful scans.
    /// </summary>
    public long SuccessfulScans => _successfulScans;

    /// <summary>
    /// Gets the number of skipped elements.
    /// </summary>
    public long SkippedElements => _skippedElements;

    /// <summary>
    /// Gets the number of error elements.
    /// </summary>
    public long ErrorElements => _errorElements;

    /// <summary>
    /// Gets the total number of elements scanned.
    /// </summary>
    public long TotalElementsScanned => _totalElementsScanned;

    /// <summary>
    /// Gets the success rate as a percentage.
    /// </summary>
    public double SuccessRate => _scanAttempts > 0 ? (double)_successfulScans / _scanAttempts * 100 : 0;

    /// <summary>
    /// Gets the average elements per successful scan.
    /// </summary>
    public double AverageElementsPerScan => _successfulScans > 0 ? (double)_totalElementsScanned / _successfulScans : 0;

    public void IncrementScanAttempts() => Interlocked.Increment(ref _scanAttempts);
    public void IncrementSuccessfulScans() => Interlocked.Increment(ref _successfulScans);
    public void IncrementSkippedElements() => Interlocked.Increment(ref _skippedElements);
    public void IncrementErrorElements() => Interlocked.Increment(ref _errorElements);
    public void AddElementCount(long count) => Interlocked.Add(ref _totalElementsScanned, count);

    public override string ToString()
    {
        return $"Scanner Stats: {_scanAttempts} attempts, {SuccessRate:F1}% success, {AverageElementsPerScan:F0} avg elements/scan";
    }
}

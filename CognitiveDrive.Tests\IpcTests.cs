using System;
using System.IO.Pipes;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;

namespace CognitiveDrive.Tests
{
    /// <summary>
    /// Integration tests for the Daemon's Named Pipe server to verify resilience
    /// against broken pipe scenarios and proper client handling.
    /// </summary>
    [TestFixture]
    public class IpcTests
    {
        private const string TestPipeName = "CognitiveDriveTestPipe";
        private CancellationTokenSource _serverCancellationTokenSource;
        private Task _serverTask;

        /// <summary>
        /// Sets up a test Named Pipe server before each test.
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            _serverCancellationTokenSource = new CancellationTokenSource();
            _serverTask = Task.Run(() => RunTestNamedPipeServer(_serverCancellationTokenSource.Token));
            
            // Give the server a moment to start
            Thread.Sleep(100);
        }

        /// <summary>
        /// Cleans up the test server after each test.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            _serverCancellationTokenSource?.Cancel();
            _serverTask?.Wait(TimeSpan.FromSeconds(5));
            _serverCancellationTokenSource?.Dispose();
        }

        /// <summary>
        /// Tests that the server can handle multiple clients connecting and disconnecting properly.
        /// </summary>
        [Test]
        public async Task NamedPipeServer_MultipleClients_HandlesConnectionsProperly()
        {
            // Act: Connect multiple clients sequentially
            var client1Result = await ConnectAndReadFromServer();
            var client2Result = await ConnectAndReadFromServer();
            var client3Result = await ConnectAndReadFromServer();

            // Assert: All clients should connect successfully
            Assert.That(client1Result, Is.True, "First client should connect successfully");
            Assert.That(client2Result, Is.True, "Second client should connect successfully");
            Assert.That(client3Result, Is.True, "Third client should connect successfully");
        }

        /// <summary>
        /// Tests that the server can handle a client that connects, reads, and then immediately
        /// disposes without a clean shutdown, and that a second client can connect immediately after.
        /// </summary>
        [Test]
        public async Task NamedPipeServer_BrokenPipeRecovery_AllowsNewConnections()
        {
            // Act: First client connects and terminates abruptly
            var brokenClientTask = Task.Run(async () =>
            {
                try
                {
                    using (var pipeClient = new NamedPipeClientStream(".", TestPipeName, PipeDirection.In))
                    {
                        await pipeClient.ConnectAsync(5000);
                        
                        // Start reading but don't complete it
                        var buffer = new byte[1024];
                        var readTask = pipeClient.ReadAsync(buffer, 0, buffer.Length);
                        
                        // Wait a short time then dispose abruptly (simulating crash)
                        await Task.Delay(100);
                        
                        // The using statement will dispose the pipe without clean shutdown
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            });

            var brokenClientResult = await brokenClientTask;
            Assert.That(brokenClientResult, Is.True, "Broken client should connect initially");

            // Give the server time to detect the broken pipe and clean up
            await Task.Delay(500);

            // Act: Second client connects immediately after the first one crashed
            var recoveryClientResult = await ConnectAndReadFromServer();

            // Assert: Second client should connect successfully despite the broken pipe
            Assert.That(recoveryClientResult, Is.True, 
                "Server should accept new connections after handling broken pipe");
        }

        /// <summary>
        /// Tests that the server can handle rapid connect/disconnect cycles without hanging.
        /// </summary>
        [Test]
        public async Task NamedPipeServer_RapidConnectDisconnect_RemainsStable()
        {
            // Act: Perform rapid connect/disconnect cycles
            var tasks = new Task<bool>[5];
            for (int i = 0; i < tasks.Length; i++)
            {
                tasks[i] = Task.Run(async () =>
                {
                    try
                    {
                        using (var pipeClient = new NamedPipeClientStream(".", TestPipeName, PipeDirection.In))
                        {
                            await pipeClient.ConnectAsync(2000);
                            
                            // Read a small amount of data
                            var buffer = new byte[100];
                            await pipeClient.ReadAsync(buffer, 0, buffer.Length);
                            
                            // Disconnect immediately
                            return true;
                        }
                    }
                    catch
                    {
                        return false;
                    }
                });
            }

            var results = await Task.WhenAll(tasks);

            // Assert: All rapid connections should succeed
            foreach (var result in results)
            {
                Assert.That(result, Is.True, "Rapid connect/disconnect should succeed");
            }
        }

        /// <summary>
        /// Tests that the server properly handles client timeout scenarios.
        /// </summary>
        [Test]
        public async Task NamedPipeServer_ClientTimeout_HandlesGracefully()
        {
            // Act: Connect but don't read data (simulating unresponsive client)
            var timeoutTask = Task.Run(async () =>
            {
                try
                {
                    using (var pipeClient = new NamedPipeClientStream(".", TestPipeName, PipeDirection.In))
                    {
                        await pipeClient.ConnectAsync(5000);
                        
                        // Connect but don't read - just wait
                        await Task.Delay(2000);
                        
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            });

            var timeoutResult = await timeoutTask;
            Assert.That(timeoutResult, Is.True, "Timeout client should connect");

            // Verify server can still accept new connections after timeout client
            var newClientResult = await ConnectAndReadFromServer();
            Assert.That(newClientResult, Is.True, "New client should connect after timeout scenario");
        }

        /// <summary>
        /// Helper method to connect to the test server and read data.
        /// </summary>
        /// <returns>True if connection and read were successful, false otherwise</returns>
        private async Task<bool> ConnectAndReadFromServer()
        {
            try
            {
                using (var pipeClient = new NamedPipeClientStream(".", TestPipeName, PipeDirection.In))
                {
                    await pipeClient.ConnectAsync(5000);
                    
                    var buffer = new byte[1024];
                    int bytesRead = await pipeClient.ReadAsync(buffer, 0, buffer.Length);
                    
                    return bytesRead > 0;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Runs a test Named Pipe server that mimics the daemon's behavior.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token to stop the server</param>
        private async Task RunTestNamedPipeServer(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var pipeServer = new NamedPipeServerStream(TestPipeName, PipeDirection.Out,
                        NamedPipeServerStream.MaxAllowedServerInstances, PipeTransmissionMode.Message);

                    await pipeServer.WaitForConnectionAsync(cancellationToken);

                    // Handle client in separate task (same pattern as the refactored daemon)
                    _ = Task.Run(() => HandleTestClient(pipeServer, cancellationToken));
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch
                {
                    // Continue on errors to maintain server availability
                    await Task.Delay(100, cancellationToken);
                }
            }
        }

        /// <summary>
        /// Handles a test client connection with proper cleanup.
        /// </summary>
        /// <param name="pipeServer">The pipe server stream</param>
        /// <param name="cancellationToken">Cancellation token</param>
        private async Task HandleTestClient(NamedPipeServerStream pipeServer, CancellationToken cancellationToken)
        {
            try
            {
                // Send test data to client
                var testData = Encoding.UTF8.GetBytes("Test HET data from server");
                await pipeServer.WriteAsync(testData, 0, testData.Length);
                await pipeServer.FlushAsync();

                // Keep connection alive briefly
                while (pipeServer.IsConnected && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(100, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected during shutdown
            }
            catch
            {
                // Handle any client-specific errors
            }
            finally
            {
                try
                {
                    if (pipeServer.IsConnected)
                    {
                        pipeServer.Disconnect();
                    }
                    pipeServer.Dispose();
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }
    }
}

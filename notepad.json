{"name": "Untitled - Notepad", "controlType": "ControlType.Window", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 892, "y": 246}, "size": {"isEmpty": false, "width": 679, "height": 440}, "x": 892, "y": 246, "width": 679, "height": 440, "left": 892, "top": 246, "right": 1571, "bottom": 686, "topLeft": {"x": 892, "y": 246}, "topRight": {"x": 1571, "y": 246}, "bottomLeft": {"x": 892, "y": 686}, "bottomRight": {"x": 1571, "y": 686}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 339}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 900, "y": 339, "width": 663, "height": 299, "left": 900, "top": 339, "right": 1563, "bottom": 638, "topLeft": {"x": 900, "y": 339}, "topRight": {"x": 1563, "y": 339}, "bottomLeft": {"x": 900, "y": 638}, "bottomRight": {"x": 1563, "y": 638}}, "children": [{"name": "Text editor", "controlType": "ControlType.Document", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 339}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 900, "y": 339, "width": 663, "height": 299, "left": 900, "top": 339, "right": 1563, "bottom": 638, "topLeft": {"x": 900, "y": 339}, "topRight": {"x": 1563, "y": 339}, "bottomLeft": {"x": 900, "y": 638}, "bottomRight": {"x": 1563, "y": 638}}, "children": []}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 950, "y": 258}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 950, "y": 258, "width": 359, "height": 40, "left": 950, "top": 258, "right": 1309, "bottom": 298, "topLeft": {"x": 950, "y": 258}, "topRight": {"x": 1309, "y": 258}, "bottomLeft": {"x": 950, "y": 298}, "bottomRight": {"x": 1309, "y": 298}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 950, "y": 258}, "size": {"isEmpty": false, "width": 287, "height": 32}, "x": 950, "y": 258, "width": 287, "height": 32, "left": 950, "top": 258, "right": 1237, "bottom": 290, "topLeft": {"x": 950, "y": 258}, "topRight": {"x": 1237, "y": 258}, "bottomLeft": {"x": 950, "y": 290}, "bottomRight": {"x": 1237, "y": 290}}, "children": [{"name": "", "controlType": "ControlType.Tab", "automationId": "Tabs", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 950, "y": 258}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 950, "y": 258, "width": 359, "height": 40, "left": 950, "top": 258, "right": 1309, "bottom": 298, "topLeft": {"x": 950, "y": 258}, "topRight": {"x": 1309, "y": 258}, "bottomLeft": {"x": 950, "y": 298}, "bottomRight": {"x": 1309, "y": 298}}, "children": [{"name": "", "controlType": "ControlType.List", "automationId": "TabListView", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 952, "y": 258}, "size": {"isEmpty": false, "width": 312, "height": 40}, "x": 952, "y": 258, "width": 312, "height": 40, "left": 952, "top": 258, "right": 1264, "bottom": 298, "topLeft": {"x": 952, "y": 258}, "topRight": {"x": 1264, "y": 258}, "bottomLeft": {"x": 952, "y": 298}, "bottomRight": {"x": 1264, "y": 298}}, "children": [{"name": "Untitled. Unmodified.", "controlType": "ControlType.TabItem", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 955, "y": 258}, "size": {"isEmpty": false, "width": 307, "height": 40}, "x": 955, "y": 258, "width": 307, "height": 40, "left": 955, "top": 258, "right": 1262, "bottom": 298, "topLeft": {"x": 955, "y": 258}, "topRight": {"x": 1262, "y": 258}, "bottomLeft": {"x": 955, "y": 298}, "bottomRight": {"x": 1262, "y": 298}}, "children": [{"name": "Untitled", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 971, "y": 268}, "size": {"isEmpty": false, "width": 56, "height": 19}, "x": 971, "y": 268, "width": 56, "height": 19, "left": 971, "top": 268, "right": 1027, "bottom": 287, "topLeft": {"x": 971, "y": 268}, "topRight": {"x": 1027, "y": 268}, "bottomLeft": {"x": 971, "y": 287}, "bottomRight": {"x": 1027, "y": 287}}, "children": []}, {"name": "Close Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "CloseButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1211, "y": 263}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 1211, "y": 263, "width": 40, "height": 30, "left": 1211, "top": 263, "right": 1251, "bottom": 293, "topLeft": {"x": 1211, "y": 263}, "topRight": {"x": 1251, "y": 263}, "bottomLeft": {"x": 1211, "y": 293}, "bottomRight": {"x": 1251, "y": 293}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1224, "y": 270}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 1224, "y": 270, "width": 15, "height": 15, "left": 1224, "top": 270, "right": 1239, "bottom": 285, "topLeft": {"x": 1224, "y": 270}, "topRight": {"x": 1239, "y": 270}, "bottomLeft": {"x": 1224, "y": 285}, "bottomRight": {"x": 1239, "y": 285}}, "children": []}]}]}]}, {"name": "Add New Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AddButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1269, "y": 264}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 1269, "y": 264, "width": 40, "height": 30, "left": 1269, "top": 264, "right": 1309, "bottom": 294, "topLeft": {"x": 1269, "y": 264}, "topRight": {"x": 1309, "y": 264}, "bottomLeft": {"x": 1269, "y": 294}, "bottomRight": {"x": 1309, "y": 294}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1281, "y": 271}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 1281, "y": 271, "width": 15, "height": 15, "left": 1281, "top": 271, "right": 1296, "bottom": 286, "topLeft": {"x": 1281, "y": 271}, "topRight": {"x": 1296, "y": 271}, "bottomLeft": {"x": 1281, "y": 286}, "bottomRight": {"x": 1296, "y": 286}}, "children": []}]}]}, {"name": "Notepad automatically saves your progress. All your content will be available the next time you open Notepad.", "controlType": "ControlType.Pane", "automationId": "TeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 298}, "size": {"isEmpty": false, "width": 663, "height": 41}, "x": 900, "y": 298, "width": 663, "height": 41, "left": 900, "top": 298, "right": 1563, "bottom": 339, "topLeft": {"x": 900, "y": 298}, "topRight": {"x": 1563, "y": 298}, "bottomLeft": {"x": 900, "y": 339}, "bottomRight": {"x": 1563, "y": 339}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 298}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 900, "y": 298, "width": 530, "height": 32, "left": 900, "top": 298, "right": 1430, "bottom": 330, "topLeft": {"x": 900, "y": 298}, "topRight": {"x": 1430, "y": 298}, "bottomLeft": {"x": 900, "y": 330}, "bottomRight": {"x": 1430, "y": 330}}, "children": [{"name": "", "controlType": "ControlType.MenuBar", "automationId": "<PERSON><PERSON><PERSON><PERSON>", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 298}, "size": {"isEmpty": false, "width": 199, "height": 40}, "x": 900, "y": 298, "width": 199, "height": 40, "left": 900, "top": 298, "right": 1099, "bottom": 338, "topLeft": {"x": 900, "y": 298}, "topRight": {"x": 1099, "y": 298}, "bottomLeft": {"x": 900, "y": 338}, "bottomRight": {"x": 1099, "y": 338}}, "children": [{"name": "File", "controlType": "ControlType.MenuItem", "automationId": "File", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 905, "y": 298}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 905, "y": 298, "width": 52, "height": 40, "left": 905, "top": 298, "right": 957, "bottom": 338, "topLeft": {"x": 905, "y": 298}, "topRight": {"x": 957, "y": 298}, "bottomLeft": {"x": 905, "y": 338}, "bottomRight": {"x": 957, "y": 338}}, "children": [{"name": "File", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 905, "y": 298}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 905, "y": 298, "width": 52, "height": 40, "left": 905, "top": 298, "right": 957, "bottom": 338, "topLeft": {"x": 905, "y": 298}, "topRight": {"x": 957, "y": 298}, "bottomLeft": {"x": 905, "y": 338}, "bottomRight": {"x": 957, "y": 338}}, "children": [{"name": "File", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 917, "y": 306}, "size": {"isEmpty": false, "width": 27, "height": 24}, "x": 917, "y": 306, "width": 27, "height": 24, "left": 917, "top": 306, "right": 944, "bottom": 330, "topLeft": {"x": 917, "y": 306}, "topRight": {"x": 944, "y": 306}, "bottomLeft": {"x": 917, "y": 330}, "bottomRight": {"x": 944, "y": 330}}, "children": []}]}]}, {"name": "Edit", "controlType": "ControlType.MenuItem", "automationId": "Edit", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 967, "y": 298}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 967, "y": 298, "width": 55, "height": 40, "left": 967, "top": 298, "right": 1022, "bottom": 338, "topLeft": {"x": 967, "y": 298}, "topRight": {"x": 1022, "y": 298}, "bottomLeft": {"x": 967, "y": 338}, "bottomRight": {"x": 1022, "y": 338}}, "children": [{"name": "Edit", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 967, "y": 298}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 967, "y": 298, "width": 55, "height": 40, "left": 967, "top": 298, "right": 1022, "bottom": 338, "topLeft": {"x": 967, "y": 298}, "topRight": {"x": 1022, "y": 298}, "bottomLeft": {"x": 967, "y": 338}, "bottomRight": {"x": 1022, "y": 338}}, "children": [{"name": "Edit", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 980, "y": 306}, "size": {"isEmpty": false, "width": 30, "height": 24}, "x": 980, "y": 306, "width": 30, "height": 24, "left": 980, "top": 306, "right": 1010, "bottom": 330, "topLeft": {"x": 980, "y": 306}, "topRight": {"x": 1010, "y": 306}, "bottomLeft": {"x": 980, "y": 330}, "bottomRight": {"x": 1010, "y": 330}}, "children": []}]}]}, {"name": "View", "controlType": "ControlType.MenuItem", "automationId": "View", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1032, "y": 298}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 1032, "y": 298, "width": 62, "height": 40, "left": 1032, "top": 298, "right": 1094, "bottom": 338, "topLeft": {"x": 1032, "y": 298}, "topRight": {"x": 1094, "y": 298}, "bottomLeft": {"x": 1032, "y": 338}, "bottomRight": {"x": 1094, "y": 338}}, "children": [{"name": "View", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1032, "y": 298}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 1032, "y": 298, "width": 62, "height": 40, "left": 1032, "top": 298, "right": 1094, "bottom": 338, "topLeft": {"x": 1032, "y": 298}, "topRight": {"x": 1094, "y": 298}, "bottomLeft": {"x": 1032, "y": 338}, "bottomRight": {"x": 1094, "y": 338}}, "children": [{"name": "View", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1044, "y": 306}, "size": {"isEmpty": false, "width": 37, "height": 24}, "x": 1044, "y": 306, "width": 37, "height": 24, "left": 1044, "top": 306, "right": 1081, "bottom": 330, "topLeft": {"x": 1044, "y": 306}, "topRight": {"x": 1081, "y": 306}, "bottomLeft": {"x": 1044, "y": 330}, "bottomRight": {"x": 1081, "y": 330}}, "children": []}]}]}]}, {"name": "Headings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1135, "y": 298}, "size": {"isEmpty": false, "width": 71, "height": 40}, "x": 1135, "y": 298, "width": 71, "height": 40, "left": 1135, "top": 298, "right": 1206, "bottom": 338, "topLeft": {"x": 1135, "y": 298}, "topRight": {"x": 1206, "y": 298}, "bottomLeft": {"x": 1135, "y": 338}, "bottomRight": {"x": 1206, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1149, "y": 308}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1149, "y": 308, "width": 18, "height": 18, "left": 1149, "top": 308, "right": 1167, "bottom": 326, "topLeft": {"x": 1149, "y": 308}, "topRight": {"x": 1167, "y": 308}, "bottomLeft": {"x": 1149, "y": 326}, "bottomRight": {"x": 1167, "y": 326}}, "children": []}]}, {"name": "Lists", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1206, "y": 298}, "size": {"isEmpty": false, "width": 70, "height": 40}, "x": 1206, "y": 298, "width": 70, "height": 40, "left": 1206, "top": 298, "right": 1276, "bottom": 338, "topLeft": {"x": 1206, "y": 298}, "topRight": {"x": 1276, "y": 298}, "bottomLeft": {"x": 1206, "y": 338}, "bottomRight": {"x": 1276, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1220, "y": 308}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1220, "y": 308, "width": 18, "height": 18, "left": 1220, "top": 308, "right": 1238, "bottom": 326, "topLeft": {"x": 1220, "y": 308}, "topRight": {"x": 1238, "y": 308}, "bottomLeft": {"x": 1220, "y": 326}, "bottomRight": {"x": 1238, "y": 326}}, "children": []}]}, {"name": "Bold (Ctrl+B)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1276, "y": 298}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 1276, "y": 298, "width": 40, "height": 40, "left": 1276, "top": 298, "right": 1316, "bottom": 338, "topLeft": {"x": 1276, "y": 298}, "topRight": {"x": 1316, "y": 298}, "bottomLeft": {"x": 1276, "y": 338}, "bottomRight": {"x": 1316, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1288, "y": 309}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1288, "y": 309, "width": 18, "height": 18, "left": 1288, "top": 309, "right": 1306, "bottom": 327, "topLeft": {"x": 1288, "y": 309}, "topRight": {"x": 1306, "y": 309}, "bottomLeft": {"x": 1288, "y": 327}, "bottomRight": {"x": 1306, "y": 327}}, "children": []}]}, {"name": "Clear formatting (Ctrl+Space)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Link (Ctrl+K)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Italic (Ctrl+I)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "More options", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "OverflowButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1317, "y": 298}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 1317, "y": 298, "width": 40, "height": 40, "left": 1317, "top": 298, "right": 1357, "bottom": 338, "topLeft": {"x": 1317, "y": 298}, "topRight": {"x": 1357, "y": 298}, "bottomLeft": {"x": 1317, "y": 338}, "bottomRight": {"x": 1357, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1328, "y": 309}, "size": {"isEmpty": false, "width": 17, "height": 18}, "x": 1328, "y": 309, "width": 17, "height": 18, "left": 1328, "top": 309, "right": 1345, "bottom": 327, "topLeft": {"x": 1328, "y": 309}, "topRight": {"x": 1345, "y": 309}, "bottomLeft": {"x": 1328, "y": 327}, "bottomRight": {"x": 1345, "y": 327}}, "children": []}]}, {"name": "Copilot (Preview)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "RewriteDropDownButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1394, "y": 298}, "size": {"isEmpty": false, "width": 73, "height": 40}, "x": 1394, "y": 298, "width": 73, "height": 40, "left": 1394, "top": 298, "right": 1467, "bottom": 338, "topLeft": {"x": 1394, "y": 298}, "topRight": {"x": 1467, "y": 298}, "bottomLeft": {"x": 1394, "y": 338}, "bottomRight": {"x": 1467, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1408, "y": 307}, "size": {"isEmpty": false, "width": 19, "height": 20}, "x": 1408, "y": 307, "width": 19, "height": 20, "left": 1408, "top": 307, "right": 1427, "bottom": 327, "topLeft": {"x": 1408, "y": 307}, "topRight": {"x": 1427, "y": 307}, "bottomLeft": {"x": 1408, "y": 327}, "bottomRight": {"x": 1427, "y": 327}}, "children": []}]}, {"name": "User avatar", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AvatarButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1472, "y": 298}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 1472, "y": 298, "width": 38, "height": 40, "left": 1472, "top": 298, "right": 1510, "bottom": 338, "topLeft": {"x": 1472, "y": 298}, "topRight": {"x": 1510, "y": 298}, "bottomLeft": {"x": 1472, "y": 338}, "bottomRight": {"x": 1510, "y": 338}}, "children": [{"name": "Person", "controlType": "ControlType.Text", "automationId": "Avatar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1478, "y": 306}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1478, "y": 306, "width": 25, "height": 25, "left": 1478, "top": 306, "right": 1503, "bottom": 331, "topLeft": {"x": 1478, "y": 306}, "topRight": {"x": 1503, "y": 306}, "bottomLeft": {"x": 1478, "y": 331}, "bottomRight": {"x": 1503, "y": 331}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "InitialsTextBlock", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "", "controlType": "ControlType.ProgressBar", "automationId": "LoadingRing", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1478, "y": 306}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1478, "y": 306, "width": 25, "height": 25, "left": 1478, "top": 306, "right": 1503, "bottom": 331, "topLeft": {"x": 1478, "y": 306}, "topRight": {"x": 1503, "y": 306}, "bottomLeft": {"x": 1478, "y": 331}, "bottomRight": {"x": 1503, "y": 331}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "<PERSON>tiePlayer", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1478, "y": 306}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1478, "y": 306, "width": 25, "height": 25, "left": 1478, "top": 306, "right": 1503, "bottom": 331, "topLeft": {"x": 1478, "y": 306}, "topRight": {"x": 1503, "y": 306}, "bottomLeft": {"x": 1478, "y": 331}, "bottomRight": {"x": 1503, "y": 331}}, "children": []}]}, {"name": "Settings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "SettingsButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1515, "y": 298}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 1515, "y": 298, "width": 38, "height": 40, "left": 1515, "top": 298, "right": 1553, "bottom": 338, "topLeft": {"x": 1515, "y": 298}, "topRight": {"x": 1553, "y": 298}, "bottomLeft": {"x": 1515, "y": 338}, "bottomRight": {"x": 1553, "y": 338}}, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "PrivacyTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "CowriterTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 638}, "size": {"isEmpty": false, "width": 663, "height": 40}, "x": 900, "y": 638, "width": 663, "height": 40, "left": 900, "top": 638, "right": 1563, "bottom": 678, "topLeft": {"x": 900, "y": 638}, "topRight": {"x": 1563, "y": 638}, "bottomLeft": {"x": 900, "y": 678}, "bottomRight": {"x": 1563, "y": 678}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 638}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 900, "y": 638, "width": 530, "height": 32, "left": 900, "top": 638, "right": 1430, "bottom": 670, "topLeft": {"x": 900, "y": 638}, "topRight": {"x": 1430, "y": 638}, "bottomLeft": {"x": 900, "y": 670}, "bottomRight": {"x": 1430, "y": 670}}, "children": [{"name": "Line 1,\nColumn 1", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 920, "y": 648}, "size": {"isEmpty": false, "width": 74, "height": 20}, "x": 920, "y": 648, "width": 74, "height": 20, "left": 920, "top": 648, "right": 994, "bottom": 668, "topLeft": {"x": 920, "y": 648}, "topRight": {"x": 994, "y": 648}, "bottomLeft": {"x": 920, "y": 668}, "bottomRight": {"x": 994, "y": 668}}, "children": []}, {"name": "0 characters", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1031, "y": 648}, "size": {"isEmpty": false, "width": 82, "height": 20}, "x": 1031, "y": 648, "width": 82, "height": 20, "left": 1031, "top": 648, "right": 1113, "bottom": 668, "topLeft": {"x": 1031, "y": 648}, "topRight": {"x": 1113, "y": 648}, "bottomLeft": {"x": 1031, "y": 668}, "bottomRight": {"x": 1113, "y": 668}}, "children": []}, {"name": "Plain text", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1138, "y": 638}, "size": {"isEmpty": false, "width": 78, "height": 40}, "x": 1138, "y": 638, "width": 78, "height": 40, "left": 1138, "top": 638, "right": 1216, "bottom": 678, "topLeft": {"x": 1138, "y": 638}, "topRight": {"x": 1216, "y": 638}, "bottomLeft": {"x": 1138, "y": 678}, "bottomRight": {"x": 1216, "y": 678}}, "children": []}, {"name": "Zoom", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1261, "y": 648}, "size": {"isEmpty": false, "width": 39, "height": 20}, "x": 1261, "y": 648, "width": 39, "height": 20, "left": 1261, "top": 648, "right": 1300, "bottom": 668, "topLeft": {"x": 1261, "y": 648}, "topRight": {"x": 1300, "y": 648}, "bottomLeft": {"x": 1261, "y": 668}, "bottomRight": {"x": 1300, "y": 668}}, "children": []}, {"name": " Windows (CRLF)", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1333, "y": 648}, "size": {"isEmpty": false, "width": 94, "height": 20}, "x": 1333, "y": 648, "width": 94, "height": 20, "left": 1333, "top": 648, "right": 1427, "bottom": 668, "topLeft": {"x": 1333, "y": 648}, "topRight": {"x": 1427, "y": 648}, "bottomLeft": {"x": 1333, "y": 668}, "bottomRight": {"x": 1427, "y": 668}}, "children": []}, {"name": " UTF-8", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1449, "y": 648}, "size": {"isEmpty": false, "width": 45, "height": 20}, "x": 1449, "y": 648, "width": 45, "height": 20, "left": 1449, "top": 648, "right": 1494, "bottom": 668, "topLeft": {"x": 1449, "y": 648}, "topRight": {"x": 1494, "y": 648}, "bottomLeft": {"x": 1449, "y": 668}, "bottomRight": {"x": 1494, "y": 668}}, "children": []}]}]}, {"name": "Untitled - Notepad", "controlType": "ControlType.TitleBar", "automationId": "TitleBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 255}, "size": {"isEmpty": false, "width": 661, "height": 29}, "x": 901, "y": 255, "width": 661, "height": 29, "left": 901, "top": 255, "right": 1562, "bottom": 284, "topLeft": {"x": 901, "y": 255}, "topRight": {"x": 1562, "y": 255}, "bottomLeft": {"x": 901, "y": 284}, "bottomRight": {"x": 1562, "y": 284}}, "children": [{"name": "System Menu Bar", "controlType": "ControlType.MenuBar", "automationId": "SystemMenuBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 255}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 901, "y": 255, "width": 28, "height": 28, "left": 901, "top": 255, "right": 929, "bottom": 283, "topLeft": {"x": 901, "y": 255}, "topRight": {"x": 929, "y": 255}, "bottomLeft": {"x": 901, "y": 283}, "bottomRight": {"x": 929, "y": 283}}, "children": [{"name": "System", "controlType": "ControlType.MenuItem", "automationId": "Item 1", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 255}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 901, "y": 255, "width": 28, "height": 28, "left": 901, "top": 255, "right": 929, "bottom": 283, "topLeft": {"x": 901, "y": 255}, "topRight": {"x": 929, "y": 255}, "bottomLeft": {"x": 901, "y": 283}, "bottomRight": {"x": 929, "y": 283}}, "children": []}]}, {"name": "Minimize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Minimize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1385, "y": 247}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 1385, "y": 247, "width": 60, "height": 37, "left": 1385, "top": 247, "right": 1445, "bottom": 284, "topLeft": {"x": 1385, "y": 247}, "topRight": {"x": 1445, "y": 247}, "bottomLeft": {"x": 1385, "y": 284}, "bottomRight": {"x": 1445, "y": 284}}, "children": []}, {"name": "Maximize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Maximize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1445, "y": 247}, "size": {"isEmpty": false, "width": 59, "height": 37}, "x": 1445, "y": 247, "width": 59, "height": 37, "left": 1445, "top": 247, "right": 1504, "bottom": 284, "topLeft": {"x": 1445, "y": 247}, "topRight": {"x": 1504, "y": 247}, "bottomLeft": {"x": 1445, "y": 284}, "bottomRight": {"x": 1504, "y": 284}}, "children": []}, {"name": "Close", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Close", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1504, "y": 247}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 1504, "y": 247, "width": 60, "height": 37, "left": 1504, "top": 247, "right": 1564, "bottom": 284, "topLeft": {"x": 1504, "y": 247}, "topRight": {"x": 1564, "y": 247}, "bottomLeft": {"x": 1504, "y": 284}, "bottomRight": {"x": 1564, "y": 284}}, "children": []}]}]}
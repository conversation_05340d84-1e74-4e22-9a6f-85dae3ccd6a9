using System.Diagnostics;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Infrastructure;
using CognitiveDrive.Infrastructure.Interfaces;

/// <summary>
/// Production validation test to prove the new architecture works with real applications.
/// This replaces all the toy code with a robust, production-ready implementation.
/// </summary>
class ProductionValidationTest
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🏗️ PRODUCTION ARCHITECTURE VALIDATION");
        Console.WriteLine("=====================================");
        Console.WriteLine("Testing the new production-ready CognitiveDrive architecture...\n");

        try
        {
            // Initialize production components
            var configuration = new ScannerConfiguration
            {
                MaxScanDepth = 20,
                MaxElementsPerScan = 2000,
                PropertyExtractionTimeout = TimeSpan.FromSeconds(1),
                ChildEnumerationTimeout = TimeSpan.FromSeconds(5),
                RetryCount = 2,
                RetryDelay = TimeSpan.FromMilliseconds(100)
            };

            var propertyExtractor = new WindowsUiaPropertyExtractor(configuration);
            var processManager = new WindowsProcessManager();
            var scanner = new WindowsUiaScanner(propertyExtractor, processManager, configuration);

            Console.WriteLine("✅ Production components initialized successfully");

            // Test 1: Process Discovery
            Console.WriteLine("\n🔍 TEST 1: Process Discovery");
            var processesResult = await scanner.GetAvailableProcessesAsync();
            
            if (processesResult.IsSuccess)
            {
                var processes = processesResult.Value!;
                Console.WriteLine($"✅ Found {processes.Count} processes with UI");
                
                foreach (var process in processes.Take(5))
                {
                    Console.WriteLine($"   - {process.ProcessName} (PID: {process.ProcessId}) - {process.MainWindowTitle}");
                }
                
                if (processes.Count > 5)
                {
                    Console.WriteLine($"   ... and {processes.Count - 5} more");
                }
            }
            else
            {
                Console.WriteLine($"❌ Process discovery failed: {processesResult.ErrorMessage}");
                return;
            }

            // Test 2: Launch and Scan Notepad
            Console.WriteLine("\n📝 TEST 2: Launch and Scan Notepad");
            var notepadProcess = await LaunchNotepadAsync();
            
            try
            {
                var scanResult = await scanner.ScanProcessAsync(notepadProcess.Id);
                
                if (scanResult.IsSuccess)
                {
                    var rootElement = scanResult.Value!;
                    Console.WriteLine($"✅ Notepad scan completed successfully:");
                    Console.WriteLine($"   - Total elements: {rootElement.TotalDescendantCount + 1}");
                    Console.WriteLine($"   - Scan duration: {scanResult.Duration.TotalMilliseconds:F2}ms");
                    Console.WriteLine($"   - Root element: {rootElement.Name} ({rootElement.ControlType})");
                    
                    // Test targeting capabilities
                    var fileMenu = rootElement.FindFirstDescendant(e => 
                        e.Name.Contains("File", StringComparison.OrdinalIgnoreCase) && 
                        e.ControlType.Contains("MenuItem"));
                    
                    if (fileMenu != null)
                    {
                        Console.WriteLine($"   - Found File menu: {fileMenu.Name} (ID: {fileMenu.AutomationId})");
                    }
                    
                    var textEditor = rootElement.FindFirstDescendant(e => 
                        e.ControlType.Contains("Document") || e.ControlType.Contains("Edit"));
                    
                    if (textEditor != null)
                    {
                        Console.WriteLine($"   - Found text editor: {textEditor.ControlType}");
                    }
                    
                    // Validate element properties
                    var actionableElements = rootElement.FindDescendants(e => e.IsActionable);
                    Console.WriteLine($"   - Actionable elements: {actionableElements.Count}");
                    
                    var elementsWithIds = rootElement.FindDescendants(e => !string.IsNullOrEmpty(e.AutomationId));
                    Console.WriteLine($"   - Elements with AutomationId: {elementsWithIds.Count}");
                    
                    var elementsWithNames = rootElement.FindDescendants(e => !string.IsNullOrEmpty(e.Name));
                    Console.WriteLine($"   - Elements with Name: {elementsWithNames.Count}");
                }
                else
                {
                    Console.WriteLine($"❌ Notepad scan failed: {scanResult.ErrorMessage}");
                }
            }
            finally
            {
                await CleanupProcessAsync(notepadProcess);
            }

            // Test 3: Performance and Statistics
            Console.WriteLine("\n📊 TEST 3: Performance Statistics");
            var stats = scanner.Statistics;
            Console.WriteLine($"✅ Scanner statistics:");
            Console.WriteLine($"   - {stats}");
            Console.WriteLine($"   - Success rate: {stats.SuccessRate:F1}%");
            Console.WriteLine($"   - Average elements per scan: {stats.AverageElementsPerScan:F0}");

            // Test 4: Error Handling
            Console.WriteLine("\n🛡️ TEST 4: Error Handling");
            var errorResult = await scanner.ScanProcessAsync(99999); // Non-existent PID
            
            if (errorResult.IsFailure)
            {
                Console.WriteLine($"✅ Error handling works correctly:");
                Console.WriteLine($"   - Error message: {errorResult.ErrorMessage}");
                Console.WriteLine($"   - Duration: {errorResult.Duration.TotalMilliseconds:F2}ms");
            }
            else
            {
                Console.WriteLine("❌ Error handling failed - should have returned failure");
            }

            // Test 5: Targeting Query System
            Console.WriteLine("\n🎯 TEST 5: Targeting Query System");
            
            var query = TargetingQuery.Builder()
                .WithName("File")
                .WithControlType("ControlType.MenuItem")
                .WithMaxDepth(10)
                .WithTimeout(TimeSpan.FromSeconds(2))
                .Build();
            
            var validationErrors = query.Validate();
            if (validationErrors.Count == 0)
            {
                Console.WriteLine($"✅ Targeting query system works:");
                Console.WriteLine($"   - Query: {query}");
                Console.WriteLine($"   - Has criteria: {query.HasCriteria}");
            }
            else
            {
                Console.WriteLine($"❌ Query validation failed: {string.Join(", ", validationErrors)}");
            }

            Console.WriteLine("\n🎉 PRODUCTION ARCHITECTURE VALIDATION COMPLETE");
            Console.WriteLine("===============================================");
            Console.WriteLine("✅ All core components are working correctly");
            Console.WriteLine("✅ Real-world application scanning successful");
            Console.WriteLine("✅ Error handling and performance monitoring operational");
            Console.WriteLine("✅ Ready for production deployment");

            scanner.Dispose();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ VALIDATION FAILED: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private static async Task<Process> LaunchNotepadAsync()
    {
        var process = Process.Start("notepad.exe");
        if (process == null)
            throw new InvalidOperationException("Failed to launch Notepad");

        // Wait for the window to be ready
        var deadline = DateTime.UtcNow.AddSeconds(5);
        while (DateTime.UtcNow < deadline)
        {
            process.Refresh();
            if (process.MainWindowHandle != IntPtr.Zero)
                return process;
                
            await Task.Delay(100);
        }
        
        throw new TimeoutException("Notepad did not create a window within 5 seconds");
    }

    private static async Task CleanupProcessAsync(Process process)
    {
        try
        {
            if (!process.HasExited)
            {
                process.CloseMainWindow();
                await Task.Delay(1000);
                
                if (!process.HasExited)
                {
                    process.Kill();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Failed to cleanup process: {ex.Message}");
        }
        finally
        {
            process.Dispose();
        }
    }
}

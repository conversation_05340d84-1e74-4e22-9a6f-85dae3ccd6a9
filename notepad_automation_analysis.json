{"name": "Untitled - Notepad", "controlType": "ControlType.Window", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1192, "y": 325}, "size": {"isEmpty": false, "width": 679, "height": 440}, "x": 1192, "y": 325, "width": 679, "height": 440, "left": 1192, "top": 325, "right": 1871, "bottom": 765, "topLeft": {"x": 1192, "y": 325}, "topRight": {"x": 1871, "y": 325}, "bottomLeft": {"x": 1192, "y": 765}, "bottomRight": {"x": 1871, "y": 765}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1200, "y": 418}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 1200, "y": 418, "width": 663, "height": 299, "left": 1200, "top": 418, "right": 1863, "bottom": 717, "topLeft": {"x": 1200, "y": 418}, "topRight": {"x": 1863, "y": 418}, "bottomLeft": {"x": 1200, "y": 717}, "bottomRight": {"x": 1863, "y": 717}}, "children": [{"name": "Text editor", "controlType": "ControlType.Document", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1200, "y": 420}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 1200, "y": 420, "width": 663, "height": 299, "left": 1200, "top": 420, "right": 1863, "bottom": 719, "topLeft": {"x": 1200, "y": 420}, "topRight": {"x": 1863, "y": 420}, "bottomLeft": {"x": 1200, "y": 719}, "bottomRight": {"x": 1863, "y": 719}}, "children": []}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1250, "y": 340}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 1250, "y": 340, "width": 359, "height": 40, "left": 1250, "top": 340, "right": 1609, "bottom": 380, "topLeft": {"x": 1250, "y": 340}, "topRight": {"x": 1609, "y": 340}, "bottomLeft": {"x": 1250, "y": 380}, "bottomRight": {"x": 1609, "y": 380}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1251, "y": 340}, "size": {"isEmpty": false, "width": 287, "height": 32}, "x": 1251, "y": 340, "width": 287, "height": 32, "left": 1251, "top": 340, "right": 1538, "bottom": 372, "topLeft": {"x": 1251, "y": 340}, "topRight": {"x": 1538, "y": 340}, "bottomLeft": {"x": 1251, "y": 372}, "bottomRight": {"x": 1538, "y": 372}}, "children": [{"name": "", "controlType": "ControlType.Tab", "automationId": "Tabs", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1251, "y": 340}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 1251, "y": 340, "width": 359, "height": 40, "left": 1251, "top": 340, "right": 1610, "bottom": 380, "topLeft": {"x": 1251, "y": 340}, "topRight": {"x": 1610, "y": 340}, "bottomLeft": {"x": 1251, "y": 380}, "bottomRight": {"x": 1610, "y": 380}}, "children": [{"name": "", "controlType": "ControlType.List", "automationId": "TabListView", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1253, "y": 340}, "size": {"isEmpty": false, "width": 312, "height": 40}, "x": 1253, "y": 340, "width": 312, "height": 40, "left": 1253, "top": 340, "right": 1565, "bottom": 380, "topLeft": {"x": 1253, "y": 340}, "topRight": {"x": 1565, "y": 340}, "bottomLeft": {"x": 1253, "y": 380}, "bottomRight": {"x": 1565, "y": 380}}, "children": [{"name": "Untitled. Unmodified.", "controlType": "ControlType.TabItem", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1256, "y": 340}, "size": {"isEmpty": false, "width": 307, "height": 40}, "x": 1256, "y": 340, "width": 307, "height": 40, "left": 1256, "top": 340, "right": 1563, "bottom": 380, "topLeft": {"x": 1256, "y": 340}, "topRight": {"x": 1563, "y": 340}, "bottomLeft": {"x": 1256, "y": 380}, "bottomRight": {"x": 1563, "y": 380}}, "children": [{"name": "Untitled", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1272, "y": 350}, "size": {"isEmpty": false, "width": 56, "height": 19}, "x": 1272, "y": 350, "width": 56, "height": 19, "left": 1272, "top": 350, "right": 1328, "bottom": 369, "topLeft": {"x": 1272, "y": 350}, "topRight": {"x": 1328, "y": 350}, "bottomLeft": {"x": 1272, "y": 369}, "bottomRight": {"x": 1328, "y": 369}}, "children": []}, {"name": "Close Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "CloseButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1512, "y": 345}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 1512, "y": 345, "width": 40, "height": 30, "left": 1512, "top": 345, "right": 1552, "bottom": 375, "topLeft": {"x": 1512, "y": 345}, "topRight": {"x": 1552, "y": 345}, "bottomLeft": {"x": 1512, "y": 375}, "bottomRight": {"x": 1552, "y": 375}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1525, "y": 352}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 1525, "y": 352, "width": 15, "height": 15, "left": 1525, "top": 352, "right": 1540, "bottom": 367, "topLeft": {"x": 1525, "y": 352}, "topRight": {"x": 1540, "y": 352}, "bottomLeft": {"x": 1525, "y": 367}, "bottomRight": {"x": 1540, "y": 367}}, "children": []}]}]}]}, {"name": "Add New Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AddButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1570, "y": 346}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 1570, "y": 346, "width": 40, "height": 30, "left": 1570, "top": 346, "right": 1610, "bottom": 376, "topLeft": {"x": 1570, "y": 346}, "topRight": {"x": 1610, "y": 346}, "bottomLeft": {"x": 1570, "y": 376}, "bottomRight": {"x": 1610, "y": 376}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1582, "y": 353}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 1582, "y": 353, "width": 15, "height": 15, "left": 1582, "top": 353, "right": 1597, "bottom": 368, "topLeft": {"x": 1582, "y": 353}, "topRight": {"x": 1597, "y": 353}, "bottomLeft": {"x": 1582, "y": 368}, "bottomRight": {"x": 1597, "y": 368}}, "children": []}]}]}, {"name": "Notepad automatically saves your progress. All your content will be available the next time you open Notepad.", "controlType": "ControlType.Pane", "automationId": "TeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1201, "y": 380}, "size": {"isEmpty": false, "width": 663, "height": 41}, "x": 1201, "y": 380, "width": 663, "height": 41, "left": 1201, "top": 380, "right": 1864, "bottom": 421, "topLeft": {"x": 1201, "y": 380}, "topRight": {"x": 1864, "y": 380}, "bottomLeft": {"x": 1201, "y": 421}, "bottomRight": {"x": 1864, "y": 421}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1201, "y": 380}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 1201, "y": 380, "width": 530, "height": 32, "left": 1201, "top": 380, "right": 1731, "bottom": 412, "topLeft": {"x": 1201, "y": 380}, "topRight": {"x": 1731, "y": 380}, "bottomLeft": {"x": 1201, "y": 412}, "bottomRight": {"x": 1731, "y": 412}}, "children": [{"name": "", "controlType": "ControlType.MenuBar", "automationId": "<PERSON><PERSON><PERSON><PERSON>", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1201, "y": 380}, "size": {"isEmpty": false, "width": 199, "height": 40}, "x": 1201, "y": 380, "width": 199, "height": 40, "left": 1201, "top": 380, "right": 1400, "bottom": 420, "topLeft": {"x": 1201, "y": 380}, "topRight": {"x": 1400, "y": 380}, "bottomLeft": {"x": 1201, "y": 420}, "bottomRight": {"x": 1400, "y": 420}}, "children": [{"name": "File", "controlType": "ControlType.MenuItem", "automationId": "File", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1206, "y": 380}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 1206, "y": 380, "width": 52, "height": 40, "left": 1206, "top": 380, "right": 1258, "bottom": 420, "topLeft": {"x": 1206, "y": 380}, "topRight": {"x": 1258, "y": 380}, "bottomLeft": {"x": 1206, "y": 420}, "bottomRight": {"x": 1258, "y": 420}}, "children": [{"name": "File", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1206, "y": 380}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 1206, "y": 380, "width": 52, "height": 40, "left": 1206, "top": 380, "right": 1258, "bottom": 420, "topLeft": {"x": 1206, "y": 380}, "topRight": {"x": 1258, "y": 380}, "bottomLeft": {"x": 1206, "y": 420}, "bottomRight": {"x": 1258, "y": 420}}, "children": [{"name": "File", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1218, "y": 388}, "size": {"isEmpty": false, "width": 27, "height": 24}, "x": 1218, "y": 388, "width": 27, "height": 24, "left": 1218, "top": 388, "right": 1245, "bottom": 412, "topLeft": {"x": 1218, "y": 388}, "topRight": {"x": 1245, "y": 388}, "bottomLeft": {"x": 1218, "y": 412}, "bottomRight": {"x": 1245, "y": 412}}, "children": []}]}]}, {"name": "Edit", "controlType": "ControlType.MenuItem", "automationId": "Edit", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1268, "y": 380}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 1268, "y": 380, "width": 55, "height": 40, "left": 1268, "top": 380, "right": 1323, "bottom": 420, "topLeft": {"x": 1268, "y": 380}, "topRight": {"x": 1323, "y": 380}, "bottomLeft": {"x": 1268, "y": 420}, "bottomRight": {"x": 1323, "y": 420}}, "children": [{"name": "Edit", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1268, "y": 380}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 1268, "y": 380, "width": 55, "height": 40, "left": 1268, "top": 380, "right": 1323, "bottom": 420, "topLeft": {"x": 1268, "y": 380}, "topRight": {"x": 1323, "y": 380}, "bottomLeft": {"x": 1268, "y": 420}, "bottomRight": {"x": 1323, "y": 420}}, "children": [{"name": "Edit", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1281, "y": 388}, "size": {"isEmpty": false, "width": 30, "height": 24}, "x": 1281, "y": 388, "width": 30, "height": 24, "left": 1281, "top": 388, "right": 1311, "bottom": 412, "topLeft": {"x": 1281, "y": 388}, "topRight": {"x": 1311, "y": 388}, "bottomLeft": {"x": 1281, "y": 412}, "bottomRight": {"x": 1311, "y": 412}}, "children": []}]}]}, {"name": "View", "controlType": "ControlType.MenuItem", "automationId": "View", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1333, "y": 380}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 1333, "y": 380, "width": 62, "height": 40, "left": 1333, "top": 380, "right": 1395, "bottom": 420, "topLeft": {"x": 1333, "y": 380}, "topRight": {"x": 1395, "y": 380}, "bottomLeft": {"x": 1333, "y": 420}, "bottomRight": {"x": 1395, "y": 420}}, "children": [{"name": "View", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1333, "y": 380}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 1333, "y": 380, "width": 62, "height": 40, "left": 1333, "top": 380, "right": 1395, "bottom": 420, "topLeft": {"x": 1333, "y": 380}, "topRight": {"x": 1395, "y": 380}, "bottomLeft": {"x": 1333, "y": 420}, "bottomRight": {"x": 1395, "y": 420}}, "children": [{"name": "View", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1345, "y": 388}, "size": {"isEmpty": false, "width": 37, "height": 24}, "x": 1345, "y": 388, "width": 37, "height": 24, "left": 1345, "top": 388, "right": 1382, "bottom": 412, "topLeft": {"x": 1345, "y": 388}, "topRight": {"x": 1382, "y": 388}, "bottomLeft": {"x": 1345, "y": 412}, "bottomRight": {"x": 1382, "y": 412}}, "children": []}]}]}]}, {"name": "Headings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1436, "y": 380}, "size": {"isEmpty": false, "width": 71, "height": 40}, "x": 1436, "y": 380, "width": 71, "height": 40, "left": 1436, "top": 380, "right": 1507, "bottom": 420, "topLeft": {"x": 1436, "y": 380}, "topRight": {"x": 1507, "y": 380}, "bottomLeft": {"x": 1436, "y": 420}, "bottomRight": {"x": 1507, "y": 420}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1450, "y": 390}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1450, "y": 390, "width": 18, "height": 18, "left": 1450, "top": 390, "right": 1468, "bottom": 408, "topLeft": {"x": 1450, "y": 390}, "topRight": {"x": 1468, "y": 390}, "bottomLeft": {"x": 1450, "y": 408}, "bottomRight": {"x": 1468, "y": 408}}, "children": []}]}, {"name": "Lists", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1507, "y": 380}, "size": {"isEmpty": false, "width": 70, "height": 40}, "x": 1507, "y": 380, "width": 70, "height": 40, "left": 1507, "top": 380, "right": 1577, "bottom": 420, "topLeft": {"x": 1507, "y": 380}, "topRight": {"x": 1577, "y": 380}, "bottomLeft": {"x": 1507, "y": 420}, "bottomRight": {"x": 1577, "y": 420}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1521, "y": 390}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1521, "y": 390, "width": 18, "height": 18, "left": 1521, "top": 390, "right": 1539, "bottom": 408, "topLeft": {"x": 1521, "y": 390}, "topRight": {"x": 1539, "y": 390}, "bottomLeft": {"x": 1521, "y": 408}, "bottomRight": {"x": 1539, "y": 408}}, "children": []}]}, {"name": "Bold (Ctrl+B)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1577, "y": 380}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 1577, "y": 380, "width": 40, "height": 40, "left": 1577, "top": 380, "right": 1617, "bottom": 420, "topLeft": {"x": 1577, "y": 380}, "topRight": {"x": 1617, "y": 380}, "bottomLeft": {"x": 1577, "y": 420}, "bottomRight": {"x": 1617, "y": 420}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1589, "y": 391}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1589, "y": 391, "width": 18, "height": 18, "left": 1589, "top": 391, "right": 1607, "bottom": 409, "topLeft": {"x": 1589, "y": 391}, "topRight": {"x": 1607, "y": 391}, "bottomLeft": {"x": 1589, "y": 409}, "bottomRight": {"x": 1607, "y": 409}}, "children": []}]}, {"name": "Clear formatting (Ctrl+Space)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Link (Ctrl+K)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Italic (Ctrl+I)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "More options", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "OverflowButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1618, "y": 380}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 1618, "y": 380, "width": 40, "height": 40, "left": 1618, "top": 380, "right": 1658, "bottom": 420, "topLeft": {"x": 1618, "y": 380}, "topRight": {"x": 1658, "y": 380}, "bottomLeft": {"x": 1618, "y": 420}, "bottomRight": {"x": 1658, "y": 420}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1629, "y": 391}, "size": {"isEmpty": false, "width": 17, "height": 18}, "x": 1629, "y": 391, "width": 17, "height": 18, "left": 1629, "top": 391, "right": 1646, "bottom": 409, "topLeft": {"x": 1629, "y": 391}, "topRight": {"x": 1646, "y": 391}, "bottomLeft": {"x": 1629, "y": 409}, "bottomRight": {"x": 1646, "y": 409}}, "children": []}]}, {"name": "Copilot (Preview)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "RewriteDropDownButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1695, "y": 380}, "size": {"isEmpty": false, "width": 73, "height": 40}, "x": 1695, "y": 380, "width": 73, "height": 40, "left": 1695, "top": 380, "right": 1768, "bottom": 420, "topLeft": {"x": 1695, "y": 380}, "topRight": {"x": 1768, "y": 380}, "bottomLeft": {"x": 1695, "y": 420}, "bottomRight": {"x": 1768, "y": 420}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1709, "y": 389}, "size": {"isEmpty": false, "width": 19, "height": 20}, "x": 1709, "y": 389, "width": 19, "height": 20, "left": 1709, "top": 389, "right": 1728, "bottom": 409, "topLeft": {"x": 1709, "y": 389}, "topRight": {"x": 1728, "y": 389}, "bottomLeft": {"x": 1709, "y": 409}, "bottomRight": {"x": 1728, "y": 409}}, "children": []}]}, {"name": "User avatar", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AvatarButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1773, "y": 380}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 1773, "y": 380, "width": 38, "height": 40, "left": 1773, "top": 380, "right": 1811, "bottom": 420, "topLeft": {"x": 1773, "y": 380}, "topRight": {"x": 1811, "y": 380}, "bottomLeft": {"x": 1773, "y": 420}, "bottomRight": {"x": 1811, "y": 420}}, "children": [{"name": "Person", "controlType": "ControlType.Text", "automationId": "Avatar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1779, "y": 388}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1779, "y": 388, "width": 25, "height": 25, "left": 1779, "top": 388, "right": 1804, "bottom": 413, "topLeft": {"x": 1779, "y": 388}, "topRight": {"x": 1804, "y": 388}, "bottomLeft": {"x": 1779, "y": 413}, "bottomRight": {"x": 1804, "y": 413}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "InitialsTextBlock", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "", "controlType": "ControlType.ProgressBar", "automationId": "LoadingRing", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1779, "y": 388}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1779, "y": 388, "width": 25, "height": 25, "left": 1779, "top": 388, "right": 1804, "bottom": 413, "topLeft": {"x": 1779, "y": 388}, "topRight": {"x": 1804, "y": 388}, "bottomLeft": {"x": 1779, "y": 413}, "bottomRight": {"x": 1804, "y": 413}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "<PERSON>tiePlayer", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1779, "y": 388}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1779, "y": 388, "width": 25, "height": 25, "left": 1779, "top": 388, "right": 1804, "bottom": 413, "topLeft": {"x": 1779, "y": 388}, "topRight": {"x": 1804, "y": 388}, "bottomLeft": {"x": 1779, "y": 413}, "bottomRight": {"x": 1804, "y": 413}}, "children": []}]}, {"name": "Settings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "SettingsButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1816, "y": 380}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 1816, "y": 380, "width": 38, "height": 40, "left": 1816, "top": 380, "right": 1854, "bottom": 420, "topLeft": {"x": 1816, "y": 380}, "topRight": {"x": 1854, "y": 380}, "bottomLeft": {"x": 1816, "y": 420}, "bottomRight": {"x": 1854, "y": 420}}, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "PrivacyTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "CowriterTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1201, "y": 720}, "size": {"isEmpty": false, "width": 663, "height": 40}, "x": 1201, "y": 720, "width": 663, "height": 40, "left": 1201, "top": 720, "right": 1864, "bottom": 760, "topLeft": {"x": 1201, "y": 720}, "topRight": {"x": 1864, "y": 720}, "bottomLeft": {"x": 1201, "y": 760}, "bottomRight": {"x": 1864, "y": 760}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1201, "y": 720}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 1201, "y": 720, "width": 530, "height": 32, "left": 1201, "top": 720, "right": 1731, "bottom": 752, "topLeft": {"x": 1201, "y": 720}, "topRight": {"x": 1731, "y": 720}, "bottomLeft": {"x": 1201, "y": 752}, "bottomRight": {"x": 1731, "y": 752}}, "children": [{"name": "Line 1,\nColumn 1", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1221, "y": 730}, "size": {"isEmpty": false, "width": 74, "height": 20}, "x": 1221, "y": 730, "width": 74, "height": 20, "left": 1221, "top": 730, "right": 1295, "bottom": 750, "topLeft": {"x": 1221, "y": 730}, "topRight": {"x": 1295, "y": 730}, "bottomLeft": {"x": 1221, "y": 750}, "bottomRight": {"x": 1295, "y": 750}}, "children": []}, {"name": "0 characters", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1332, "y": 730}, "size": {"isEmpty": false, "width": 82, "height": 20}, "x": 1332, "y": 730, "width": 82, "height": 20, "left": 1332, "top": 730, "right": 1414, "bottom": 750, "topLeft": {"x": 1332, "y": 730}, "topRight": {"x": 1414, "y": 730}, "bottomLeft": {"x": 1332, "y": 750}, "bottomRight": {"x": 1414, "y": 750}}, "children": []}, {"name": "Plain text", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1439, "y": 720}, "size": {"isEmpty": false, "width": 78, "height": 40}, "x": 1439, "y": 720, "width": 78, "height": 40, "left": 1439, "top": 720, "right": 1517, "bottom": 760, "topLeft": {"x": 1439, "y": 720}, "topRight": {"x": 1517, "y": 720}, "bottomLeft": {"x": 1439, "y": 760}, "bottomRight": {"x": 1517, "y": 760}}, "children": []}, {"name": "Zoom", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1562, "y": 730}, "size": {"isEmpty": false, "width": 39, "height": 20}, "x": 1562, "y": 730, "width": 39, "height": 20, "left": 1562, "top": 730, "right": 1601, "bottom": 750, "topLeft": {"x": 1562, "y": 730}, "topRight": {"x": 1601, "y": 730}, "bottomLeft": {"x": 1562, "y": 750}, "bottomRight": {"x": 1601, "y": 750}}, "children": []}, {"name": " Windows (CRLF)", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1634, "y": 730}, "size": {"isEmpty": false, "width": 94, "height": 20}, "x": 1634, "y": 730, "width": 94, "height": 20, "left": 1634, "top": 730, "right": 1728, "bottom": 750, "topLeft": {"x": 1634, "y": 730}, "topRight": {"x": 1728, "y": 730}, "bottomLeft": {"x": 1634, "y": 750}, "bottomRight": {"x": 1728, "y": 750}}, "children": []}, {"name": " UTF-8", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1750, "y": 730}, "size": {"isEmpty": false, "width": 45, "height": 20}, "x": 1750, "y": 730, "width": 45, "height": 20, "left": 1750, "top": 730, "right": 1795, "bottom": 750, "topLeft": {"x": 1750, "y": 730}, "topRight": {"x": 1795, "y": 730}, "bottomLeft": {"x": 1750, "y": 750}, "bottomRight": {"x": 1795, "y": 750}}, "children": []}]}]}, {"name": "Untitled - Notepad", "controlType": "ControlType.TitleBar", "automationId": "TitleBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1202, "y": 337}, "size": {"isEmpty": false, "width": 661, "height": 29}, "x": 1202, "y": 337, "width": 661, "height": 29, "left": 1202, "top": 337, "right": 1863, "bottom": 366, "topLeft": {"x": 1202, "y": 337}, "topRight": {"x": 1863, "y": 337}, "bottomLeft": {"x": 1202, "y": 366}, "bottomRight": {"x": 1863, "y": 366}}, "children": [{"name": "System Menu Bar", "controlType": "ControlType.MenuBar", "automationId": "SystemMenuBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1202, "y": 337}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 1202, "y": 337, "width": 28, "height": 28, "left": 1202, "top": 337, "right": 1230, "bottom": 365, "topLeft": {"x": 1202, "y": 337}, "topRight": {"x": 1230, "y": 337}, "bottomLeft": {"x": 1202, "y": 365}, "bottomRight": {"x": 1230, "y": 365}}, "children": [{"name": "System", "controlType": "ControlType.MenuItem", "automationId": "Item 1", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1202, "y": 337}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 1202, "y": 337, "width": 28, "height": 28, "left": 1202, "top": 337, "right": 1230, "bottom": 365, "topLeft": {"x": 1202, "y": 337}, "topRight": {"x": 1230, "y": 337}, "bottomLeft": {"x": 1202, "y": 365}, "bottomRight": {"x": 1230, "y": 365}}, "children": []}]}, {"name": "Minimize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Minimize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1686, "y": 329}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 1686, "y": 329, "width": 60, "height": 37, "left": 1686, "top": 329, "right": 1746, "bottom": 366, "topLeft": {"x": 1686, "y": 329}, "topRight": {"x": 1746, "y": 329}, "bottomLeft": {"x": 1686, "y": 366}, "bottomRight": {"x": 1746, "y": 366}}, "children": []}, {"name": "Maximize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Maximize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1746, "y": 329}, "size": {"isEmpty": false, "width": 59, "height": 37}, "x": 1746, "y": 329, "width": 59, "height": 37, "left": 1746, "top": 329, "right": 1805, "bottom": 366, "topLeft": {"x": 1746, "y": 329}, "topRight": {"x": 1805, "y": 329}, "bottomLeft": {"x": 1746, "y": 366}, "bottomRight": {"x": 1805, "y": 366}}, "children": []}, {"name": "Close", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Close", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1805, "y": 329}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 1805, "y": 329, "width": 60, "height": 37, "left": 1805, "top": 329, "right": 1865, "bottom": 366, "topLeft": {"x": 1805, "y": 329}, "topRight": {"x": 1865, "y": 329}, "bottomLeft": {"x": 1805, "y": 366}, "bottomRight": {"x": 1865, "y": 366}}, "children": []}]}]}
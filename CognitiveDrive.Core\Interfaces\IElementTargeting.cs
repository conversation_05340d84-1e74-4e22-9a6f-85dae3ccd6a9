using CognitiveDrive.Core.Models;

namespace CognitiveDrive.Core.Interfaces;

/// <summary>
/// Defines the contract for finding and targeting UI elements within element trees.
/// Provides high-performance, flexible element discovery with caching and optimization.
/// </summary>
public interface IElementTargeting
{
    /// <summary>
    /// Finds the first element that matches the specified query.
    /// </summary>
    /// <param name="rootElement">The root element to search within</param>
    /// <param name="query">The targeting query</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing the first matching element, or null if none found</returns>
    Task<ScanResult<UiaElementNode?>> FindElementAsync(UiaElementNode rootElement, TargetingQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Finds all elements that match the specified query.
    /// </summary>
    /// <param name="rootElement">The root element to search within</param>
    /// <param name="query">The targeting query</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing all matching elements</returns>
    Task<ScanResult<IReadOnlyList<UiaElementNode>>> FindAllElementsAsync(UiaElementNode rootElement, TargetingQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Finds elements using a path-based query (e.g., "Window/MenuBar/MenuItem[name='File']").
    /// </summary>
    /// <param name="rootElement">The root element to search within</param>
    /// <param name="path">The element path</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing the element at the specified path</returns>
    Task<ScanResult<UiaElementNode?>> FindElementByPathAsync(UiaElementNode rootElement, string path, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates that an element still exists and matches its expected properties.
    /// </summary>
    /// <param name="element">The element to validate</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result indicating whether the element is still valid</returns>
    Task<ScanResult<bool>> ValidateElementAsync(UiaElementNode element, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets targeting statistics for performance monitoring.
    /// </summary>
    /// <returns>Targeting performance statistics</returns>
    TargetingStatistics GetStatistics();

    /// <summary>
    /// Clears any internal caches used for targeting optimization.
    /// </summary>
    void ClearCache();
}

/// <summary>
/// Contains performance statistics for element targeting operations.
/// </summary>
public sealed class TargetingStatistics
{
    /// <summary>
    /// Gets or sets the total number of targeting operations performed.
    /// </summary>
    public long TotalOperations { get; set; }

    /// <summary>
    /// Gets or sets the number of successful targeting operations.
    /// </summary>
    public long SuccessfulOperations { get; set; }

    /// <summary>
    /// Gets or sets the number of failed targeting operations.
    /// </summary>
    public long FailedOperations { get; set; }

    /// <summary>
    /// Gets or sets the average operation duration in milliseconds.
    /// </summary>
    public double AverageOperationDurationMs { get; set; }

    /// <summary>
    /// Gets or sets the number of cache hits.
    /// </summary>
    public long CacheHits { get; set; }

    /// <summary>
    /// Gets or sets the number of cache misses.
    /// </summary>
    public long CacheMisses { get; set; }

    /// <summary>
    /// Gets or sets the total number of elements searched.
    /// </summary>
    public long TotalElementsSearched { get; set; }

    /// <summary>
    /// Gets the success rate as a percentage.
    /// </summary>
    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations * 100 : 0;

    /// <summary>
    /// Gets the cache hit rate as a percentage.
    /// </summary>
    public double CacheHitRate => (CacheHits + CacheMisses) > 0 ? (double)CacheHits / (CacheHits + CacheMisses) * 100 : 0;

    /// <summary>
    /// Gets the average elements searched per operation.
    /// </summary>
    public double AverageElementsPerOperation => TotalOperations > 0 ? (double)TotalElementsSearched / TotalOperations : 0;

    public override string ToString()
    {
        return $"Targeting Stats: {TotalOperations} ops, {SuccessRate:F1}% success, {AverageOperationDurationMs:F2}ms avg, {CacheHitRate:F1}% cache hit rate";
    }
}

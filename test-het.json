{"name": "Test Window - Notepad", "controlType": "ControlType.Window", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"x": 100, "y": 100, "width": 800, "height": 600}, "children": [{"name": "File", "controlType": "ControlType.MenuItem", "automationId": "File", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"x": 110, "y": 130, "width": 50, "height": 30}, "children": []}, {"name": "Edit", "controlType": "ControlType.MenuItem", "automationId": "Edit", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"x": 170, "y": 130, "width": 50, "height": 30}, "children": []}, {"name": "Text Editor", "controlType": "ControlType.Document", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"x": 110, "y": 170, "width": 780, "height": 400}, "children": []}, {"name": "Virtual Element", "controlType": "ControlType.Custom", "automationId": "VirtualElement", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "Child of Virtual", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Close", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Close", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"x": 850, "y": 110, "width": 40, "height": 30}, "children": []}]}
{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"CognitiveDrive.Daemon/1.0.0": {"dependencies": {"CognitiveDrive.CoreUIA": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.ServiceProcess.ServiceController": "8.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"CognitiveDrive.Daemon.dll": {}}}, "System.Configuration.ConfigurationManager/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "System.Diagnostics.EventLog/8.0.1": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {}, "System.ServiceProcess.ServiceController/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1"}, "runtime": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}}, "System.Text.Json/8.0.5": {}, "CognitiveDrive.CoreUIA/1.0.0": {"dependencies": {"System.Text.Json": "8.0.5"}, "runtime": {"CognitiveDrive.CoreUIA.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"CognitiveDrive.Daemon/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "path": "system.configuration.configurationmanager/8.0.1", "hashPath": "system.configuration.configurationmanager.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "path": "system.diagnostics.eventlog/8.0.1", "hashPath": "system.diagnostics.eventlog.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-02I0BXo1kmMBgw03E8Hu4K6nTqur4wpQdcDZrndczPzY2fEoGvlinE35AWbyzLZ2h2IksEZ6an4tVt3hi9j1oA==", "path": "system.serviceprocess.servicecontroller/8.0.1", "hashPath": "system.serviceprocess.servicecontroller.8.0.1.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "CognitiveDrive.CoreUIA/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}
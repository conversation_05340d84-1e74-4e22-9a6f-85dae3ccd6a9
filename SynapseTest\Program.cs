﻿using CognitiveDrive.Infrastructure;

/// <summary>
/// Simple test to verify the Synapse event monitoring is working
/// </summary>
class SynapseTest
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🧠 SYNAPSE SIMPLE TEST");
        Console.WriteLine("======================");
        Console.WriteLine("Testing basic event monitoring...\n");

        var eventMonitor = UiaEventMonitor.Instance;
        var eventCount = 0;

        // Subscribe to events
        eventMonitor.FocusChanged += (sender, e) =>
        {
            eventCount++;
            Console.WriteLine($"🎯 Event #{eventCount}: Focus changed to window {e.WindowHandle} (PID: {e.ProcessId})");
        };

        Console.WriteLine("🚀 Starting Synapse...");
        eventMonitor.Start();

        Console.WriteLine("👆 Please manually switch between applications for 10 seconds...");
        Console.WriteLine("   (Click on different windows, Alt+Tab, etc.)");

        await Task.Delay(10000);

        Console.WriteLine("\n🛑 Stopping Synapse...");
        eventMonitor.Stop();

        var stats = eventMonitor.Statistics;
        Console.WriteLine($"\n📊 RESULTS:");
        Console.WriteLine($"   - Events detected: {eventCount}");
        Console.WriteLine($"   - Synapse stats: {stats}");

        if (eventCount > 0)
        {
            Console.WriteLine("✅ SYNAPSE IS WORKING!");
        }
        else
        {
            Console.WriteLine("❌ SYNAPSE NOT DETECTING EVENTS");
        }

        eventMonitor.Dispose();
    }
}

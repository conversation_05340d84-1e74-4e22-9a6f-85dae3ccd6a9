{"version": 3, "targets": {"net8.0-windows7.0": {"CognitiveDrive.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/CognitiveDrive.Core.dll": {}}, "runtime": {"bin/placeholder/CognitiveDrive.Core.dll": {}}}, "CognitiveDrive.Infrastructure/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"CognitiveDrive.Core": "1.0.0"}, "compile": {"bin/placeholder/CognitiveDrive.Infrastructure.dll": {}}, "runtime": {"bin/placeholder/CognitiveDrive.Infrastructure.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App", "Microsoft.WindowsDesktop.App.WindowsForms"]}}}, "libraries": {"CognitiveDrive.Core/1.0.0": {"type": "project", "path": "../CognitiveDrive.Core/CognitiveDrive.Core.csproj", "msbuildProject": "../CognitiveDrive.Core/CognitiveDrive.Core.csproj"}, "CognitiveDrive.Infrastructure/1.0.0": {"type": "project", "path": "../CognitiveDrive.Infrastructure/CognitiveDrive.Infrastructure.csproj", "msbuildProject": "../CognitiveDrive.Infrastructure/CognitiveDrive.Infrastructure.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["CognitiveDrive.Core >= 1.0.0", "CognitiveDrive.Infrastructure >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ProductionValidation\\ProductionValidation.csproj", "projectName": "ProductionValidation", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ProductionValidation\\ProductionValidation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ProductionValidation\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}
using System;
using System.IO;
using System.IO.Pipes;
using System.ServiceProcess;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Automation;
using CognitiveDrive.CoreUIA;

namespace CognitiveDrive.Daemon
{
    /// <summary>
    /// Main Windows Service class for the CognitiveDrive Daemon.
    /// Provides persistent, real-time UI monitoring and Named Pipe communication.
    /// </summary>
    public partial class CognitiveDaemon : ServiceBase
    {
        private static UiaElementNode? _liveHET;
        private static readonly object _hetLock = new object();
        private UiaEventMonitor? _eventMonitor;
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _namedPipeServerTask;
        private readonly string _logFilePath;
        private readonly string _pipeName = "CognitiveDrivePipe";

        /// <summary>
        /// Initializes a new instance of the CognitiveDaemon service.
        /// </summary>
        public CognitiveDaemon()
        {
            InitializeComponent();
            
            // Set up logging to a file in the service directory
            string serviceDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? 
                                    Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            _logFilePath = Path.Combine(serviceDirectory, "CognitiveDrive", "daemon.log");
            
            // Ensure log directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(_logFilePath)!);
        }

        /// <summary>
        /// Called when the service is started.
        /// Initializes UI event monitoring and Named Pipe server.
        /// </summary>
        /// <param name="args">Service start arguments</param>
        protected override void OnStart(string[] args)
        {
            try
            {
                LogMessage("Starting CognitiveDrive Daemon service...");

                // Initialize cancellation token for graceful shutdown
                _cancellationTokenSource = new CancellationTokenSource();

                // Initialize and start the UIA event monitor
                _eventMonitor = UiaEventMonitor.Instance;
                _eventMonitor.FocusChanged += OnFocusChanged;
                _eventMonitor.StructureChanged += OnStructureChanged;
                _eventMonitor.Start();

                LogMessage("UIA Event Monitor started successfully.");

                // Perform initial full scan to populate the live HET
                PerformInitialScan();

                // Start the Named Pipe server
                LogMessage("Starting Named Pipe server...");
                _namedPipeServerTask = Task.Run(() => RunNamedPipeServer(_cancellationTokenSource.Token));

                LogMessage("Daemon service started successfully.");
            }
            catch (Exception ex)
            {
                LogMessage($"Error starting daemon service: {ex.Message}");
                LogMessage($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Called when the service is stopped.
        /// Cleans up resources and stops monitoring.
        /// </summary>
        protected override void OnStop()
        {
            try
            {
                LogMessage("Stopping CognitiveDrive Daemon service...");

                // Signal cancellation to all background tasks
                _cancellationTokenSource?.Cancel();

                // Stop the UIA event monitor
                if (_eventMonitor != null)
                {
                    _eventMonitor.FocusChanged -= OnFocusChanged;
                    _eventMonitor.StructureChanged -= OnStructureChanged;
                    _eventMonitor.Stop();
                }

                // Wait for Named Pipe server to stop
                _namedPipeServerTask?.Wait(TimeSpan.FromSeconds(5));

                // Clean up resources
                _cancellationTokenSource?.Dispose();

                LogMessage("Daemon service stopped successfully.");
            }
            catch (Exception ex)
            {
                LogMessage($"Error stopping daemon service: {ex.Message}");
            }
        }

        /// <summary>
        /// Performs an initial scan of the active window to populate the live HET.
        /// </summary>
        private void PerformInitialScan()
        {
            try
            {
                LogMessage("Performing initial active window scan...");

                // Get the currently focused element and find its window
                AutomationElement focusedElement = AutomationElement.FocusedElement;
                if (focusedElement != null)
                {
                    // Find the top-level window containing the focused element
                    AutomationElement window = focusedElement;
                    while (window != null && window.Current.ControlType != ControlType.Window)
                    {
                        try
                        {
                            TreeWalker walker = TreeWalker.ControlViewWalker;
                            window = walker.GetParent(window);
                        }
                        catch (ElementNotAvailableException)
                        {
                            break;
                        }
                    }

                    if (window != null)
                    {
                        // Build the initial HET for the active window
                        UiaElementNode initialHET = UiaScanner.BuildHET(window);

                        // Update the live HET thread-safely
                        lock (_hetLock)
                        {
                            _liveHET = initialHET;
                        }

                        LogMessage($"Initial scan completed. Window: {window.Current.Name}, Found {initialHET.TotalDescendantCount + 1} elements.");
                    }
                    else
                    {
                        LogMessage("No active window found for initial scan.");
                    }
                }
                else
                {
                    LogMessage("No focused element found for initial scan.");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error during initial scan: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles UI focus change events.
        /// </summary>
        private void OnFocusChanged(object sender, AutomationFocusChangedEventArgs e)
        {
            try
            {
                LogMessage($"Focus changed event received. Runtime ID: {string.Join(",", e.ChildId)}");
                
                // Trigger a rescan of the active window
                UpdateLiveHET();
            }
            catch (Exception ex)
            {
                LogMessage($"Error handling focus change: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles UI structure change events.
        /// </summary>
        private void OnStructureChanged(object sender, StructureChangedEventArgs e)
        {
            try
            {
                LogMessage($"Structure changed event received. Type: {e.StructureChangeType}");
                
                // Trigger a rescan of the active window
                UpdateLiveHET();
            }
            catch (Exception ex)
            {
                LogMessage($"Error handling structure change: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates the live HET by rescanning the current active window.
        /// </summary>
        private void UpdateLiveHET()
        {
            try
            {
                // Get the currently focused element
                AutomationElement focusedElement = AutomationElement.FocusedElement;
                if (focusedElement == null)
                {
                    return;
                }

                // Find the top-level window containing the focused element
                AutomationElement window = focusedElement;
                while (window != null && window.Current.ControlType != ControlType.Window)
                {
                    try
                    {
                        TreeWalker walker = TreeWalker.ControlViewWalker;
                        window = walker.GetParent(window);
                    }
                    catch (ElementNotAvailableException)
                    {
                        break;
                    }
                }

                if (window != null)
                {
                    // Build HET for the active window
                    UiaElementNode updatedHET = UiaScanner.BuildHET(window);

                    // Update the live HET thread-safely
                    lock (_hetLock)
                    {
                        _liveHET = updatedHET;
                    }

                    LogMessage($"Live HET updated. Window: {window.Current.Name}, Elements: {updatedHET.TotalDescendantCount + 1}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error updating live HET: {ex.Message}");
            }
        }

        /// <summary>
        /// Runs the Named Pipe server to communicate with Chariot clients.
        /// Uses per-client task handling to prevent broken pipe issues from affecting the main server loop.
        /// </summary>
        private async Task RunNamedPipeServer(CancellationToken cancellationToken)
        {
            LogMessage("Starting Named Pipe server...");

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Create a new pipe server instance for each client
                    var pipeServer = new NamedPipeServerStream(_pipeName, PipeDirection.Out,
                        NamedPipeServerStream.MaxAllowedServerInstances, PipeTransmissionMode.Message);

                    LogMessage("Waiting for client connection...");

                    // Wait for a client to connect
                    await pipeServer.WaitForConnectionAsync(cancellationToken);
                    LogMessage("Client connected to Named Pipe.");

                    // Spin off client handling to a separate task to prevent blocking the main server loop
                    _ = Task.Run(() => HandleClient(pipeServer, cancellationToken));
                }
                catch (OperationCanceledException)
                {
                    // Expected when service is stopping
                    break;
                }
                catch (Exception ex)
                {
                    LogMessage($"Error in Named Pipe server: {ex.Message}");
                    await Task.Delay(5000, cancellationToken); // Wait before retrying
                }
            }

            LogMessage("Named Pipe server stopped.");
        }

        /// <summary>
        /// Handles a single client connection with proper exception handling and resource cleanup.
        /// This method runs in its own task to isolate client-specific issues from the main server loop.
        /// </summary>
        private async Task HandleClient(NamedPipeServerStream pipeServer, CancellationToken cancellationToken)
        {
            try
            {
                // Send initial HET to the connected client
                await SendHETToClient(pipeServer);

                // Keep the connection alive and send updates
                while (pipeServer.IsConnected && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, cancellationToken); // Check every second

                    // Send updated HET if available
                    await SendHETToClient(pipeServer);
                }

                LogMessage("Client disconnected from Named Pipe.");
            }
            catch (OperationCanceledException)
            {
                // Expected when service is stopping
                LogMessage("Client connection cancelled due to service shutdown.");
            }
            catch (Exception ex)
            {
                LogMessage($"Error handling client: {ex.Message}");
            }
            finally
            {
                // Ensure proper cleanup of the pipe server
                try
                {
                    if (pipeServer.IsConnected)
                    {
                        pipeServer.Disconnect();
                    }
                    pipeServer.Dispose();
                    LogMessage("Client connection resources cleaned up.");
                }
                catch (Exception ex)
                {
                    LogMessage($"Error cleaning up client connection: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Sends the current live HET to a connected Named Pipe client.
        /// </summary>
        private async Task SendHETToClient(NamedPipeServerStream pipeServer)
        {
            try
            {
                UiaElementNode? currentHET;
                lock (_hetLock)
                {
                    currentHET = _liveHET;
                }

                if (currentHET != null && pipeServer.IsConnected)
                {
                    // Serialize the HET to JSON with robust cycle handling
                    var jsonOptions = new JsonSerializerOptions
                    {
                        WriteIndented = false, // Compact JSON for network transmission
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        ReferenceHandler = ReferenceHandler.IgnoreCycles,
                        MaxDepth = 128 // Increased depth limit for complex UI hierarchies
                    };

                    string jsonData = JsonSerializer.Serialize(currentHET, jsonOptions);
                    byte[] data = Encoding.UTF8.GetBytes(jsonData);

                    // Send the JSON data to the client
                    await pipeServer.WriteAsync(data, 0, data.Length);
                    await pipeServer.FlushAsync();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error sending HET to client: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs a message to the daemon log file with timestamp.
        /// </summary>
        private void LogMessage(string message)
        {
            try
            {
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";
                File.AppendAllText(_logFilePath, logEntry + Environment.NewLine);

                // Also write to console if running interactively
                if (Environment.UserInteractive)
                {
                    Console.WriteLine(logEntry);
                }
            }
            catch
            {
                // Ignore logging errors to prevent service crashes
            }
        }

        /// <summary>
        /// Test method for running the service in console mode for debugging.
        /// </summary>
        public void TestStartupAndStop(string[] args)
        {
            OnStart(args);
            Console.WriteLine("Service started. Press any key to stop...");
            Console.ReadKey();
            OnStop();
        }
    }
}

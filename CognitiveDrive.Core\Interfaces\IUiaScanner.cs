using CognitiveDrive.Core.Models;

namespace CognitiveDrive.Core.Interfaces;

/// <summary>
/// Defines the contract for scanning UI elements from applications.
/// This interface abstracts the underlying UI automation technology and provides
/// a clean, testable API for UI element discovery and tree construction.
/// </summary>
public interface IUiaScanner
{
    /// <summary>
    /// Scans the UI elements of the specified process and builds a hierarchical element tree.
    /// </summary>
    /// <param name="processId">The ID of the process to scan</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing the root element of the UI tree</returns>
    Task<ScanResult<UiaElementNode>> ScanProcessAsync(int processId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Scans the UI elements of the process with the specified name.
    /// </summary>
    /// <param name="processName">The name of the process to scan</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing the root element of the UI tree</returns>
    Task<ScanResult<UiaElementNode>> ScanProcessByNameAsync(string processName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Scans a specific window by its handle.
    /// </summary>
    /// <param name="windowHandle">The handle of the window to scan</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing the window's element tree</returns>
    Task<ScanResult<UiaElementNode>> ScanWindowAsync(IntPtr windowHandle, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs a quick scan to get basic information about a process without building the full tree.
    /// </summary>
    /// <param name="processId">The ID of the process to scan</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing basic process UI information</returns>
    Task<ScanResult<ProcessUiInfo>> GetProcessUiInfoAsync(int processId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the available processes that have UI elements.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A scan result containing the list of processes with UI</returns>
    Task<ScanResult<IReadOnlyList<ProcessUiInfo>>> GetAvailableProcessesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Contains basic information about a process's UI.
/// </summary>
public sealed class ProcessUiInfo
{
    /// <summary>
    /// Gets or sets the process ID.
    /// </summary>
    public int ProcessId { get; set; }

    /// <summary>
    /// Gets or sets the process name.
    /// </summary>
    public string ProcessName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the main window title.
    /// </summary>
    public string MainWindowTitle { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the main window handle.
    /// </summary>
    public IntPtr MainWindowHandle { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the process has a visible UI.
    /// </summary>
    public bool HasVisibleUi { get; set; }

    /// <summary>
    /// Gets or sets the estimated number of UI elements.
    /// </summary>
    public int EstimatedElementCount { get; set; }

    /// <summary>
    /// Gets or sets the UI framework used by the application.
    /// </summary>
    public string UiFramework { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets additional metadata about the process.
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    public override string ToString()
    {
        return $"{ProcessName} (PID: {ProcessId}) - {MainWindowTitle} - {EstimatedElementCount} elements";
    }
}

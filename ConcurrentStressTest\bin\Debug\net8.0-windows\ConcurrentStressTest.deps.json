{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ConcurrentStressTest/1.0.0": {"dependencies": {"CognitiveDrive.Core": "1.0.0", "CognitiveDrive.Infrastructure": "1.0.0"}, "runtime": {"ConcurrentStressTest.dll": {}}}, "CognitiveDrive.Core/1.0.0": {"runtime": {"CognitiveDrive.Core.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "CognitiveDrive.Infrastructure/1.0.0": {"dependencies": {"CognitiveDrive.Core": "1.0.0"}, "runtime": {"CognitiveDrive.Infrastructure.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"ConcurrentStressTest/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CognitiveDrive.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CognitiveDrive.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}
﻿<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="CognitiveDrive.Chariot.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="using:CognitiveDrive.Chariot">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Top toolbar -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Padding="10" Background="LightGray">
            <Button x:ConnectionId='4' x:Name="LoadButton"
                    Content="Load HET File"
                                            
                    Padding="15,8"
                    Margin="0,0,10,0"/>

            <TextBlock x:ConnectionId='5' x:Name="StatusText"
                       Text="No HET file loaded"
                       VerticalAlignment="Center"
                       Margin="10,0"/>

            <Button x:ConnectionId='6' x:Name="ClearButton"
                    Content="Clear Overlay"
                                             
                    Padding="15,8"
                    Margin="10,0,0,0"
                    IsEnabled="False"/>
        </StackPanel>

        <!-- Main canvas for overlay rendering -->
        <ScrollViewer Grid.Row="1"
                      ZoomMode="Enabled"
                      HorizontalScrollMode="Enabled"
                      VerticalScrollMode="Enabled"
                      HorizontalScrollBarVisibility="Auto"
                      VerticalScrollBarVisibility="Auto">

            <Canvas x:ConnectionId='2' x:Name="OverlayCanvas"
                    Background="Transparent"
                    Width="1920"
                    Height="1080">

                <!-- Instructions text when no HET is loaded -->
                <TextBlock x:ConnectionId='3' x:Name="InstructionsText"
                           Canvas.Left="50"
                           Canvas.Top="50"
                           Text="Click 'Load HET File' to visualize a UI element hierarchy"
                           FontSize="16"
                           Foreground="Gray"/>

            </Canvas>
        </ScrollViewer>
    </Grid>
</Window>


{"name": "Project - Visual Studio Code", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": -9, "y": -9}, "size": {"isEmpty": false, "width": 1938, "height": 1098}, "x": -9, "y": -9, "width": 1938, "height": 1098, "left": -9, "top": -9, "right": 1929, "bottom": 1089, "topLeft": {"x": -9, "y": -9}, "topRight": {"x": 1929, "y": -9}, "bottomLeft": {"x": -9, "y": 1089}, "bottomRight": {"x": 1929, "y": 1089}}, "children": [{"name": "Chrome Legacy Window", "controlType": "ControlType.Document", "automationId": "750816", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": []}, {"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": false, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1078}, "x": 0, "y": 0, "width": 1920, "height": 1078, "left": 0, "top": 0, "right": 1920, "bottom": 1078, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1078}, "bottomRight": {"x": 1920, "y": 1078}}, "children": []}, {"name": "Project - Visual Studio Code", "controlType": "ControlType.TitleBar", "automationId": "TitleBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 29}, "x": 0, "y": 0, "width": 1920, "height": 29, "left": 0, "top": 0, "right": 1920, "bottom": 29, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 29}, "bottomRight": {"x": 1920, "y": 29}}, "children": []}, {"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1748, "y": 0}, "size": {"isEmpty": false, "width": 172, "height": 52}, "x": 1748, "y": 0, "width": 172, "height": 52, "left": 1748, "top": 0, "right": 1920, "bottom": 52, "topLeft": {"x": 1748, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 1748, "y": 52}, "bottomRight": {"x": 1920, "y": 52}}, "children": [{"name": "Minimize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1748, "y": 0}, "size": {"isEmpty": false, "width": 57, "height": 52}, "x": 1748, "y": 0, "width": 57, "height": 52, "left": 1748, "top": 0, "right": 1805, "bottom": 52, "topLeft": {"x": 1748, "y": 0}, "topRight": {"x": 1805, "y": 0}, "bottomLeft": {"x": 1748, "y": 52}, "bottomRight": {"x": 1805, "y": 52}}, "children": []}, {"name": "Rest<PERSON>", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1805, "y": 0}, "size": {"isEmpty": false, "width": 58, "height": 52}, "x": 1805, "y": 0, "width": 58, "height": 52, "left": 1805, "top": 0, "right": 1863, "bottom": 52, "topLeft": {"x": 1805, "y": 0}, "topRight": {"x": 1863, "y": 0}, "bottomLeft": {"x": 1805, "y": 52}, "bottomRight": {"x": 1863, "y": 52}}, "children": []}, {"name": "Close", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1862, "y": 0}, "size": {"isEmpty": false, "width": 58, "height": 52}, "x": 1862, "y": 0, "width": 58, "height": 52, "left": 1862, "top": 0, "right": 1920, "bottom": 52, "topLeft": {"x": 1862, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 1862, "y": 52}, "bottomRight": {"x": 1920, "y": 52}}, "children": []}]}, {"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": [{"name": "", "controlType": "ControlType.Document", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": null, "children": []}]}, {"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 0, "y": 0}, "size": {"isEmpty": false, "width": 1920, "height": 1079}, "x": 0, "y": 0, "width": 1920, "height": 1079, "left": 0, "top": 0, "right": 1920, "bottom": 1079, "topLeft": {"x": 0, "y": 0}, "topRight": {"x": 1920, "y": 0}, "bottomLeft": {"x": 0, "y": 1079}, "bottomRight": {"x": 1920, "y": 1079}}, "children": []}]}]}]}]}]}]}
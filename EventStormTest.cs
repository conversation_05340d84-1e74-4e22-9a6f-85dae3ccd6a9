using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Automation;

/// <summary>
/// Test program to simulate an event storm and analyze daemon resilience.
/// Creates thousands of rapid UI events to test UiaEventMonitor queue overflow.
/// </summary>
class EventStormTest
{
    private static volatile int _eventCount = 0;
    private static volatile bool _stopTest = false;
    private static readonly Stopwatch _stopwatch = new Stopwatch();

    static async Task Main(string[] args)
    {
        Console.WriteLine("=== CognitiveDrive Event Storm Test ===");
        Console.WriteLine("This test simulates thousands of rapid UI events");
        Console.WriteLine("to analyze daemon resilience and queue overflow behavior.");
        Console.WriteLine();

        try
        {
            Console.WriteLine("Starting event storm simulation...");
            Console.WriteLine("Monitoring daemon log for CPU usage and queue overflow...");
            Console.WriteLine("Press any key to stop the test.");
            Console.WriteLine();

            _stopwatch.Start();

            // Start multiple event generation tasks
            var eventTasks = new Task[20];
            for (int i = 0; i < eventTasks.Length; i++)
            {
                int taskId = i;
                eventTasks[i] = Task.Run(() => GenerateEventStorm(taskId));
            }

            // Monitor performance
            var monitorTask = Task.Run(MonitorPerformance);

            // Wait for user input to stop
            Console.ReadKey();
            _stopTest = true;
            _stopwatch.Stop();

            // Wait for all tasks to complete
            await Task.WhenAll(eventTasks);
            await monitorTask;

            Console.WriteLine();
            Console.WriteLine("=== Event Storm Test Results ===");
            Console.WriteLine($"Total events generated: {_eventCount:N0}");
            Console.WriteLine($"Test duration: {_stopwatch.Elapsed.TotalSeconds:F2} seconds");
            Console.WriteLine($"Events per second: {_eventCount / _stopwatch.Elapsed.TotalSeconds:F0}");
            Console.WriteLine();
            Console.WriteLine("Check daemon log for:");
            Console.WriteLine("- CPU usage spikes");
            Console.WriteLine("- Memory pressure");
            Console.WriteLine("- Event queue overflow");
            Console.WriteLine("- Recovery behavior");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    static void GenerateEventStorm(int taskId)
    {
        var random = new Random(taskId);
        
        while (!_stopTest)
        {
            try
            {
                // Simulate rapid UI changes that would trigger UIA events
                // We can't directly fire UIA events, but we can simulate the load
                // by creating rapid window focus changes and UI manipulations
                
                // Create a temporary window to generate events
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "notepad.exe",
                        WindowStyle = ProcessWindowStyle.Minimized,
                        CreateNoWindow = false
                    }
                };

                process.Start();
                
                // Wait briefly for window to appear
                Thread.Sleep(random.Next(10, 50));
                
                // Close the process to generate structure change events
                if (!process.HasExited)
                {
                    process.Kill();
                    process.WaitForExit(1000);
                }
                
                process.Dispose();
                
                Interlocked.Increment(ref _eventCount);
                
                // Brief pause to prevent system overload
                Thread.Sleep(random.Next(1, 10));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Task {taskId}] Event generation error: {ex.Message}");
                Thread.Sleep(100); // Back off on error
            }
        }
    }

    static async Task MonitorPerformance()
    {
        var lastEventCount = 0;
        var lastTime = DateTime.UtcNow;

        while (!_stopTest)
        {
            await Task.Delay(1000); // Check every second

            var currentTime = DateTime.UtcNow;
            var currentEventCount = _eventCount;
            var eventsPerSecond = (currentEventCount - lastEventCount) / (currentTime - lastTime).TotalSeconds;

            Console.WriteLine($"[{currentTime:HH:mm:ss}] Events/sec: {eventsPerSecond:F0}, Total: {currentEventCount:N0}");

            // Check system performance
            var currentProcess = Process.GetCurrentProcess();
            var memoryMB = currentProcess.WorkingSet64 / (1024 * 1024);
            var cpuTime = currentProcess.TotalProcessorTime;

            Console.WriteLine($"[{currentTime:HH:mm:ss}] Memory: {memoryMB}MB, CPU Time: {cpuTime.TotalSeconds:F1}s");

            lastEventCount = currentEventCount;
            lastTime = currentTime;
        }
    }
}

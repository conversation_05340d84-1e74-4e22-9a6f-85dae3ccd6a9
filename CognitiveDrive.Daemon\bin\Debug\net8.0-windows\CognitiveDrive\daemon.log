[2025-07-20 00:10:16.895] Starting CognitiveDrive Daemon service...
[2025-07-20 00:10:17.601] UIA Event Monitor started successfully.
[2025-07-20 00:10:17.602] Performing initial desktop scan...
[2025-07-20 00:10:17.673] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:18.087] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:18.094] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:18.445] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:18.445] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:18.801] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:18.802] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:19.153] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:19.153] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:19.566] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:19.566] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:19.899] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:19.900] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:20.358] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:20.359] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:20.922] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:20.923] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:21.360] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:21.361] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:21.811] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:21.811] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:22.223] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:22.224] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:22.679] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:22.680] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:23.120] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:23.121] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:23.592] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:23.593] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:24.054] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:24.056] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:24.526] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:24.527] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:25.252] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:25.252] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:25.794] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:25.795] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:26.340] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:26.341] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:26.870] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:26.870] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:27.602] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:27.603] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:28.176] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:28.176] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:28.892] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:10:28.892] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:10:30.879] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 358
[2025-07-20 00:10:30.883] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:11:49.971] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 15359
[2025-07-20 00:11:49.975] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:11:51.411] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:51.413] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:52.406] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:52.407] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:53.254] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:53.255] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:54.197] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:54.198] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:55.128] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:55.129] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:56.032] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:56.033] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:56.974] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:56.975] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:58.001] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:58.075] Structure changed event received. Type: ChildAdded
[2025-07-20 00:11:59.155] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:11:59.157] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:00.150] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:00.151] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:01.045] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:04.418] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:05.107] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:05.109] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:05.743] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:05.744] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:06.490] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:06.491] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:07.476] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:07.477] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:10.268] Live HET updated. Window: Can I set up a startup in SF and incorporate a office in India and I will stay... - Comet, Elements: 63
[2025-07-20 00:12:10.269] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:10.915] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:10.915] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:11.609] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:11.610] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:12.290] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:12.290] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:12.974] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:12.974] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:13.653] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:13.653] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:14.314] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:14.315] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:14.984] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:14.985] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:15.638] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:15.638] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:16.326] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:16.327] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:17.014] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:17.015] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:17.830] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:17.830] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:18.569] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:18.569] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:19.490] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:19.494] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:20.399] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:20.400] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:21.136] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:21.137] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:21.839] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:21.841] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:22.571] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:22.571] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:23.467] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:23.468] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:24.341] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:24.341] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:25.211] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:25.212] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:26.074] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:26.075] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:26.931] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:26.932] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:27.833] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:27.834] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:28.516] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:28.516] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:29.192] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:29.193] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.095] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:30.096] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.856] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:12:30.856] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.866] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.880] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.892] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.904] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.914] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.927] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:30.940] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:12:31.012] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:14.408] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 15359
[2025-07-20 00:14:14.411] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:15.648] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:15.650] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:17.071] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:17.073] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:18.381] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:18.385] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:19.813] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:19.814] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:21.150] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:21.152] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:22.423] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:22.424] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:23.526] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:23.528] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:24.708] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:24.709] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:26.033] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:26.034] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:27.159] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:27.160] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:28.083] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:28.084] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:28.996] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:28.997] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:29.954] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:29.955] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:31.033] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:31.034] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:31.931] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:31.932] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:32.802] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:32.803] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:33.668] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:33.669] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:34.598] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:34.598] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:35.424] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:35.425] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:36.257] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:36.258] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:37.095] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:37.096] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:37.924] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:37.925] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:38.857] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:38.858] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:39.899] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:39.899] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:40.707] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:40.707] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:41.593] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:41.594] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:42.783] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:42.784] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:43.937] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:43.938] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:45.085] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:45.087] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:46.353] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:46.355] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:47.233] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:47.234] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:48.065] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:48.066] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:48.864] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:48.865] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:14:50.542] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:14:50.544] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:35.951] Starting CognitiveDrive Daemon service...
[2025-07-20 00:15:36.282] UIA Event Monitor started successfully.
[2025-07-20 00:15:36.284] Performing initial active window scan...
[2025-07-20 00:15:36.308] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:36.637] Initial scan completed. Window: Project - Visual Studio Code, Found 16 elements.
[2025-07-20 00:15:36.637] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:36.638] Starting Named Pipe server...
[2025-07-20 00:15:36.640] Daemon service started successfully.
[2025-07-20 00:15:36.643] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:36.643] Starting Named Pipe server...
[2025-07-20 00:15:36.646] Waiting for client connection...
[2025-07-20 00:15:36.951] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:36.952] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:37.302] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:37.303] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:37.647] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:37.647] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:37.991] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:37.991] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:38.398] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:38.400] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:38.674] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:38.674] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:39.147] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:39.148] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:39.999] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:40.001] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:42.699] Live HET updated. Window: Can I set up a startup in SF and incorporate a office in India and I will stay... - Comet, Elements: 63
[2025-07-20 00:15:42.701] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:43.067] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:43.068] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:43.423] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:43.424] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:43.786] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:43.787] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:44.193] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:44.195] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:44.592] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:44.593] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:44.974] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:44.974] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:45.440] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:45.441] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:46.098] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:46.099] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:15:47.191] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:15:47.194] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:16:05.194] Client connected to Named Pipe.
[2025-07-20 00:16:08.399] Error sending HET to client: Pipe is broken.
[2025-07-20 00:16:08.400] Client disconnected from Named Pipe.
[2025-07-20 00:16:08.401] Waiting for client connection...
[2025-07-20 00:18:29.996] Live HET updated. Window: Best linux that is as close to windows and mac interms of features also which..., Elements: 4540
[2025-07-20 00:18:29.999] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:21.992] Starting CognitiveDrive Daemon service...
[2025-07-20 00:20:22.424] UIA Event Monitor started successfully.
[2025-07-20 00:20:22.426] Performing initial active window scan...
[2025-07-20 00:20:22.456] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:23.025] Initial scan completed. Window: Project - Visual Studio Code, Found 16 elements.
[2025-07-20 00:20:23.026] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:20:23.026] Starting Named Pipe server...
[2025-07-20 00:20:23.030] Daemon service started successfully.
[2025-07-20 00:20:23.032] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:23.033] Starting Named Pipe server...
[2025-07-20 00:20:23.035] Waiting for client connection...
[2025-07-20 00:20:23.490] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:20:23.491] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:23.954] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:20:23.955] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:24.414] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:20:24.415] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:24.878] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:20:24.879] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:25.287] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:20:25.288] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:20:26.169] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:20:26.170] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:21:40.796] Client connected to Named Pipe.
[2025-07-20 00:21:44.049] Error sending HET to client: Pipe is broken.
[2025-07-20 00:21:44.050] Client disconnected from Named Pipe.
[2025-07-20 00:21:44.052] Waiting for client connection...
[2025-07-20 00:21:54.310] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 15897
[2025-07-20 00:21:54.331] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:22:57.811] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 15897
[2025-07-20 00:22:57.811] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:22:58.235] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:22:58.235] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:22:58.672] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:22:58.674] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:22:59.204] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:22:59.207] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:22:59.762] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:22:59.763] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:00.211] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:00.212] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:00.727] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:00.728] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:01.202] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:01.203] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:01.636] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:01.637] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:02.075] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:02.075] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:02.528] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:02.529] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:03.072] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:03.073] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:03.631] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:03.632] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:04.117] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:04.117] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:04.635] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:04.636] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:05.346] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:05.349] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:06.218] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:06.220] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:06.814] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:06.815] Structure changed event received. Type: ChildAdded
[2025-07-20 00:23:07.440] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:07.441] Structure changed event received. Type: ChildAdded
[2025-07-20 00:23:08.056] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:08.057] Structure changed event received. Type: ChildAdded
[2025-07-20 00:23:08.682] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:08.683] Structure changed event received. Type: ChildAdded
[2025-07-20 00:23:09.326] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:09.327] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:09.781] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:09.783] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:10.240] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:10.758] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:11.345] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:11.346] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:11.796] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:11.797] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:12.320] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:12.321] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:12.841] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:12.842] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:13.260] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:13.261] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:13.715] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:13.716] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.184] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:14.184] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.649] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:23:14.650] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.665] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.673] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.683] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.691] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.705] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.720] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.734] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.745] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.759] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.776] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.792] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.808] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.819] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:23:14.901] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:25:26.147] Live HET updated. Window: Best linux that is as close to windows and mac interms of features also which..., Elements: 4540
[2025-07-20 00:25:26.321] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:26:52.781] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 16432
[2025-07-20 00:26:52.785] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:27:51.654] Client connected to Named Pipe.
[2025-07-20 00:27:51.791] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:27:52.809] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:27:53.859] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:27:54.879] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:27:55.944] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:27:57.132] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:27:58.322] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:27:59.348] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:00.361] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:01.376] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:02.389] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:03.522] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:04.898] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:05.916] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:06.966] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:07.992] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:09.016] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:10.044] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:11.072] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:12.087] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:13.151] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:14.324] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:15.340] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:16.377] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:17.394] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:18.408] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:19.452] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:20.470] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:21.488] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:22.503] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:23.533] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:24.590] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:25.652] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:26.685] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:27.813] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:28.873] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:29.966] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:31.179] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:32.212] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:33.237] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:34.256] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:35.270] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:36.283] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:37.296] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:38.518] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:39.523] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:40.536] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:41.558] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:42.569] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:43.584] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:44.684] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:45.696] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:46.708] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:47.723] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:48.736] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:49.753] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:50.791] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:51.798] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:52.822] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:53.894] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:54.920] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:55.935] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:56.951] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:57.967] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:28:58.998] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:00.017] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:00.819] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 16432
[2025-07-20 00:29:00.859] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:29:01.065] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:02.170] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:03.193] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:04.213] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:05.234] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:06.256] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:07.279] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:08.299] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:09.326] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:10.335] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:11.347] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:12.361] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:13.382] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:14.770] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:16.172] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:17.251] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:18.261] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:19.278] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:20.296] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:21.323] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:22.406] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:23.581] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:24.589] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:25.617] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:26.638] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:27.645] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:28.660] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:29.674] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:30.688] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:31.732] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:32.744] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:33.754] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:34.769] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:35.787] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:36.802] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:37.829] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:39.649] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:40.667] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:41.680] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:42.690] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:43.707] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:44.717] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:45.741] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:46.753] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:47.780] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:48.919] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:50.104] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:51.131] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:52.145] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:53.162] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:54.177] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:55.216] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:56.230] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:57.240] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:58.256] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:29:59.268] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:00.284] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:01.379] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:02.407] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:03.426] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:04.446] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:05.465] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:06.479] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:07.497] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:08.510] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:09.524] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:10.529] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:11.544] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:12.605] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:13.833] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:14.882] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:15.953] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:17.009] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:18.030] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:19.049] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:20.065] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:21.075] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:22.088] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:23.112] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:24.121] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:25.130] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:26.163] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:27.181] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:28.193] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:29.206] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:30.217] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:31.233] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:32.250] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:33.261] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:34.269] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:35.415] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:36.550] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:37.570] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:38.581] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:39.601] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:40.621] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:41.635] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:42.647] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:43.667] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:44.687] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:45.706] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:46.718] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:47.737] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:48.751] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:49.757] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:50.774] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:51.786] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:52.802] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:53.811] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:54.883] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:56.039] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:57.055] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:58.070] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:30:59.090] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:00.106] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:01.140] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:02.161] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:02.988] Live HET updated. Window: (1) Instagram and 7 more pages - Personal - Microsoft​ Edge, Elements: 16971
[2025-07-20 00:31:02.990] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:31:03.175] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:04.181] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:05.194] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:06.207] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:07.224] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:08.252] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:09.270] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:10.288] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:11.308] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:12.322] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:13.395] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:14.598] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:15.741] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:17.065] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:18.458] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:20.012] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:21.756] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:23.263] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:24.711] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:25.986] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:27.292] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:28.777] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:30.071] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:31.284] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:32.528] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:33.747] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:34.952] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:36.162] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:37.353] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:38.566] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:39.717] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:40.955] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:42.129] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:43.296] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:44.805] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:46.119] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:47.308] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:48.525] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:49.751] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:50.929] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:52.119] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:53.430] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:55.011] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:56.274] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:57.468] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:58.554] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:31:59.749] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:00.858] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:01.961] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:03.049] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:04.257] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:05.708] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:07.095] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:08.612] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:10.307] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:11.872] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:13.334] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:15.038] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:16.443] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:17.634] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:18.844] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:20.199] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:21.427] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:22.697] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:24.012] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:25.322] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:26.890] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:28.730] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:30.136] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:31.433] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:32.633] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:33.670] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:34.742] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:35.929] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:37.068] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:38.133] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:39.165] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:40.281] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:41.351] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:42.388] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:43.473] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:44.503] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:45.560] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:46.598] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:47.643] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:48.704] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:49.776] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:50.847] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:51.909] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:52.946] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:53.980] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:55.048] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:56.119] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:57.178] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:58.221] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:32:59.256] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:00.291] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:01.323] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:02.375] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:03.456] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:04.518] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:05.580] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:06.604] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:07.722] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:08.754] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:09.785] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:10.828] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:11.856] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:12.955] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:13.969] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:15.100] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:16.685] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:17.997] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:19.134] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:20.226] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:21.268] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:22.307] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:23.361] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:24.428] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:25.457] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:26.492] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:27.622] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:28.652] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:29.716] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:30.727] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:31.745] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:32.756] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:33.776] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:34.789] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:35.799] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:36.809] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:37.821] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:38.835] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:39.864] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:33:40.881] Error sending HET to client: A possible object cycle was detected. This can either be due to a cycle or if the object depth is larger than the maximum allowed depth of 64. Consider using ReferenceHandler.Preserve on JsonSerializerOptions to support cycles. Path: $.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.Children.
[2025-07-20 00:35:22.416] Starting CognitiveDrive Daemon service...
[2025-07-20 00:35:22.834] UIA Event Monitor started successfully.
[2025-07-20 00:35:22.837] Performing initial active window scan...
[2025-07-20 00:35:22.870] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:35:23.495] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:35:23.497] Initial scan completed. Window: Project - Visual Studio Code, Found 16 elements.
[2025-07-20 00:35:23.497] Starting Named Pipe server...
[2025-07-20 00:35:23.505] Daemon service started successfully.
[2025-07-20 00:35:23.512] Starting Named Pipe server...
[2025-07-20 00:35:23.515] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:35:23.521] Waiting for client connection...
[2025-07-20 00:35:24.137] Stopping CognitiveDrive Daemon service...
[2025-07-20 00:35:24.166] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:35:24.183] Named Pipe server stopped.
[2025-07-20 00:35:28.188] Daemon service stopped successfully.
[2025-07-20 00:37:36.023] Starting CognitiveDrive Daemon service...
[2025-07-20 00:37:36.272] UIA Event Monitor started successfully.
[2025-07-20 00:37:36.273] Performing initial active window scan...
[2025-07-20 00:37:36.297] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:36.720] Initial scan completed. Window: Project - Visual Studio Code, Found 16 elements.
[2025-07-20 00:37:36.720] Starting Named Pipe server...
[2025-07-20 00:37:36.724] Daemon service started successfully.
[2025-07-20 00:37:36.727] Starting Named Pipe server...
[2025-07-20 00:37:36.729] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:36.730] Waiting for client connection...
[2025-07-20 00:37:37.027] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:37.027] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:37.364] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:37.364] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:37.670] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:37.671] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:37.710] Stopping CognitiveDrive Daemon service...
[2025-07-20 00:37:37.732] Named Pipe server stopped.
[2025-07-20 00:37:38.073] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:41.350] Starting CognitiveDrive Daemon service...
[2025-07-20 00:37:41.507] UIA Event Monitor started successfully.
[2025-07-20 00:37:41.509] Performing initial active window scan...
[2025-07-20 00:37:41.524] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:41.838] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:41.838] Initial scan completed. Window: Project - Visual Studio Code, Found 16 elements.
[2025-07-20 00:37:41.838] Starting Named Pipe server...
[2025-07-20 00:37:41.841] Daemon service started successfully.
[2025-07-20 00:37:41.842] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:41.843] Starting Named Pipe server...
[2025-07-20 00:37:41.845] Waiting for client connection...
[2025-07-20 00:37:42.075] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:42.075] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:42.089] Daemon service stopped successfully.
[2025-07-20 00:37:42.327] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:42.327] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:42.583] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:42.584] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:42.834] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:42.835] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:43.105] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:43.106] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:43.340] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:43.340] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:43.596] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:43.597] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:43.838] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:43.838] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:44.105] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:44.106] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:44.460] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:44.480] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:44.766] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:44.766] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:45.007] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:45.008] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:45.394] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:45.397] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:45.652] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:45.653] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:46.162] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:46.163] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:46.483] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:46.483] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:47.086] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:47.086] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:47.451] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:47.452] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:47.725] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:47.725] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:48.015] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:48.016] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:48.855] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:37:48.856] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:53.246] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1105
[2025-07-20 00:37:53.247] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:37:57.126] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:37:57.127] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:00.398] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:38:00.399] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:04.820] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:38:04.822] Structure changed event received. Type: ChildAdded
[2025-07-20 00:38:09.644] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:38:09.645] Structure changed event received. Type: ChildAdded
[2025-07-20 00:38:09.702] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 2
[2025-07-20 00:38:09.702] Structure changed event received. Type: ChildAdded
[2025-07-20 00:38:12.418] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1098
[2025-07-20 00:38:12.418] Structure changed event received. Type: ChildAdded
[2025-07-20 00:38:12.848] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:12.849] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:13.256] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:13.257] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:13.645] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:13.848] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:14.241] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:14.242] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:14.579] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:14.579] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:14.933] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:14.933] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:15.373] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:15.374] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:15.828] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:15.829] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:16.514] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:16.514] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:17.119] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:17.121] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:17.718] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:17.719] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:18.203] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:18.204] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:18.705] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:18.705] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:19.322] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:19.323] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:20.154] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:20.155] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:20.681] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:20.682] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:21.179] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:21.180] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:21.623] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:21.623] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:22.103] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:22.104] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:22.552] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:22.552] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:22.703] Client connected to Named Pipe.
[2025-07-20 00:38:23.210] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:23.211] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:23.765] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:23.766] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:24.359] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:24.361] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:24.774] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:24.775] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:25.251] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:25.253] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:25.673] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:25.674] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:26.159] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:26.159] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:26.646] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:26.647] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:27.056] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:27.057] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:27.421] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:27.422] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:27.911] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:27.912] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:28.266] Error sending HET to client: Pipe is broken.
[2025-07-20 00:38:28.268] Client disconnected from Named Pipe.
[2025-07-20 00:38:28.269] Waiting for client connection...
[2025-07-20 00:38:28.366] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:28.367] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:28.862] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:28.863] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:29.232] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:29.233] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:29.597] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:29.598] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:30.041] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:30.042] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:30.430] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:30.430] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:30.904] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:30.924] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:31.457] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:31.457] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:31.915] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:31.916] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:32.312] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:32.313] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:32.736] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:32.737] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:33.233] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:33.234] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:33.672] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:33.673] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:34.133] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:34.133] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:34.554] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:34.554] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:34.982] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:34.983] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:35.374] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:35.375] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:35.771] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:35.772] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:36.183] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:36.184] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:36.560] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:36.561] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:36.942] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:36.943] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:37.390] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:37.391] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:37.808] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:37.809] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:38.321] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:38.322] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:38.754] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:38.755] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:39.247] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:39.248] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:39.675] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:39.676] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:40.100] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:40.100] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:40.510] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:40.511] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:40.976] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:40.977] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:41.432] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:41.433] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:41.834] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:41.835] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:42.307] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:42.309] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:42.718] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:42.719] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:43.170] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:43.171] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:43.536] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:43.537] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:43.900] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:43.901] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:44.305] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:44.306] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:44.693] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:44.694] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:45.156] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:45.156] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:45.530] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:45.531] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:46.229] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:46.230] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:46.981] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:46.991] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:50.975] Live HET updated. Window: Google Gemini and 6 more pages - Personal - Microsoft​ Edge, Elements: 1095
[2025-07-20 00:38:50.976] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:55.758] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:38:55.760] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:59.407] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:38:59.408] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:38:59.757] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:38:59.758] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:00.098] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:00.099] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:00.478] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:00.479] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:00.853] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:00.854] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:01.206] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:01.206] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:01.562] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:01.562] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:01.998] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:01.999] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:02.521] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:02.522] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:02.938] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:02.939] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:03.416] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:03.417] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:04.091] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:04.092] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:04.517] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:04.518] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:05.061] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:05.062] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:05.492] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:05.493] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:05.898] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:05.899] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:06.281] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:06.282] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:06.757] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:06.758] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:07.178] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:07.179] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:07.627] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:07.628] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.110] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:08.112] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.119] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.125] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.135] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.141] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.148] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.155] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.161] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.168] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.173] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.180] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.186] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.201] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.212] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:08.299] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:14.311] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1098
[2025-07-20 00:39:14.312] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:19.792] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1098
[2025-07-20 00:39:19.792] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:24.524] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1098
[2025-07-20 00:39:24.525] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:29.248] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:39:29.251] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:34.380] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:39:34.384] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:40.563] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:39:40.564] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:45.096] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:39:45.100] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:48.899] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1097
[2025-07-20 00:39:48.900] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:53.619] Live HET updated. Window: (368) A Billion Dollars Scene | The Social Network - YouTube and 6 more pages - Personal - Microsoft​ Edge, Elements: 1098
[2025-07-20 00:39:53.620] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:54.072] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:54.073] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:54.489] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:54.489] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:54.866] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:54.867] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:55.238] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:55.239] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:55.622] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:55.623] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:56.007] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:56.008] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:56.472] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:56.473] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:56.924] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:56.925] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:57.417] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:57.418] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:58.084] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:58.085] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:58.605] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:58.605] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:59.166] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:59.166] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:39:59.696] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:39:59.697] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:00.200] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:00.201] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:00.807] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:00.809] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:01.312] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:01.313] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:01.865] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:01.866] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:02.403] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:02.404] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:02.902] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:02.903] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:03.396] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:03.397] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:03.907] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:03.908] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:04.407] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:04.408] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:04.912] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:04.912] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:05.455] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:05.456] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:06.035] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:06.037] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:06.590] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:06.590] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:07.119] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:07.120] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:07.641] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:07.642] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:08.196] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:08.197] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:08.782] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:08.783] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:09.466] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:09.467] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:09.965] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:09.966] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:10.752] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:10.754] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:11.720] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:11.722] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:12.405] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:12.406] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:13.118] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:13.119] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:13.799] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:13.800] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:14.546] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:14.547] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:15.164] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:15.165] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:15.798] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:15.799] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:16.871] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:16.872] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:17.702] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:17.703] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:18.507] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:18.508] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:19.234] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:19.234] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:19.924] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:19.925] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:20.651] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:20.652] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:21.358] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:21.359] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:22.059] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:22.059] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:22.808] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:22.809] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:23.642] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:23.643] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:24.432] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:24.434] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:25.131] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:25.132] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:25.752] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:25.753] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:26.278] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:26.279] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:26.792] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:26.793] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:27.446] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:27.447] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:28.246] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:28.247] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:28.993] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:28.995] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:29.674] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:29.680] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:30.359] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:30.360] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:31.030] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:31.031] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:31.585] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:31.586] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:32.052] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:32.053] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:33.677] Live HET updated. Window: Project - Visual Studio Code, Elements: 16
[2025-07-20 00:40:33.678] Structure changed event received. Type: ChildRemoved
[2025-07-20 00:40:57.813] Stopping CognitiveDrive Daemon service...
[2025-07-20 00:40:57.832] Named Pipe server stopped.

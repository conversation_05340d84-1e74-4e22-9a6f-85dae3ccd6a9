﻿using System.Collections.Concurrent;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Automation;
using CognitiveDrive.Infrastructure;

/// <summary>
/// THE CRUCIBLE - Suite 2: The Chaos Test (Advanced Event Storm)
/// This test stress-tests the UiaEventMonitor under high-frequency, chaotic conditions.
/// </summary>
class ChaosTest
{
    [DllImport("user32.dll")]
    private static extern bool SetForegroundWindow(IntPtr hWnd);

    [DllImport("user32.dll")]
    private static extern IntPtr GetForegroundWindow();

    private static readonly ConcurrentQueue<EventRecord> _eventLog = new();
    private static volatile bool _testRunning = false;
    private static long _eventCount = 0;
    private static readonly Stopwatch _testTimer = new();

    static async Task Main(string[] args)
    {
        Console.WriteLine("⚡ THE CRUCIBLE - SUITE 2: THE CHAOS TEST");
        Console.WriteLine("=========================================");
        Console.WriteLine("Advanced Event Storm - UiaEventMonitor Stress Test\n");

        // Initialize event monitor
        var eventMonitor = UiaEventMonitor.Instance;

        // Subscribe to focus change events with high-resolution logging
        eventMonitor.FocusChanged += OnFocusChanged;

        Console.WriteLine("🎯 Starting UiaEventMonitor...");
        eventMonitor.Start();

        // Get available windows for chaos testing
        var targetWindows = GetTargetWindows();
        if (targetWindows.Count < 3)
        {
            Console.WriteLine($"❌ Need at least 3 windows for chaos test, found {targetWindows.Count}");
            Console.WriteLine("Please open more applications and try again.");
            return;
        }

        Console.WriteLine($"🎯 Found {targetWindows.Count} target windows for chaos test:");
        foreach (var (handle, title) in targetWindows)
        {
            Console.WriteLine($"   - {title} (Handle: {handle})");
        }

        // Start monitoring system resources
        var initialMemory = GC.GetTotalMemory(false);
        var process = Process.GetCurrentProcess();
        var initialCpuTime = process.TotalProcessorTime;

        Console.WriteLine($"\n🔥 INITIATING 60-SECOND CHAOS EVENT STORM...");
        Console.WriteLine($"Initial Memory: {initialMemory / 1024 / 1024:F2} MB");
        Console.WriteLine($"Initial CPU Time: {initialCpuTime.TotalMilliseconds:F2} ms");

        // Start the chaos!
        _testRunning = true;
        _testTimer.Start();

        var chaosTask = Task.Run(() => ChaosLoop(targetWindows));

        // Monitor for 60 seconds
        await Task.Delay(60000);

        // Stop the chaos
        _testRunning = false;
        _testTimer.Stop();
        await chaosTask;

        // Stop event monitoring
        eventMonitor.Stop();
        eventMonitor.FocusChanged -= OnFocusChanged;

        // Analyze results
        var finalMemory = GC.GetTotalMemory(true); // Force GC
        process.Refresh();
        var finalCpuTime = process.TotalProcessorTime;

        Console.WriteLine($"\n📊 CHAOS TEST RESULTS:");
        Console.WriteLine($"   - Test Duration: {_testTimer.Elapsed.TotalSeconds:F2} seconds");
        Console.WriteLine($"   - Total Events Captured: {_eventCount}");
        Console.WriteLine($"   - Events Per Second: {_eventCount / _testTimer.Elapsed.TotalSeconds:F2}");
        Console.WriteLine($"   - Memory Usage: {initialMemory / 1024 / 1024:F2} MB → {finalMemory / 1024 / 1024:F2} MB");
        Console.WriteLine($"   - Memory Delta: {(finalMemory - initialMemory) / 1024 / 1024:F2} MB");
        Console.WriteLine($"   - CPU Time Delta: {(finalCpuTime - initialCpuTime).TotalMilliseconds:F2} ms");

        // Check for dropped events by analyzing timestamps
        var events = _eventLog.ToArray();
        var droppedEvents = AnalyzeEventDrops(events);

        Console.WriteLine($"   - Potential Dropped Events: {droppedEvents}");
        Console.WriteLine($"   - Event Capture Rate: {((double)_eventCount / (_eventCount + droppedEvents)) * 100:F2}%");

        // Performance verdict
        var memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024;
        var eventsPerSecond = _eventCount / _testTimer.Elapsed.TotalSeconds;

        Console.WriteLine($"\n🎯 CHAOS TEST VERDICT:");
        if (memoryIncrease < 50 && eventsPerSecond > 10 && droppedEvents < _eventCount * 0.05)
        {
            Console.WriteLine("✅ PASSED: UiaEventMonitor survived the chaos!");
            Console.WriteLine("   - Memory usage remained stable");
            Console.WriteLine("   - Event capture rate was excellent");
            Console.WriteLine("   - System remained responsive");
        }
        else
        {
            Console.WriteLine("❌ FAILED: UiaEventMonitor showed instability");
            if (memoryIncrease >= 50) Console.WriteLine("   - Excessive memory usage detected");
            if (eventsPerSecond <= 10) Console.WriteLine("   - Low event capture rate");
            if (droppedEvents >= _eventCount * 0.05) Console.WriteLine("   - High event drop rate");
        }

        eventMonitor.Dispose();
    }

    private static void OnFocusChanged(object sender, AutomationFocusChangedEventArgs e)
    {
        var timestamp = _testTimer.Elapsed;
        var eventRecord = new EventRecord
        {
            Timestamp = timestamp,
            ThreadId = Thread.CurrentThread.ManagedThreadId,
            EventType = "FocusChanged"
        };

        _eventLog.Enqueue(eventRecord);
        Interlocked.Increment(ref _eventCount);
    }

    private static async Task ChaosLoop(List<(IntPtr handle, string title)> windows)
    {
        var random = new Random();
        var switchCount = 0;

        while (_testRunning)
        {
            try
            {
                // Rapidly cycle through windows
                var targetWindow = windows[random.Next(windows.Count)];
                SetForegroundWindow(targetWindow.handle);
                switchCount++;

                // Vary the timing to create chaos
                var delay = random.Next(50, 200); // 50-200ms intervals
                await Task.Delay(delay);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chaos loop error: {ex.Message}");
            }
        }

        Console.WriteLine($"🔥 Chaos loop completed: {switchCount} window switches");
    }

    private static List<(IntPtr handle, string title)> GetTargetWindows()
    {
        var windows = new List<(IntPtr handle, string title)>();

        try
        {
            var processes = Process.GetProcesses()
                .Where(p => p.MainWindowHandle != IntPtr.Zero && !string.IsNullOrEmpty(p.MainWindowTitle))
                .Take(8) // Limit to 8 windows for manageable chaos
                .ToList();

            foreach (var process in processes)
            {
                windows.Add((process.MainWindowHandle, process.MainWindowTitle));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting windows: {ex.Message}");
        }

        return windows;
    }

    private static int AnalyzeEventDrops(EventRecord[] events)
    {
        if (events.Length < 2) return 0;

        // Look for gaps in event timing that might indicate drops
        var droppedCount = 0;
        var sortedEvents = events.OrderBy(e => e.Timestamp).ToArray();

        for (int i = 1; i < sortedEvents.Length; i++)
        {
            var timeDiff = sortedEvents[i].Timestamp - sortedEvents[i - 1].Timestamp;

            // If gap is > 500ms, might indicate dropped events
            if (timeDiff.TotalMilliseconds > 500)
            {
                droppedCount++;
            }
        }

        return droppedCount;
    }
}

public class EventRecord
{
    public TimeSpan Timestamp { get; set; }
    public int ThreadId { get; set; }
    public string EventType { get; set; } = string.Empty;
}

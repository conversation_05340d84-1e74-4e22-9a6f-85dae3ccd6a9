{"name": "Untitled - Notepad", "controlType": "ControlType.Window", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 120, "y": 120}, "size": {"isEmpty": false, "width": 679, "height": 440}, "x": 120, "y": 120, "width": 679, "height": 440, "left": 120, "top": 120, "right": 799, "bottom": 560, "topLeft": {"x": 120, "y": 120}, "topRight": {"x": 799, "y": 120}, "bottomLeft": {"x": 120, "y": 560}, "bottomRight": {"x": 799, "y": 560}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 128, "y": 213}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 128, "y": 213, "width": 663, "height": 299, "left": 128, "top": 213, "right": 791, "bottom": 512, "topLeft": {"x": 128, "y": 213}, "topRight": {"x": 791, "y": 213}, "bottomLeft": {"x": 128, "y": 512}, "bottomRight": {"x": 791, "y": 512}}, "children": [{"name": "Text editor", "controlType": "ControlType.Document", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 128, "y": 213}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 128, "y": 213, "width": 663, "height": 299, "left": 128, "top": 213, "right": 791, "bottom": 512, "topLeft": {"x": 128, "y": 213}, "topRight": {"x": 791, "y": 213}, "bottomLeft": {"x": 128, "y": 512}, "bottomRight": {"x": 791, "y": 512}}, "children": []}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 178, "y": 132}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 178, "y": 132, "width": 359, "height": 40, "left": 178, "top": 132, "right": 537, "bottom": 172, "topLeft": {"x": 178, "y": 132}, "topRight": {"x": 537, "y": 132}, "bottomLeft": {"x": 178, "y": 172}, "bottomRight": {"x": 537, "y": 172}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 178, "y": 132}, "size": {"isEmpty": false, "width": 287, "height": 32}, "x": 178, "y": 132, "width": 287, "height": 32, "left": 178, "top": 132, "right": 465, "bottom": 164, "topLeft": {"x": 178, "y": 132}, "topRight": {"x": 465, "y": 132}, "bottomLeft": {"x": 178, "y": 164}, "bottomRight": {"x": 465, "y": 164}}, "children": [{"name": "", "controlType": "ControlType.Tab", "automationId": "Tabs", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 178, "y": 132}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 178, "y": 132, "width": 359, "height": 40, "left": 178, "top": 132, "right": 537, "bottom": 172, "topLeft": {"x": 178, "y": 132}, "topRight": {"x": 537, "y": 132}, "bottomLeft": {"x": 178, "y": 172}, "bottomRight": {"x": 537, "y": 172}}, "children": [{"name": "", "controlType": "ControlType.List", "automationId": "TabListView", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 180, "y": 132}, "size": {"isEmpty": false, "width": 312, "height": 40}, "x": 180, "y": 132, "width": 312, "height": 40, "left": 180, "top": 132, "right": 492, "bottom": 172, "topLeft": {"x": 180, "y": 132}, "topRight": {"x": 492, "y": 132}, "bottomLeft": {"x": 180, "y": 172}, "bottomRight": {"x": 492, "y": 172}}, "children": [{"name": "Untitled. Unmodified.", "controlType": "ControlType.TabItem", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 183, "y": 132}, "size": {"isEmpty": false, "width": 307, "height": 40}, "x": 183, "y": 132, "width": 307, "height": 40, "left": 183, "top": 132, "right": 490, "bottom": 172, "topLeft": {"x": 183, "y": 132}, "topRight": {"x": 490, "y": 132}, "bottomLeft": {"x": 183, "y": 172}, "bottomRight": {"x": 490, "y": 172}}, "children": [{"name": "Untitled", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 199, "y": 142}, "size": {"isEmpty": false, "width": 56, "height": 19}, "x": 199, "y": 142, "width": 56, "height": 19, "left": 199, "top": 142, "right": 255, "bottom": 161, "topLeft": {"x": 199, "y": 142}, "topRight": {"x": 255, "y": 142}, "bottomLeft": {"x": 199, "y": 161}, "bottomRight": {"x": 255, "y": 161}}, "children": []}, {"name": "Close Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "CloseButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 439, "y": 137}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 439, "y": 137, "width": 40, "height": 30, "left": 439, "top": 137, "right": 479, "bottom": 167, "topLeft": {"x": 439, "y": 137}, "topRight": {"x": 479, "y": 137}, "bottomLeft": {"x": 439, "y": 167}, "bottomRight": {"x": 479, "y": 167}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 452, "y": 144}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 452, "y": 144, "width": 15, "height": 15, "left": 452, "top": 144, "right": 467, "bottom": 159, "topLeft": {"x": 452, "y": 144}, "topRight": {"x": 467, "y": 144}, "bottomLeft": {"x": 452, "y": 159}, "bottomRight": {"x": 467, "y": 159}}, "children": []}]}]}]}, {"name": "Add New Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AddButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 497, "y": 138}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 497, "y": 138, "width": 40, "height": 30, "left": 497, "top": 138, "right": 537, "bottom": 168, "topLeft": {"x": 497, "y": 138}, "topRight": {"x": 537, "y": 138}, "bottomLeft": {"x": 497, "y": 168}, "bottomRight": {"x": 537, "y": 168}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 509, "y": 145}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 509, "y": 145, "width": 15, "height": 15, "left": 509, "top": 145, "right": 524, "bottom": 160, "topLeft": {"x": 509, "y": 145}, "topRight": {"x": 524, "y": 145}, "bottomLeft": {"x": 509, "y": 160}, "bottomRight": {"x": 524, "y": 160}}, "children": []}]}]}, {"name": "Notepad automatically saves your progress. All your content will be available the next time you open Notepad.", "controlType": "ControlType.Pane", "automationId": "TeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 128, "y": 172}, "size": {"isEmpty": false, "width": 663, "height": 41}, "x": 128, "y": 172, "width": 663, "height": 41, "left": 128, "top": 172, "right": 791, "bottom": 213, "topLeft": {"x": 128, "y": 172}, "topRight": {"x": 791, "y": 172}, "bottomLeft": {"x": 128, "y": 213}, "bottomRight": {"x": 791, "y": 213}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 128, "y": 172}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 128, "y": 172, "width": 530, "height": 32, "left": 128, "top": 172, "right": 658, "bottom": 204, "topLeft": {"x": 128, "y": 172}, "topRight": {"x": 658, "y": 172}, "bottomLeft": {"x": 128, "y": 204}, "bottomRight": {"x": 658, "y": 204}}, "children": [{"name": "", "controlType": "ControlType.MenuBar", "automationId": "<PERSON><PERSON><PERSON><PERSON>", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 128, "y": 172}, "size": {"isEmpty": false, "width": 199, "height": 40}, "x": 128, "y": 172, "width": 199, "height": 40, "left": 128, "top": 172, "right": 327, "bottom": 212, "topLeft": {"x": 128, "y": 172}, "topRight": {"x": 327, "y": 172}, "bottomLeft": {"x": 128, "y": 212}, "bottomRight": {"x": 327, "y": 212}}, "children": [{"name": "File", "controlType": "ControlType.MenuItem", "automationId": "File", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 133, "y": 172}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 133, "y": 172, "width": 52, "height": 40, "left": 133, "top": 172, "right": 185, "bottom": 212, "topLeft": {"x": 133, "y": 172}, "topRight": {"x": 185, "y": 172}, "bottomLeft": {"x": 133, "y": 212}, "bottomRight": {"x": 185, "y": 212}}, "children": [{"name": "File", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 133, "y": 172}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 133, "y": 172, "width": 52, "height": 40, "left": 133, "top": 172, "right": 185, "bottom": 212, "topLeft": {"x": 133, "y": 172}, "topRight": {"x": 185, "y": 172}, "bottomLeft": {"x": 133, "y": 212}, "bottomRight": {"x": 185, "y": 212}}, "children": [{"name": "File", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 145, "y": 180}, "size": {"isEmpty": false, "width": 27, "height": 24}, "x": 145, "y": 180, "width": 27, "height": 24, "left": 145, "top": 180, "right": 172, "bottom": 204, "topLeft": {"x": 145, "y": 180}, "topRight": {"x": 172, "y": 180}, "bottomLeft": {"x": 145, "y": 204}, "bottomRight": {"x": 172, "y": 204}}, "children": []}]}]}, {"name": "Edit", "controlType": "ControlType.MenuItem", "automationId": "Edit", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 195, "y": 172}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 195, "y": 172, "width": 55, "height": 40, "left": 195, "top": 172, "right": 250, "bottom": 212, "topLeft": {"x": 195, "y": 172}, "topRight": {"x": 250, "y": 172}, "bottomLeft": {"x": 195, "y": 212}, "bottomRight": {"x": 250, "y": 212}}, "children": [{"name": "Edit", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 195, "y": 172}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 195, "y": 172, "width": 55, "height": 40, "left": 195, "top": 172, "right": 250, "bottom": 212, "topLeft": {"x": 195, "y": 172}, "topRight": {"x": 250, "y": 172}, "bottomLeft": {"x": 195, "y": 212}, "bottomRight": {"x": 250, "y": 212}}, "children": [{"name": "Edit", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 208, "y": 180}, "size": {"isEmpty": false, "width": 30, "height": 24}, "x": 208, "y": 180, "width": 30, "height": 24, "left": 208, "top": 180, "right": 238, "bottom": 204, "topLeft": {"x": 208, "y": 180}, "topRight": {"x": 238, "y": 180}, "bottomLeft": {"x": 208, "y": 204}, "bottomRight": {"x": 238, "y": 204}}, "children": []}]}]}, {"name": "View", "controlType": "ControlType.MenuItem", "automationId": "View", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 260, "y": 172}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 260, "y": 172, "width": 62, "height": 40, "left": 260, "top": 172, "right": 322, "bottom": 212, "topLeft": {"x": 260, "y": 172}, "topRight": {"x": 322, "y": 172}, "bottomLeft": {"x": 260, "y": 212}, "bottomRight": {"x": 322, "y": 212}}, "children": [{"name": "View", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 260, "y": 172}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 260, "y": 172, "width": 62, "height": 40, "left": 260, "top": 172, "right": 322, "bottom": 212, "topLeft": {"x": 260, "y": 172}, "topRight": {"x": 322, "y": 172}, "bottomLeft": {"x": 260, "y": 212}, "bottomRight": {"x": 322, "y": 212}}, "children": [{"name": "View", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 272, "y": 180}, "size": {"isEmpty": false, "width": 37, "height": 24}, "x": 272, "y": 180, "width": 37, "height": 24, "left": 272, "top": 180, "right": 309, "bottom": 204, "topLeft": {"x": 272, "y": 180}, "topRight": {"x": 309, "y": 180}, "bottomLeft": {"x": 272, "y": 204}, "bottomRight": {"x": 309, "y": 204}}, "children": []}]}]}]}, {"name": "Headings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 363, "y": 172}, "size": {"isEmpty": false, "width": 71, "height": 40}, "x": 363, "y": 172, "width": 71, "height": 40, "left": 363, "top": 172, "right": 434, "bottom": 212, "topLeft": {"x": 363, "y": 172}, "topRight": {"x": 434, "y": 172}, "bottomLeft": {"x": 363, "y": 212}, "bottomRight": {"x": 434, "y": 212}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 377, "y": 182}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 377, "y": 182, "width": 18, "height": 18, "left": 377, "top": 182, "right": 395, "bottom": 200, "topLeft": {"x": 377, "y": 182}, "topRight": {"x": 395, "y": 182}, "bottomLeft": {"x": 377, "y": 200}, "bottomRight": {"x": 395, "y": 200}}, "children": []}]}, {"name": "Lists", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 434, "y": 172}, "size": {"isEmpty": false, "width": 70, "height": 40}, "x": 434, "y": 172, "width": 70, "height": 40, "left": 434, "top": 172, "right": 504, "bottom": 212, "topLeft": {"x": 434, "y": 172}, "topRight": {"x": 504, "y": 172}, "bottomLeft": {"x": 434, "y": 212}, "bottomRight": {"x": 504, "y": 212}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 448, "y": 182}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 448, "y": 182, "width": 18, "height": 18, "left": 448, "top": 182, "right": 466, "bottom": 200, "topLeft": {"x": 448, "y": 182}, "topRight": {"x": 466, "y": 182}, "bottomLeft": {"x": 448, "y": 200}, "bottomRight": {"x": 466, "y": 200}}, "children": []}]}, {"name": "Bold (Ctrl+B)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 504, "y": 172}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 504, "y": 172, "width": 40, "height": 40, "left": 504, "top": 172, "right": 544, "bottom": 212, "topLeft": {"x": 504, "y": 172}, "topRight": {"x": 544, "y": 172}, "bottomLeft": {"x": 504, "y": 212}, "bottomRight": {"x": 544, "y": 212}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 516, "y": 183}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 516, "y": 183, "width": 18, "height": 18, "left": 516, "top": 183, "right": 534, "bottom": 201, "topLeft": {"x": 516, "y": 183}, "topRight": {"x": 534, "y": 183}, "bottomLeft": {"x": 516, "y": 201}, "bottomRight": {"x": 534, "y": 201}}, "children": []}]}, {"name": "Clear formatting (Ctrl+Space)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Link (Ctrl+K)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Italic (Ctrl+I)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "More options", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "OverflowButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 545, "y": 172}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 545, "y": 172, "width": 40, "height": 40, "left": 545, "top": 172, "right": 585, "bottom": 212, "topLeft": {"x": 545, "y": 172}, "topRight": {"x": 585, "y": 172}, "bottomLeft": {"x": 545, "y": 212}, "bottomRight": {"x": 585, "y": 212}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 556, "y": 183}, "size": {"isEmpty": false, "width": 17, "height": 18}, "x": 556, "y": 183, "width": 17, "height": 18, "left": 556, "top": 183, "right": 573, "bottom": 201, "topLeft": {"x": 556, "y": 183}, "topRight": {"x": 573, "y": 183}, "bottomLeft": {"x": 556, "y": 201}, "bottomRight": {"x": 573, "y": 201}}, "children": []}]}, {"name": "Copilot (Preview)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "RewriteDropDownButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 622, "y": 172}, "size": {"isEmpty": false, "width": 73, "height": 40}, "x": 622, "y": 172, "width": 73, "height": 40, "left": 622, "top": 172, "right": 695, "bottom": 212, "topLeft": {"x": 622, "y": 172}, "topRight": {"x": 695, "y": 172}, "bottomLeft": {"x": 622, "y": 212}, "bottomRight": {"x": 695, "y": 212}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 636, "y": 181}, "size": {"isEmpty": false, "width": 19, "height": 20}, "x": 636, "y": 181, "width": 19, "height": 20, "left": 636, "top": 181, "right": 655, "bottom": 201, "topLeft": {"x": 636, "y": 181}, "topRight": {"x": 655, "y": 181}, "bottomLeft": {"x": 636, "y": 201}, "bottomRight": {"x": 655, "y": 201}}, "children": []}]}, {"name": "User avatar", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AvatarButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 700, "y": 172}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 700, "y": 172, "width": 38, "height": 40, "left": 700, "top": 172, "right": 738, "bottom": 212, "topLeft": {"x": 700, "y": 172}, "topRight": {"x": 738, "y": 172}, "bottomLeft": {"x": 700, "y": 212}, "bottomRight": {"x": 738, "y": 212}}, "children": [{"name": "Person", "controlType": "ControlType.Text", "automationId": "Avatar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 706, "y": 180}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 706, "y": 180, "width": 25, "height": 25, "left": 706, "top": 180, "right": 731, "bottom": 205, "topLeft": {"x": 706, "y": 180}, "topRight": {"x": 731, "y": 180}, "bottomLeft": {"x": 706, "y": 205}, "bottomRight": {"x": 731, "y": 205}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "InitialsTextBlock", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "", "controlType": "ControlType.ProgressBar", "automationId": "LoadingRing", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 706, "y": 180}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 706, "y": 180, "width": 25, "height": 25, "left": 706, "top": 180, "right": 731, "bottom": 205, "topLeft": {"x": 706, "y": 180}, "topRight": {"x": 731, "y": 180}, "bottomLeft": {"x": 706, "y": 205}, "bottomRight": {"x": 731, "y": 205}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "<PERSON>tiePlayer", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 706, "y": 180}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 706, "y": 180, "width": 25, "height": 25, "left": 706, "top": 180, "right": 731, "bottom": 205, "topLeft": {"x": 706, "y": 180}, "topRight": {"x": 731, "y": 180}, "bottomLeft": {"x": 706, "y": 205}, "bottomRight": {"x": 731, "y": 205}}, "children": []}]}, {"name": "Settings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "SettingsButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 743, "y": 172}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 743, "y": 172, "width": 38, "height": 40, "left": 743, "top": 172, "right": 781, "bottom": 212, "topLeft": {"x": 743, "y": 172}, "topRight": {"x": 781, "y": 172}, "bottomLeft": {"x": 743, "y": 212}, "bottomRight": {"x": 781, "y": 212}}, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "PrivacyTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "CowriterTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 128, "y": 512}, "size": {"isEmpty": false, "width": 663, "height": 40}, "x": 128, "y": 512, "width": 663, "height": 40, "left": 128, "top": 512, "right": 791, "bottom": 552, "topLeft": {"x": 128, "y": 512}, "topRight": {"x": 791, "y": 512}, "bottomLeft": {"x": 128, "y": 552}, "bottomRight": {"x": 791, "y": 552}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 128, "y": 512}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 128, "y": 512, "width": 530, "height": 32, "left": 128, "top": 512, "right": 658, "bottom": 544, "topLeft": {"x": 128, "y": 512}, "topRight": {"x": 658, "y": 512}, "bottomLeft": {"x": 128, "y": 544}, "bottomRight": {"x": 658, "y": 544}}, "children": [{"name": "Line 1,\nColumn 1", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 148, "y": 522}, "size": {"isEmpty": false, "width": 74, "height": 20}, "x": 148, "y": 522, "width": 74, "height": 20, "left": 148, "top": 522, "right": 222, "bottom": 542, "topLeft": {"x": 148, "y": 522}, "topRight": {"x": 222, "y": 522}, "bottomLeft": {"x": 148, "y": 542}, "bottomRight": {"x": 222, "y": 542}}, "children": []}, {"name": "0 characters", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 259, "y": 522}, "size": {"isEmpty": false, "width": 82, "height": 20}, "x": 259, "y": 522, "width": 82, "height": 20, "left": 259, "top": 522, "right": 341, "bottom": 542, "topLeft": {"x": 259, "y": 522}, "topRight": {"x": 341, "y": 522}, "bottomLeft": {"x": 259, "y": 542}, "bottomRight": {"x": 341, "y": 542}}, "children": []}, {"name": "Plain text", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 366, "y": 512}, "size": {"isEmpty": false, "width": 78, "height": 40}, "x": 366, "y": 512, "width": 78, "height": 40, "left": 366, "top": 512, "right": 444, "bottom": 552, "topLeft": {"x": 366, "y": 512}, "topRight": {"x": 444, "y": 512}, "bottomLeft": {"x": 366, "y": 552}, "bottomRight": {"x": 444, "y": 552}}, "children": []}, {"name": "Zoom", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 489, "y": 522}, "size": {"isEmpty": false, "width": 39, "height": 20}, "x": 489, "y": 522, "width": 39, "height": 20, "left": 489, "top": 522, "right": 528, "bottom": 542, "topLeft": {"x": 489, "y": 522}, "topRight": {"x": 528, "y": 522}, "bottomLeft": {"x": 489, "y": 542}, "bottomRight": {"x": 528, "y": 542}}, "children": []}, {"name": " Windows (CRLF)", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 561, "y": 522}, "size": {"isEmpty": false, "width": 94, "height": 20}, "x": 561, "y": 522, "width": 94, "height": 20, "left": 561, "top": 522, "right": 655, "bottom": 542, "topLeft": {"x": 561, "y": 522}, "topRight": {"x": 655, "y": 522}, "bottomLeft": {"x": 561, "y": 542}, "bottomRight": {"x": 655, "y": 542}}, "children": []}, {"name": " UTF-8", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 677, "y": 522}, "size": {"isEmpty": false, "width": 45, "height": 20}, "x": 677, "y": 522, "width": 45, "height": 20, "left": 677, "top": 522, "right": 722, "bottom": 542, "topLeft": {"x": 677, "y": 522}, "topRight": {"x": 722, "y": 522}, "bottomLeft": {"x": 677, "y": 542}, "bottomRight": {"x": 722, "y": 542}}, "children": []}]}]}, {"name": "Untitled - Notepad", "controlType": "ControlType.TitleBar", "automationId": "TitleBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 129, "y": 129}, "size": {"isEmpty": false, "width": 661, "height": 29}, "x": 129, "y": 129, "width": 661, "height": 29, "left": 129, "top": 129, "right": 790, "bottom": 158, "topLeft": {"x": 129, "y": 129}, "topRight": {"x": 790, "y": 129}, "bottomLeft": {"x": 129, "y": 158}, "bottomRight": {"x": 790, "y": 158}}, "children": [{"name": "System Menu Bar", "controlType": "ControlType.MenuBar", "automationId": "SystemMenuBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 129, "y": 129}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 129, "y": 129, "width": 28, "height": 28, "left": 129, "top": 129, "right": 157, "bottom": 157, "topLeft": {"x": 129, "y": 129}, "topRight": {"x": 157, "y": 129}, "bottomLeft": {"x": 129, "y": 157}, "bottomRight": {"x": 157, "y": 157}}, "children": [{"name": "System", "controlType": "ControlType.MenuItem", "automationId": "Item 1", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 129, "y": 129}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 129, "y": 129, "width": 28, "height": 28, "left": 129, "top": 129, "right": 157, "bottom": 157, "topLeft": {"x": 129, "y": 129}, "topRight": {"x": 157, "y": 129}, "bottomLeft": {"x": 129, "y": 157}, "bottomRight": {"x": 157, "y": 157}}, "children": []}]}, {"name": "Minimize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Minimize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 613, "y": 121}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 613, "y": 121, "width": 60, "height": 37, "left": 613, "top": 121, "right": 673, "bottom": 158, "topLeft": {"x": 613, "y": 121}, "topRight": {"x": 673, "y": 121}, "bottomLeft": {"x": 613, "y": 158}, "bottomRight": {"x": 673, "y": 158}}, "children": []}, {"name": "Maximize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Maximize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 673, "y": 121}, "size": {"isEmpty": false, "width": 59, "height": 37}, "x": 673, "y": 121, "width": 59, "height": 37, "left": 673, "top": 121, "right": 732, "bottom": 158, "topLeft": {"x": 673, "y": 121}, "topRight": {"x": 732, "y": 121}, "bottomLeft": {"x": 673, "y": 158}, "bottomRight": {"x": 732, "y": 158}}, "children": []}, {"name": "Close", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Close", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 732, "y": 121}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 732, "y": 121, "width": 60, "height": 37, "left": 732, "top": 121, "right": 792, "bottom": 158, "topLeft": {"x": 732, "y": 121}, "topRight": {"x": 792, "y": 121}, "bottomLeft": {"x": 732, "y": 158}, "bottomRight": {"x": 792, "y": 158}}, "children": []}]}]}
# CognitiveDrive

CognitiveDrive is a C# .NET 8.0 solution that provides the initial components of an AI agent's perception engine. This Sprint 1 implementation creates a command-line inspection tool that can extract and display the raw UI element hierarchy from any running application on Windows.

## Project Structure

The solution contains two projects:

### CognitiveDrive.CoreUIA
- **Type**: C# Class Library
- **Purpose**: Contains the core logic for interacting with the Windows UI Automation (UIA) framework
- **Key Class**: `UiaScanner` - Provides methods for scanning UI trees

### CognitiveDrive.Inspector
- **Type**: C# Console Application  
- **Purpose**: Command-line tool for testing and debugging the CoreUIA library
- **Dependencies**: System.CommandLine NuGet package, project reference to CognitiveDrive.CoreUIA

## Features

The inspector tool supports two distinct modes:

### 1. Global Mode
Inspects any running process by name:
```bash
CognitiveDrive.Inspector.exe inspect global --process notepad
```

### 2. Harness Mode  
Launches a new process and then inspects it:
```bash
CognitiveDrive.Inspector.exe inspect harness --executable "C:\Windows\System32\notepad.exe"
```

## Building the Solution

### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 (recommended)
- Windows OS (required for UI Automation)

### Build Steps
1. Open `CognitiveDrive.sln` in Visual Studio 2022
2. Restore NuGet packages
3. Build the solution (Ctrl+Shift+B)

Alternatively, using the .NET CLI:
```bash
dotnet restore
dotnet build
```

## Usage Examples

### Inspect Running Notepad
1. Open Notepad manually
2. Run: `CognitiveDrive.Inspector.exe inspect global --process notepad`

### Launch and Inspect Notepad
```bash
CognitiveDrive.Inspector.exe inspect harness --executable "C:\Windows\System32\notepad.exe"
```

### Launch and Inspect Calculator
```bash
CognitiveDrive.Inspector.exe inspect harness --executable "C:\Windows\System32\calc.exe"
```

## Output Format

The tool outputs a hierarchical text dump showing:
- Element Name
- ControlType (ProgrammaticName)
- AutomationId  
- BoundingRectangle

Example output:
```
Element:
  Name: Untitled - Notepad
  ControlType: ControlType.Window
  AutomationId: <No ID>
  BoundingRectangle: {X=100,Y=100,Width=800,Height=600}
  Element:
    Name: <No Name>
    ControlType: ControlType.MenuBar
    AutomationId: MenuBar
    BoundingRectangle: {X=100,Y=120,Width=800,Height=25}
```

## Technical Implementation

### Core Components

**UiaScanner.FindWindowByProcessName(string processName)**
- Finds processes by name using `System.Diagnostics.Process.GetProcessesByName()`
- Handles cases where the main window isn't immediately available
- Returns the root `AutomationElement` for the window

**UiaScanner.GetElementTreeRaw(AutomationElement, StringBuilder, int depth)**
- Recursively traverses the UI tree using `TreeWalker.RawViewWalker`
- Extracts key properties from each element
- Builds indented text representation of the hierarchy

### Error Handling
- Graceful handling of processes that don't exist
- Retry logic for windows that aren't immediately ready
- Clean termination of launched processes in harness mode
- Comprehensive exception handling with user-friendly error messages

## Requirements Met

✅ Solution compiles without errors or warnings in Visual Studio 2022  
✅ Global mode successfully inspects running processes  
✅ Harness mode launches and inspects new processes  
✅ Uses System.CommandLine for robust CLI parsing  
✅ Implements Windows UI Automation framework integration  
✅ Provides hierarchical UI element tree output  

## Future Enhancements

This Sprint 1 implementation provides the foundation for:
- Advanced element filtering and querying
- JSON/XML output formats  
- Real-time UI monitoring
- Integration with AI/ML perception models
- Cross-platform UI automation support

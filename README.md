# CognitiveDrive - Production-Grade UI Automation Engine

**CognitiveDrive** is a production-ready C# .NET 8.0 solution that provides a robust, high-performance UI automation engine for Windows applications. Built with clean architecture principles, comprehensive error handling, and real-world testing validation.

## 🏗️ Architecture Overview

CognitiveDrive follows a clean, layered architecture designed for production use:

### **CognitiveDrive.Core**
- **Purpose**: Core domain models and interfaces
- **Contains**:
  - `ScanResult<T>` - Production-grade result wrapper with timing and error handling
  - `UiaElementNode` - Enhanced UI element data model with validation
  - `TargetingQuery` - Strongly-typed query system with fluent builder
  - Core interfaces (`IUiaScanner`, `IElementTargeting`)

### **CognitiveDrive.Infrastructure**
- **Purpose**: Windows UI Automation implementation
- **Contains**:
  - `WindowsUiaScanner` - Production UIA scanner with timeout protection
  - `WindowsUiaPropertyExtractor` - Safe property extraction with error handling
  - `WindowsProcessManager` - Process discovery and management
  - `UiaEventMonitor` - Global UI event monitoring (singleton pattern)

## Features

The inspector tool supports two distinct modes:

### 1. Global Mode
Inspects any running process by name:
```bash
CognitiveDrive.Inspector.exe inspect global --process notepad
```

### 2. Harness Mode  
Launches a new process and then inspects it:
```bash
CognitiveDrive.Inspector.exe inspect harness --executable "C:\Windows\System32\notepad.exe"
```

## Building the Solution

### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 (recommended)
- Windows OS (required for UI Automation)

### Build Steps
1. Open `CognitiveDrive.sln` in Visual Studio 2022
2. Restore NuGet packages
3. Build the solution (Ctrl+Shift+B)

Alternatively, using the .NET CLI:
```bash
dotnet restore
dotnet build
```

## Usage Examples

### Inspect Running Notepad
1. Open Notepad manually
2. Run: `CognitiveDrive.Inspector.exe inspect global --process notepad`

### Launch and Inspect Notepad
```bash
CognitiveDrive.Inspector.exe inspect harness --executable "C:\Windows\System32\notepad.exe"
```

### Launch and Inspect Calculator
```bash
CognitiveDrive.Inspector.exe inspect harness --executable "C:\Windows\System32\calc.exe"
```

## Output Format

The tool outputs a hierarchical text dump showing:
- Element Name
- ControlType (ProgrammaticName)
- AutomationId  
- BoundingRectangle

Example output:
```
Element:
  Name: Untitled - Notepad
  ControlType: ControlType.Window
  AutomationId: <No ID>
  BoundingRectangle: {X=100,Y=100,Width=800,Height=600}
  Element:
    Name: <No Name>
    ControlType: ControlType.MenuBar
    AutomationId: MenuBar
    BoundingRectangle: {X=100,Y=120,Width=800,Height=25}
```

## Technical Implementation

### Core Components

**UiaScanner.FindWindowByProcessName(string processName)**
- Finds processes by name using `System.Diagnostics.Process.GetProcessesByName()`
- Handles cases where the main window isn't immediately available
- Returns the root `AutomationElement` for the window

**UiaScanner.GetElementTreeRaw(AutomationElement, StringBuilder, int depth)**
- Recursively traverses the UI tree using `TreeWalker.RawViewWalker`
- Extracts key properties from each element
- Builds indented text representation of the hierarchy

### Error Handling
- Graceful handling of processes that don't exist
- Retry logic for windows that aren't immediately ready
- Clean termination of launched processes in harness mode
- Comprehensive exception handling with user-friendly error messages

## Requirements Met

✅ Solution compiles without errors or warnings in Visual Studio 2022  
✅ Global mode successfully inspects running processes  
✅ Harness mode launches and inspects new processes  
✅ Uses System.CommandLine for robust CLI parsing  
✅ Implements Windows UI Automation framework integration  
✅ Provides hierarchical UI element tree output  

## Future Enhancements

This Sprint 1 implementation provides the foundation for:
- Advanced element filtering and querying
- JSON/XML output formats  
- Real-time UI monitoring
- Integration with AI/ML perception models
- Cross-platform UI automation support

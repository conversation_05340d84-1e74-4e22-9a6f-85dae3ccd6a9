{"name": "Untitled - Notepad", "controlType": "ControlType.Window", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 893, "y": 246}, "size": {"isEmpty": false, "width": 679, "height": 440}, "x": 893, "y": 246, "width": 679, "height": 440, "left": 893, "top": 246, "right": 1572, "bottom": 686, "topLeft": {"x": 893, "y": 246}, "topRight": {"x": 1572, "y": 246}, "bottomLeft": {"x": 893, "y": 686}, "bottomRight": {"x": 1572, "y": 686}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 339}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 901, "y": 339, "width": 663, "height": 299, "left": 901, "top": 339, "right": 1564, "bottom": 638, "topLeft": {"x": 901, "y": 339}, "topRight": {"x": 1564, "y": 339}, "bottomLeft": {"x": 901, "y": 638}, "bottomRight": {"x": 1564, "y": 638}}, "children": [{"name": "Text editor", "controlType": "ControlType.Document", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 339}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 901, "y": 339, "width": 663, "height": 299, "left": 901, "top": 339, "right": 1564, "bottom": 638, "topLeft": {"x": 901, "y": 339}, "topRight": {"x": 1564, "y": 339}, "bottomLeft": {"x": 901, "y": 638}, "bottomRight": {"x": 1564, "y": 638}}, "children": []}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 951, "y": 258}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 951, "y": 258, "width": 359, "height": 40, "left": 951, "top": 258, "right": 1310, "bottom": 298, "topLeft": {"x": 951, "y": 258}, "topRight": {"x": 1310, "y": 258}, "bottomLeft": {"x": 951, "y": 298}, "bottomRight": {"x": 1310, "y": 298}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 951, "y": 258}, "size": {"isEmpty": false, "width": 287, "height": 32}, "x": 951, "y": 258, "width": 287, "height": 32, "left": 951, "top": 258, "right": 1238, "bottom": 290, "topLeft": {"x": 951, "y": 258}, "topRight": {"x": 1238, "y": 258}, "bottomLeft": {"x": 951, "y": 290}, "bottomRight": {"x": 1238, "y": 290}}, "children": [{"name": "", "controlType": "ControlType.Tab", "automationId": "Tabs", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 951, "y": 258}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 951, "y": 258, "width": 359, "height": 40, "left": 951, "top": 258, "right": 1310, "bottom": 298, "topLeft": {"x": 951, "y": 258}, "topRight": {"x": 1310, "y": 258}, "bottomLeft": {"x": 951, "y": 298}, "bottomRight": {"x": 1310, "y": 298}}, "children": [{"name": "", "controlType": "ControlType.List", "automationId": "TabListView", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 953, "y": 258}, "size": {"isEmpty": false, "width": 312, "height": 40}, "x": 953, "y": 258, "width": 312, "height": 40, "left": 953, "top": 258, "right": 1265, "bottom": 298, "topLeft": {"x": 953, "y": 258}, "topRight": {"x": 1265, "y": 258}, "bottomLeft": {"x": 953, "y": 298}, "bottomRight": {"x": 1265, "y": 298}}, "children": [{"name": "Untitled. Unmodified.", "controlType": "ControlType.TabItem", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 956, "y": 258}, "size": {"isEmpty": false, "width": 307, "height": 40}, "x": 956, "y": 258, "width": 307, "height": 40, "left": 956, "top": 258, "right": 1263, "bottom": 298, "topLeft": {"x": 956, "y": 258}, "topRight": {"x": 1263, "y": 258}, "bottomLeft": {"x": 956, "y": 298}, "bottomRight": {"x": 1263, "y": 298}}, "children": [{"name": "Untitled", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 972, "y": 268}, "size": {"isEmpty": false, "width": 56, "height": 19}, "x": 972, "y": 268, "width": 56, "height": 19, "left": 972, "top": 268, "right": 1028, "bottom": 287, "topLeft": {"x": 972, "y": 268}, "topRight": {"x": 1028, "y": 268}, "bottomLeft": {"x": 972, "y": 287}, "bottomRight": {"x": 1028, "y": 287}}, "children": []}, {"name": "Close Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "CloseButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1212, "y": 263}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 1212, "y": 263, "width": 40, "height": 30, "left": 1212, "top": 263, "right": 1252, "bottom": 293, "topLeft": {"x": 1212, "y": 263}, "topRight": {"x": 1252, "y": 263}, "bottomLeft": {"x": 1212, "y": 293}, "bottomRight": {"x": 1252, "y": 293}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1225, "y": 270}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 1225, "y": 270, "width": 15, "height": 15, "left": 1225, "top": 270, "right": 1240, "bottom": 285, "topLeft": {"x": 1225, "y": 270}, "topRight": {"x": 1240, "y": 270}, "bottomLeft": {"x": 1225, "y": 285}, "bottomRight": {"x": 1240, "y": 285}}, "children": []}]}]}]}, {"name": "Add New Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AddButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1270, "y": 264}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 1270, "y": 264, "width": 40, "height": 30, "left": 1270, "top": 264, "right": 1310, "bottom": 294, "topLeft": {"x": 1270, "y": 264}, "topRight": {"x": 1310, "y": 264}, "bottomLeft": {"x": 1270, "y": 294}, "bottomRight": {"x": 1310, "y": 294}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1282, "y": 271}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 1282, "y": 271, "width": 15, "height": 15, "left": 1282, "top": 271, "right": 1297, "bottom": 286, "topLeft": {"x": 1282, "y": 271}, "topRight": {"x": 1297, "y": 271}, "bottomLeft": {"x": 1282, "y": 286}, "bottomRight": {"x": 1297, "y": 286}}, "children": []}]}]}, {"name": "Notepad automatically saves your progress. All your content will be available the next time you open Notepad.", "controlType": "ControlType.Pane", "automationId": "TeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 298}, "size": {"isEmpty": false, "width": 663, "height": 41}, "x": 901, "y": 298, "width": 663, "height": 41, "left": 901, "top": 298, "right": 1564, "bottom": 339, "topLeft": {"x": 901, "y": 298}, "topRight": {"x": 1564, "y": 298}, "bottomLeft": {"x": 901, "y": 339}, "bottomRight": {"x": 1564, "y": 339}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 298}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 901, "y": 298, "width": 530, "height": 32, "left": 901, "top": 298, "right": 1431, "bottom": 330, "topLeft": {"x": 901, "y": 298}, "topRight": {"x": 1431, "y": 298}, "bottomLeft": {"x": 901, "y": 330}, "bottomRight": {"x": 1431, "y": 330}}, "children": [{"name": "", "controlType": "ControlType.MenuBar", "automationId": "<PERSON><PERSON><PERSON><PERSON>", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 298}, "size": {"isEmpty": false, "width": 199, "height": 40}, "x": 901, "y": 298, "width": 199, "height": 40, "left": 901, "top": 298, "right": 1100, "bottom": 338, "topLeft": {"x": 901, "y": 298}, "topRight": {"x": 1100, "y": 298}, "bottomLeft": {"x": 901, "y": 338}, "bottomRight": {"x": 1100, "y": 338}}, "children": [{"name": "File", "controlType": "ControlType.MenuItem", "automationId": "File", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 906, "y": 298}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 906, "y": 298, "width": 52, "height": 40, "left": 906, "top": 298, "right": 958, "bottom": 338, "topLeft": {"x": 906, "y": 298}, "topRight": {"x": 958, "y": 298}, "bottomLeft": {"x": 906, "y": 338}, "bottomRight": {"x": 958, "y": 338}}, "children": [{"name": "File", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 906, "y": 298}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 906, "y": 298, "width": 52, "height": 40, "left": 906, "top": 298, "right": 958, "bottom": 338, "topLeft": {"x": 906, "y": 298}, "topRight": {"x": 958, "y": 298}, "bottomLeft": {"x": 906, "y": 338}, "bottomRight": {"x": 958, "y": 338}}, "children": [{"name": "File", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 918, "y": 306}, "size": {"isEmpty": false, "width": 27, "height": 24}, "x": 918, "y": 306, "width": 27, "height": 24, "left": 918, "top": 306, "right": 945, "bottom": 330, "topLeft": {"x": 918, "y": 306}, "topRight": {"x": 945, "y": 306}, "bottomLeft": {"x": 918, "y": 330}, "bottomRight": {"x": 945, "y": 330}}, "children": []}]}]}, {"name": "Edit", "controlType": "ControlType.MenuItem", "automationId": "Edit", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 968, "y": 298}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 968, "y": 298, "width": 55, "height": 40, "left": 968, "top": 298, "right": 1023, "bottom": 338, "topLeft": {"x": 968, "y": 298}, "topRight": {"x": 1023, "y": 298}, "bottomLeft": {"x": 968, "y": 338}, "bottomRight": {"x": 1023, "y": 338}}, "children": [{"name": "Edit", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 968, "y": 298}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 968, "y": 298, "width": 55, "height": 40, "left": 968, "top": 298, "right": 1023, "bottom": 338, "topLeft": {"x": 968, "y": 298}, "topRight": {"x": 1023, "y": 298}, "bottomLeft": {"x": 968, "y": 338}, "bottomRight": {"x": 1023, "y": 338}}, "children": [{"name": "Edit", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 981, "y": 306}, "size": {"isEmpty": false, "width": 30, "height": 24}, "x": 981, "y": 306, "width": 30, "height": 24, "left": 981, "top": 306, "right": 1011, "bottom": 330, "topLeft": {"x": 981, "y": 306}, "topRight": {"x": 1011, "y": 306}, "bottomLeft": {"x": 981, "y": 330}, "bottomRight": {"x": 1011, "y": 330}}, "children": []}]}]}, {"name": "View", "controlType": "ControlType.MenuItem", "automationId": "View", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1033, "y": 298}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 1033, "y": 298, "width": 62, "height": 40, "left": 1033, "top": 298, "right": 1095, "bottom": 338, "topLeft": {"x": 1033, "y": 298}, "topRight": {"x": 1095, "y": 298}, "bottomLeft": {"x": 1033, "y": 338}, "bottomRight": {"x": 1095, "y": 338}}, "children": [{"name": "View", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1033, "y": 298}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 1033, "y": 298, "width": 62, "height": 40, "left": 1033, "top": 298, "right": 1095, "bottom": 338, "topLeft": {"x": 1033, "y": 298}, "topRight": {"x": 1095, "y": 298}, "bottomLeft": {"x": 1033, "y": 338}, "bottomRight": {"x": 1095, "y": 338}}, "children": [{"name": "View", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1045, "y": 306}, "size": {"isEmpty": false, "width": 37, "height": 24}, "x": 1045, "y": 306, "width": 37, "height": 24, "left": 1045, "top": 306, "right": 1082, "bottom": 330, "topLeft": {"x": 1045, "y": 306}, "topRight": {"x": 1082, "y": 306}, "bottomLeft": {"x": 1045, "y": 330}, "bottomRight": {"x": 1082, "y": 330}}, "children": []}]}]}]}, {"name": "Headings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1136, "y": 298}, "size": {"isEmpty": false, "width": 71, "height": 40}, "x": 1136, "y": 298, "width": 71, "height": 40, "left": 1136, "top": 298, "right": 1207, "bottom": 338, "topLeft": {"x": 1136, "y": 298}, "topRight": {"x": 1207, "y": 298}, "bottomLeft": {"x": 1136, "y": 338}, "bottomRight": {"x": 1207, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1150, "y": 308}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1150, "y": 308, "width": 18, "height": 18, "left": 1150, "top": 308, "right": 1168, "bottom": 326, "topLeft": {"x": 1150, "y": 308}, "topRight": {"x": 1168, "y": 308}, "bottomLeft": {"x": 1150, "y": 326}, "bottomRight": {"x": 1168, "y": 326}}, "children": []}]}, {"name": "Lists", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1207, "y": 298}, "size": {"isEmpty": false, "width": 70, "height": 40}, "x": 1207, "y": 298, "width": 70, "height": 40, "left": 1207, "top": 298, "right": 1277, "bottom": 338, "topLeft": {"x": 1207, "y": 298}, "topRight": {"x": 1277, "y": 298}, "bottomLeft": {"x": 1207, "y": 338}, "bottomRight": {"x": 1277, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1221, "y": 308}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1221, "y": 308, "width": 18, "height": 18, "left": 1221, "top": 308, "right": 1239, "bottom": 326, "topLeft": {"x": 1221, "y": 308}, "topRight": {"x": 1239, "y": 308}, "bottomLeft": {"x": 1221, "y": 326}, "bottomRight": {"x": 1239, "y": 326}}, "children": []}]}, {"name": "Bold (Ctrl+B)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1277, "y": 298}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 1277, "y": 298, "width": 40, "height": 40, "left": 1277, "top": 298, "right": 1317, "bottom": 338, "topLeft": {"x": 1277, "y": 298}, "topRight": {"x": 1317, "y": 298}, "bottomLeft": {"x": 1277, "y": 338}, "bottomRight": {"x": 1317, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1289, "y": 309}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 1289, "y": 309, "width": 18, "height": 18, "left": 1289, "top": 309, "right": 1307, "bottom": 327, "topLeft": {"x": 1289, "y": 309}, "topRight": {"x": 1307, "y": 309}, "bottomLeft": {"x": 1289, "y": 327}, "bottomRight": {"x": 1307, "y": 327}}, "children": []}]}, {"name": "Clear formatting (Ctrl+Space)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Link (Ctrl+K)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Italic (Ctrl+I)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "More options", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "OverflowButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1318, "y": 298}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 1318, "y": 298, "width": 40, "height": 40, "left": 1318, "top": 298, "right": 1358, "bottom": 338, "topLeft": {"x": 1318, "y": 298}, "topRight": {"x": 1358, "y": 298}, "bottomLeft": {"x": 1318, "y": 338}, "bottomRight": {"x": 1358, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1329, "y": 309}, "size": {"isEmpty": false, "width": 17, "height": 18}, "x": 1329, "y": 309, "width": 17, "height": 18, "left": 1329, "top": 309, "right": 1346, "bottom": 327, "topLeft": {"x": 1329, "y": 309}, "topRight": {"x": 1346, "y": 309}, "bottomLeft": {"x": 1329, "y": 327}, "bottomRight": {"x": 1346, "y": 327}}, "children": []}]}, {"name": "Copilot (Preview)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "RewriteDropDownButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1395, "y": 298}, "size": {"isEmpty": false, "width": 73, "height": 40}, "x": 1395, "y": 298, "width": 73, "height": 40, "left": 1395, "top": 298, "right": 1468, "bottom": 338, "topLeft": {"x": 1395, "y": 298}, "topRight": {"x": 1468, "y": 298}, "bottomLeft": {"x": 1395, "y": 338}, "bottomRight": {"x": 1468, "y": 338}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1409, "y": 307}, "size": {"isEmpty": false, "width": 19, "height": 20}, "x": 1409, "y": 307, "width": 19, "height": 20, "left": 1409, "top": 307, "right": 1428, "bottom": 327, "topLeft": {"x": 1409, "y": 307}, "topRight": {"x": 1428, "y": 307}, "bottomLeft": {"x": 1409, "y": 327}, "bottomRight": {"x": 1428, "y": 327}}, "children": []}]}, {"name": "User avatar", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AvatarButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1473, "y": 298}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 1473, "y": 298, "width": 38, "height": 40, "left": 1473, "top": 298, "right": 1511, "bottom": 338, "topLeft": {"x": 1473, "y": 298}, "topRight": {"x": 1511, "y": 298}, "bottomLeft": {"x": 1473, "y": 338}, "bottomRight": {"x": 1511, "y": 338}}, "children": [{"name": "Person", "controlType": "ControlType.Text", "automationId": "Avatar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1479, "y": 306}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1479, "y": 306, "width": 25, "height": 25, "left": 1479, "top": 306, "right": 1504, "bottom": 331, "topLeft": {"x": 1479, "y": 306}, "topRight": {"x": 1504, "y": 306}, "bottomLeft": {"x": 1479, "y": 331}, "bottomRight": {"x": 1504, "y": 331}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "InitialsTextBlock", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "", "controlType": "ControlType.ProgressBar", "automationId": "LoadingRing", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1479, "y": 306}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1479, "y": 306, "width": 25, "height": 25, "left": 1479, "top": 306, "right": 1504, "bottom": 331, "topLeft": {"x": 1479, "y": 306}, "topRight": {"x": 1504, "y": 306}, "bottomLeft": {"x": 1479, "y": 331}, "bottomRight": {"x": 1504, "y": 331}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "<PERSON>tiePlayer", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1479, "y": 306}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 1479, "y": 306, "width": 25, "height": 25, "left": 1479, "top": 306, "right": 1504, "bottom": 331, "topLeft": {"x": 1479, "y": 306}, "topRight": {"x": 1504, "y": 306}, "bottomLeft": {"x": 1479, "y": 331}, "bottomRight": {"x": 1504, "y": 331}}, "children": []}]}, {"name": "Settings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "SettingsButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1516, "y": 298}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 1516, "y": 298, "width": 38, "height": 40, "left": 1516, "top": 298, "right": 1554, "bottom": 338, "topLeft": {"x": 1516, "y": 298}, "topRight": {"x": 1554, "y": 298}, "bottomLeft": {"x": 1516, "y": 338}, "bottomRight": {"x": 1554, "y": 338}}, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "PrivacyTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "CowriterTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 638}, "size": {"isEmpty": false, "width": 663, "height": 40}, "x": 901, "y": 638, "width": 663, "height": 40, "left": 901, "top": 638, "right": 1564, "bottom": 678, "topLeft": {"x": 901, "y": 638}, "topRight": {"x": 1564, "y": 638}, "bottomLeft": {"x": 901, "y": 678}, "bottomRight": {"x": 1564, "y": 678}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 901, "y": 638}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 901, "y": 638, "width": 530, "height": 32, "left": 901, "top": 638, "right": 1431, "bottom": 670, "topLeft": {"x": 901, "y": 638}, "topRight": {"x": 1431, "y": 638}, "bottomLeft": {"x": 901, "y": 670}, "bottomRight": {"x": 1431, "y": 670}}, "children": [{"name": "Line 1,\nColumn 1", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 921, "y": 648}, "size": {"isEmpty": false, "width": 74, "height": 20}, "x": 921, "y": 648, "width": 74, "height": 20, "left": 921, "top": 648, "right": 995, "bottom": 668, "topLeft": {"x": 921, "y": 648}, "topRight": {"x": 995, "y": 648}, "bottomLeft": {"x": 921, "y": 668}, "bottomRight": {"x": 995, "y": 668}}, "children": []}, {"name": "0 characters", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1032, "y": 648}, "size": {"isEmpty": false, "width": 82, "height": 20}, "x": 1032, "y": 648, "width": 82, "height": 20, "left": 1032, "top": 648, "right": 1114, "bottom": 668, "topLeft": {"x": 1032, "y": 648}, "topRight": {"x": 1114, "y": 648}, "bottomLeft": {"x": 1032, "y": 668}, "bottomRight": {"x": 1114, "y": 668}}, "children": []}, {"name": "Plain text", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1139, "y": 638}, "size": {"isEmpty": false, "width": 78, "height": 40}, "x": 1139, "y": 638, "width": 78, "height": 40, "left": 1139, "top": 638, "right": 1217, "bottom": 678, "topLeft": {"x": 1139, "y": 638}, "topRight": {"x": 1217, "y": 638}, "bottomLeft": {"x": 1139, "y": 678}, "bottomRight": {"x": 1217, "y": 678}}, "children": []}, {"name": "Zoom", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1262, "y": 648}, "size": {"isEmpty": false, "width": 39, "height": 20}, "x": 1262, "y": 648, "width": 39, "height": 20, "left": 1262, "top": 648, "right": 1301, "bottom": 668, "topLeft": {"x": 1262, "y": 648}, "topRight": {"x": 1301, "y": 648}, "bottomLeft": {"x": 1262, "y": 668}, "bottomRight": {"x": 1301, "y": 668}}, "children": []}, {"name": " Windows (CRLF)", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1334, "y": 648}, "size": {"isEmpty": false, "width": 94, "height": 20}, "x": 1334, "y": 648, "width": 94, "height": 20, "left": 1334, "top": 648, "right": 1428, "bottom": 668, "topLeft": {"x": 1334, "y": 648}, "topRight": {"x": 1428, "y": 648}, "bottomLeft": {"x": 1334, "y": 668}, "bottomRight": {"x": 1428, "y": 668}}, "children": []}, {"name": " UTF-8", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1450, "y": 648}, "size": {"isEmpty": false, "width": 45, "height": 20}, "x": 1450, "y": 648, "width": 45, "height": 20, "left": 1450, "top": 648, "right": 1495, "bottom": 668, "topLeft": {"x": 1450, "y": 648}, "topRight": {"x": 1495, "y": 648}, "bottomLeft": {"x": 1450, "y": 668}, "bottomRight": {"x": 1495, "y": 668}}, "children": []}]}]}, {"name": "Untitled - Notepad", "controlType": "ControlType.TitleBar", "automationId": "TitleBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 902, "y": 255}, "size": {"isEmpty": false, "width": 661, "height": 29}, "x": 902, "y": 255, "width": 661, "height": 29, "left": 902, "top": 255, "right": 1563, "bottom": 284, "topLeft": {"x": 902, "y": 255}, "topRight": {"x": 1563, "y": 255}, "bottomLeft": {"x": 902, "y": 284}, "bottomRight": {"x": 1563, "y": 284}}, "children": [{"name": "System Menu Bar", "controlType": "ControlType.MenuBar", "automationId": "SystemMenuBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 902, "y": 255}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 902, "y": 255, "width": 28, "height": 28, "left": 902, "top": 255, "right": 930, "bottom": 283, "topLeft": {"x": 902, "y": 255}, "topRight": {"x": 930, "y": 255}, "bottomLeft": {"x": 902, "y": 283}, "bottomRight": {"x": 930, "y": 283}}, "children": [{"name": "System", "controlType": "ControlType.MenuItem", "automationId": "Item 1", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 902, "y": 255}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 902, "y": 255, "width": 28, "height": 28, "left": 902, "top": 255, "right": 930, "bottom": 283, "topLeft": {"x": 902, "y": 255}, "topRight": {"x": 930, "y": 255}, "bottomLeft": {"x": 902, "y": 283}, "bottomRight": {"x": 930, "y": 283}}, "children": []}]}, {"name": "Minimize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Minimize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1386, "y": 247}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 1386, "y": 247, "width": 60, "height": 37, "left": 1386, "top": 247, "right": 1446, "bottom": 284, "topLeft": {"x": 1386, "y": 247}, "topRight": {"x": 1446, "y": 247}, "bottomLeft": {"x": 1386, "y": 284}, "bottomRight": {"x": 1446, "y": 284}}, "children": []}, {"name": "Maximize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Maximize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1446, "y": 247}, "size": {"isEmpty": false, "width": 59, "height": 37}, "x": 1446, "y": 247, "width": 59, "height": 37, "left": 1446, "top": 247, "right": 1505, "bottom": 284, "topLeft": {"x": 1446, "y": 247}, "topRight": {"x": 1505, "y": 247}, "bottomLeft": {"x": 1446, "y": 284}, "bottomRight": {"x": 1505, "y": 284}}, "children": []}, {"name": "Close", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Close", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 1505, "y": 247}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 1505, "y": 247, "width": 60, "height": 37, "left": 1505, "top": 247, "right": 1565, "bottom": 284, "topLeft": {"x": 1505, "y": 247}, "topRight": {"x": 1565, "y": 247}, "bottomLeft": {"x": 1505, "y": 284}, "bottomRight": {"x": 1565, "y": 284}}, "children": []}]}]}
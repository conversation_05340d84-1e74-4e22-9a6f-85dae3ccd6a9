using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;
using System.Text;
using System.Text.RegularExpressions;

namespace CognitiveDrive.Core;

/// <summary>
/// THE ARCHIVIST - Local Embedding Engine for semantic understanding.
/// Uses Microsoft.ML.OnnxRuntime to run a pre-trained sentence-transformer model locally.
/// Singleton pattern ensures efficient model loading and memory usage.
/// </summary>
public sealed class EmbeddingEngine : IDisposable
{
    private static readonly object _lock = new object();
    private static EmbeddingEngine? _instance;
    private readonly InferenceSession _session;
    private readonly Dictionary<string, int> _vocabulary;
    private bool _disposed = false;

    // Model configuration for all-MiniLM-L6-v2
    private const int MaxSequenceLength = 256;
    private const int EmbeddingDimension = 384;
    private const string ModelFileName = "model.onnx";

    /// <summary>
    /// Gets the singleton instance of the EmbeddingEngine.
    /// </summary>
    public static EmbeddingEngine Instance
    {
        get
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    _instance ??= new EmbeddingEngine();
                }
            }
            return _instance;
        }
    }

    /// <summary>
    /// Private constructor to enforce singleton pattern.
    /// Loads the ONNX model and initializes the inference session.
    /// </summary>
    private EmbeddingEngine()
    {
        try
        {
            // Initialize ONNX Runtime session options
            var sessionOptions = new SessionOptions
            {
                EnableCpuMemArena = true,
                EnableMemoryPattern = true,
                GraphOptimizationLevel = GraphOptimizationLevel.ORT_ENABLE_ALL
            };

            // Load the model file
            var modelPath = GetModelPath();
            if (!File.Exists(modelPath))
            {
                throw new FileNotFoundException($"ONNX model file not found: {modelPath}. Please ensure {ModelFileName} is available in the models directory.");
            }

            _session = new InferenceSession(modelPath, sessionOptions);
            _vocabulary = LoadVocabulary();

            Console.WriteLine($"🧠 THE ARCHIVIST: Embedding engine initialized");
            Console.WriteLine($"   - Model: {modelPath}");
            Console.WriteLine($"   - Embedding Dimension: {EmbeddingDimension}");
            Console.WriteLine($"   - Max Sequence Length: {MaxSequenceLength}");
            Console.WriteLine($"   - Vocabulary Size: {_vocabulary.Count}");
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to initialize EmbeddingEngine: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Generates a 384-dimension embedding vector from the input text.
    /// Uses the locally-run sentence-transformer model for semantic understanding.
    /// </summary>
    /// <param name="text">The input text to embed</param>
    /// <returns>A 384-dimension float array representing the semantic embedding</returns>
    public float[] Generate(string text)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(EmbeddingEngine));

        if (string.IsNullOrWhiteSpace(text))
            return new float[EmbeddingDimension]; // Return zero vector for empty input

        try
        {
            // Preprocess the text
            var normalizedText = NormalizeText(text);
            
            // Tokenize the text
            var tokens = TokenizeText(normalizedText);
            
            // Create input tensors
            var inputIds = CreateInputTensor(tokens);
            var attentionMask = CreateAttentionMask(tokens.Length);
            
            // Prepare inputs for the model
            var inputs = new List<NamedOnnxValue>
            {
                NamedOnnxValue.CreateFromTensor("input_ids", inputIds),
                NamedOnnxValue.CreateFromTensor("attention_mask", attentionMask)
            };

            // Run inference
            using var results = _session.Run(inputs);
            var output = results.First().AsTensor<float>();
            
            // Extract and normalize the embedding
            var embedding = ExtractEmbedding(output, attentionMask);
            var normalizedEmbedding = NormalizeEmbedding(embedding);
            
            return normalizedEmbedding;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to generate embedding for text: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Gets the path to the ONNX model file.
    /// Looks in the application directory and common model locations.
    /// </summary>
    private string GetModelPath()
    {
        var possiblePaths = new[]
        {
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ModelFileName),
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "models", ModelFileName),
            Path.Combine(Environment.CurrentDirectory, ModelFileName),
            Path.Combine(Environment.CurrentDirectory, "models", ModelFileName)
        };

        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
                return path;
        }

        // If not found, return the default path for error reporting
        return possiblePaths[0];
    }

    /// <summary>
    /// Loads the vocabulary for tokenization from vocab.txt file.
    /// </summary>
    private Dictionary<string, int> LoadVocabulary()
    {
        var vocab = new Dictionary<string, int>();
        var vocabPath = Path.Combine(Path.GetDirectoryName(GetModelPath())!, "vocab.txt");

        if (!File.Exists(vocabPath))
        {
            throw new FileNotFoundException($"Vocabulary file not found: {vocabPath}");
        }

        var lines = File.ReadAllLines(vocabPath);
        for (int i = 0; i < lines.Length; i++)
        {
            var token = lines[i].Trim();
            if (!string.IsNullOrEmpty(token))
            {
                vocab[token] = i;
            }
        }

        return vocab;
    }

    /// <summary>
    /// Normalizes the input text for consistent processing.
    /// </summary>
    private string NormalizeText(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return string.Empty;

        // Convert to lowercase and normalize whitespace
        var normalized = text.ToLowerInvariant();
        normalized = Regex.Replace(normalized, @"\s+", " ");
        normalized = normalized.Trim();

        // Limit length to prevent memory issues
        if (normalized.Length > 1000)
        {
            normalized = normalized.Substring(0, 1000);
        }

        return normalized;
    }

    /// <summary>
    /// BERT-style tokenization using WordPiece algorithm.
    /// </summary>
    private int[] TokenizeText(string text)
    {
        var tokens = new List<int> { _vocabulary["[CLS]"] };

        var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        foreach (var word in words)
        {
            if (tokens.Count >= MaxSequenceLength - 1) break;

            var wordTokens = TokenizeWord(word.ToLowerInvariant());
            foreach (var token in wordTokens)
            {
                if (tokens.Count >= MaxSequenceLength - 1) break;
                tokens.Add(token);
            }
        }

        tokens.Add(_vocabulary["[SEP]"]);

        // Pad to max length
        while (tokens.Count < MaxSequenceLength)
        {
            tokens.Add(_vocabulary["[PAD]"]);
        }

        return tokens.Take(MaxSequenceLength).ToArray();
    }

    /// <summary>
    /// Tokenizes a single word using WordPiece algorithm.
    /// </summary>
    private List<int> TokenizeWord(string word)
    {
        var tokens = new List<int>();

        if (string.IsNullOrEmpty(word))
            return tokens;

        // Try to find the word in vocabulary first
        if (_vocabulary.TryGetValue(word, out var wholeWordToken))
        {
            tokens.Add(wholeWordToken);
            return tokens;
        }

        // WordPiece tokenization
        var start = 0;
        while (start < word.Length)
        {
            var end = word.Length;
            var foundToken = false;

            while (start < end)
            {
                var substr = word.Substring(start, end - start);
                if (start > 0)
                {
                    substr = "##" + substr; // WordPiece continuation marker
                }

                if (_vocabulary.TryGetValue(substr, out var tokenId))
                {
                    tokens.Add(tokenId);
                    start = end;
                    foundToken = true;
                    break;
                }
                end--;
            }

            if (!foundToken)
            {
                tokens.Add(_vocabulary["[UNK]"]);
                start++;
            }
        }

        return tokens;
    }

    /// <summary>
    /// Creates the input tensor for the model.
    /// </summary>
    private Tensor<long> CreateInputTensor(int[] tokens)
    {
        var inputIds = new long[1, MaxSequenceLength];
        for (int i = 0; i < tokens.Length; i++)
        {
            inputIds[0, i] = tokens[i];
        }
        
        return new DenseTensor<long>(inputIds, new[] { 1, MaxSequenceLength });
    }

    /// <summary>
    /// Creates the attention mask tensor.
    /// </summary>
    private Tensor<long> CreateAttentionMask(int tokenCount)
    {
        var mask = new long[1, MaxSequenceLength];
        for (int i = 0; i < Math.Min(tokenCount, MaxSequenceLength); i++)
        {
            mask[0, i] = 1;
        }
        
        return new DenseTensor<long>(mask, new[] { 1, MaxSequenceLength });
    }

    /// <summary>
    /// Extracts the embedding from the model output.
    /// Uses mean pooling over the sequence dimension.
    /// </summary>
    private float[] ExtractEmbedding(Tensor<float> output, Tensor<long> attentionMask)
    {
        var embedding = new float[EmbeddingDimension];
        var validTokens = 0;

        // Mean pooling over valid tokens
        for (int i = 0; i < MaxSequenceLength; i++)
        {
            if (attentionMask[0, i] == 1)
            {
                validTokens++;
                for (int j = 0; j < EmbeddingDimension; j++)
                {
                    embedding[j] += output[0, i, j];
                }
            }
        }

        // Average the embeddings
        if (validTokens > 0)
        {
            for (int i = 0; i < EmbeddingDimension; i++)
            {
                embedding[i] /= validTokens;
            }
        }

        return embedding;
    }

    /// <summary>
    /// Normalizes the embedding vector to unit length.
    /// </summary>
    private float[] NormalizeEmbedding(float[] embedding)
    {
        var norm = Math.Sqrt(embedding.Sum(x => x * x));
        if (norm > 0)
        {
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] /= (float)norm;
            }
        }
        return embedding;
    }

    /// <summary>
    /// Disposes the EmbeddingEngine and releases resources.
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _session?.Dispose();
            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// Finalizer to ensure proper cleanup.
    /// </summary>
    ~EmbeddingEngine()
    {
        Dispose();
    }
}

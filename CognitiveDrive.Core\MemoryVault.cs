using Microsoft.Data.Sqlite;
using System.Text;
using CognitiveDrive.Core.Models;

namespace CognitiveDrive.Core;

/// <summary>
/// THE ARCHIVIST - MemoryVault for long-term memory storage and semantic deduplication.
/// Uses SQLite with sqlite-vec extension for vector similarity search.
/// Implements semantic understanding through local sentence-transformer embeddings.
/// </summary>
public sealed class MemoryVault : IDisposable
{
    private readonly SqliteConnection _connection;
    private readonly EmbeddingEngine _embeddingEngine;
    private bool _disposed = false;

    // Similarity threshold for considering states as "known" (>99% similarity)
    private const double SimilarityThreshold = 0.99;
    private const string DatabaseFileName = "memory.db";

    /// <summary>
    /// Initializes the MemoryVault with SQLite database and sqlite-vss extension.
    /// </summary>
    public MemoryVault() : this(null)
    {
    }

    /// <summary>
    /// Initializes the MemoryVault with a specific database path.
    /// </summary>
    /// <param name="databasePath">Custom database path, or null to use default</param>
    public MemoryVault(string? databasePath)
    {
        try
        {
            var dbPath = databasePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, DatabaseFileName);
            var connectionString = $"Data Source={dbPath}";

            _connection = new SqliteConnection(connectionString);
            _connection.Open();

            // Load sqlite-vss extension
            LoadVssExtension();

            // Initialize database schema
            InitializeDatabase();

            // Initialize embedding engine
            _embeddingEngine = EmbeddingEngine.Instance;

            Console.WriteLine($"🧠 THE ARCHIVIST: MemoryVault initialized");
            Console.WriteLine($"   - Database: {dbPath}");
            Console.WriteLine($"   - Using: sqlite-vec extension");
            Console.WriteLine($"   - Similarity Threshold: {SimilarityThreshold:P2}");
        }
        catch (FileNotFoundException ex)
        {
            Console.WriteLine($"❌ MEMORY VAULT ERROR: Required files not found");
            Console.WriteLine($"   - {ex.Message}");
            Console.WriteLine($"   - Please ensure model files (model.onnx, vocab.txt) are in the models directory");
            throw new InvalidOperationException($"MemoryVault initialization failed: Missing required model files. {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ MEMORY VAULT ERROR: Initialization failed");
            Console.WriteLine($"   - Error: {ex.Message}");
            Console.WriteLine($"   - Type: {ex.GetType().Name}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"   - Inner: {ex.InnerException.Message}");
            }
            throw new InvalidOperationException($"Failed to initialize MemoryVault: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Processes a UI state and determines if it's known or new learning.
    /// Uses vector similarity search for semantic deduplication.
    /// </summary>
    /// <param name="rootNode">The root UI element node to process</param>
    /// <returns>Result string indicating "Known State (Vector Match)" or "New Learning Stored"</returns>
    public async Task<string> ProcessUIStateAsync(UiaElementNode rootNode)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MemoryVault));

        try
        {
            // Extract all text content from the UI tree
            var textContent = ExtractTextContent(rootNode);
            
            if (string.IsNullOrWhiteSpace(textContent))
            {
                return "Empty State (No Content)";
            }

            // Generate embedding vector
            var embedding = _embeddingEngine.Generate(textContent);
            
            // Search for similar vectors
            var similarMemory = await FindSimilarMemoryAsync(embedding);
            
            if (similarMemory != null && similarMemory.Similarity > SimilarityThreshold)
            {
                // Update existing memory
                await UpdateMemoryAsync(similarMemory.Id);
                
                Console.WriteLine($"🧠 MEMORY: Known state detected (Similarity: {similarMemory.Similarity:P2})");
                return "Known State (Vector Match)";
            }
            else
            {
                // Store new memory
                await StoreNewMemoryAsync(embedding, textContent);
                
                Console.WriteLine($"🧠 MEMORY: New learning stored ({textContent.Length} chars)");
                return "New Learning Stored";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ MEMORY ERROR: {ex.Message}");
            return $"Error: {ex.Message}";
        }
    }

    /// <summary>
    /// Loads the sqlite-vec extension for vector similarity search.
    /// </summary>
    private void LoadVssExtension()
    {
        try
        {
            // Get the path to the vec0.dll extension
            var extensionPath = GetVecExtensionPath();

            // Load the vec extension
            var command = _connection.CreateCommand();
            command.CommandText = $"SELECT load_extension('{extensionPath}')";
            command.ExecuteNonQuery();

            Console.WriteLine($"🧠 THE ARCHIVIST: sqlite-vec extension loaded from {extensionPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ WARNING: Could not load sqlite-vec extension: {ex.Message}");
            Console.WriteLine("   - Vector similarity search will use fallback method");
        }
    }

    /// <summary>
    /// Gets the path to the vec0.dll extension file.
    /// </summary>
    private string GetVecExtensionPath()
    {
        var possiblePaths = new[]
        {
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "models", "vec0.dll"),
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "vec0.dll"),
            Path.Combine(Environment.CurrentDirectory, "models", "vec0.dll"),
            Path.Combine(Environment.CurrentDirectory, "vec0.dll")
        };

        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
                return path.Replace('\\', '/'); // Use forward slashes for SQLite
        }

        // If not found, return the default path for error reporting
        return possiblePaths[0].Replace('\\', '/');
    }

    /// <summary>
    /// Initializes the database schema with Memories table and vec_memories virtual table.
    /// </summary>
    private void InitializeDatabase()
    {
        var command = _connection.CreateCommand();

        // Create the main Memories table
        command.CommandText = @"
            CREATE TABLE IF NOT EXISTS Memories (
                ID                INTEGER PRIMARY KEY,
                Timestamp         TEXT NOT NULL,
                ObservationCount  INTEGER NOT NULL,
                Embedding         BLOB,
                TextContent       TEXT
            )";
        command.ExecuteNonQuery();

        // Try to create the vec virtual table
        try
        {
            command.CommandText = @"
                CREATE VIRTUAL TABLE IF NOT EXISTS vec_memories USING vec0(
                    embedding float[384]
                )";
            command.ExecuteNonQuery();

            Console.WriteLine("🧠 THE ARCHIVIST: vec virtual table created");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ WARNING: Could not create vec virtual table: {ex.Message}");
            Console.WriteLine("   - Using fallback similarity search");
        }

        Console.WriteLine("🧠 THE ARCHIVIST: Database schema initialized");
    }

    /// <summary>
    /// Extracts all text content from a UI element tree.
    /// </summary>
    private string ExtractTextContent(UiaElementNode node)
    {
        var textBuilder = new StringBuilder();
        ExtractTextRecursive(node, textBuilder);
        
        var content = textBuilder.ToString().Trim();
        
        // Normalize whitespace
        content = System.Text.RegularExpressions.Regex.Replace(content, @"\s+", " ");
        
        return content;
    }

    /// <summary>
    /// Recursively extracts text from UI element nodes.
    /// </summary>
    private void ExtractTextRecursive(UiaElementNode node, StringBuilder textBuilder)
    {
        // Add node's own text content
        if (!string.IsNullOrWhiteSpace(node.Name))
        {
            textBuilder.Append(node.Name).Append(" ");
        }

        if (!string.IsNullOrWhiteSpace(node.ControlType))
        {
            textBuilder.Append(node.ControlType).Append(" ");
        }

        if (!string.IsNullOrWhiteSpace(node.AutomationId))
        {
            textBuilder.Append(node.AutomationId).Append(" ");
        }

        // Add properties that might contain text
        foreach (var property in node.Properties)
        {
            if (property.Value is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
            {
                textBuilder.Append(stringValue).Append(" ");
            }
        }

        // Process children
        if (node.Children != null)
        {
            foreach (var child in node.Children)
            {
                ExtractTextRecursive(child, textBuilder);
            }
        }
    }

    /// <summary>
    /// Finds the most similar memory using vector search.
    /// </summary>
    private async Task<SimilarMemory?> FindSimilarMemoryAsync(float[] embedding)
    {
        try
        {
            // Try vec search first
            var vecResult = await TryVecSearchAsync(embedding);
            if (vecResult != null)
                return vecResult;

            // Fallback to manual similarity calculation
            return await FallbackSimilaritySearchAsync(embedding);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ MEMORY SEARCH ERROR: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Attempts to use vec for similarity search.
    /// </summary>
    private async Task<SimilarMemory?> TryVecSearchAsync(float[] embedding)
    {
        try
        {
            var command = _connection.CreateCommand();
            command.CommandText = @"
                SELECT m.ID, m.ObservationCount, v.distance
                FROM vec_memories v
                JOIN Memories m ON m.rowid = v.rowid
                WHERE v.embedding MATCH ?
                ORDER BY v.distance ASC
                LIMIT 1";

            // Convert embedding to JSON format for sqlite-vec
            var embeddingJson = "[" + string.Join(",", embedding.Select(f => f.ToString("F6"))) + "]";
            command.Parameters.AddWithValue("@embedding", embeddingJson);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                var distance = reader.GetDouble(2); // distance column
                var similarity = 1.0 - distance; // Convert distance to similarity

                return new SimilarMemory
                {
                    Id = reader.GetInt64(0), // ID column
                    ObservationCount = reader.GetInt32(1), // ObservationCount column
                    Similarity = similarity
                };
            }
        }
        catch
        {
            // vec not available, will use fallback
        }

        return null;
    }

    /// <summary>
    /// Fallback similarity search using manual cosine similarity calculation.
    /// </summary>
    private async Task<SimilarMemory?> FallbackSimilaritySearchAsync(float[] embedding)
    {
        var command = _connection.CreateCommand();
        command.CommandText = "SELECT ID, ObservationCount, Embedding FROM Memories";
        
        SimilarMemory? bestMatch = null;
        double bestSimilarity = 0;

        using var reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            var storedEmbedding = DeserializeEmbedding((byte[])reader[2]); // Embedding column
            var similarity = CalculateCosineSimilarity(embedding, storedEmbedding);

            if (similarity > bestSimilarity)
            {
                bestSimilarity = similarity;
                bestMatch = new SimilarMemory
                {
                    Id = reader.GetInt64(0), // ID column
                    ObservationCount = reader.GetInt32(1), // ObservationCount column
                    Similarity = similarity
                };
            }
        }

        return bestMatch;
    }

    /// <summary>
    /// Updates an existing memory's observation count and timestamp.
    /// </summary>
    private async Task UpdateMemoryAsync(long memoryId)
    {
        var command = _connection.CreateCommand();
        command.CommandText = @"
            UPDATE Memories
            SET ObservationCount = ObservationCount + 1,
                Timestamp = @timestamp
            WHERE ID = @id";

        command.Parameters.AddWithValue("@timestamp", DateTime.UtcNow.ToString("O"));
        command.Parameters.AddWithValue("@id", memoryId);
        
        await command.ExecuteNonQueryAsync();
    }

    /// <summary>
    /// Stores a new memory with embedding and content.
    /// </summary>
    private async Task StoreNewMemoryAsync(float[] embedding, string textContent)
    {
        var command = _connection.CreateCommand();
        command.CommandText = @"
            INSERT INTO Memories (Timestamp, ObservationCount, Embedding, TextContent)
            VALUES (@timestamp, 1, @embedding, @textContent)";

        command.Parameters.AddWithValue("@timestamp", DateTime.UtcNow.ToString("O"));
        command.Parameters.AddWithValue("@embedding", SerializeEmbedding(embedding));
        command.Parameters.AddWithValue("@textContent", textContent);

        await command.ExecuteNonQueryAsync();

        // Get the last inserted row ID
        var idCommand = _connection.CreateCommand();
        idCommand.CommandText = "SELECT last_insert_rowid()";
        var memoryId = (long)(await idCommand.ExecuteScalarAsync())!;

        // Try to insert into vec table
        try
        {
            var vecCommand = _connection.CreateCommand();
            vecCommand.CommandText = "INSERT INTO vec_memories (rowid, embedding) VALUES (@rowid, @embedding)";
            vecCommand.Parameters.AddWithValue("@rowid", memoryId);

            // Convert embedding to JSON format for sqlite-vec
            var embeddingJson = "[" + string.Join(",", embedding.Select(f => f.ToString("F6"))) + "]";
            vecCommand.Parameters.AddWithValue("@embedding", embeddingJson);
            await vecCommand.ExecuteNonQueryAsync();
        }
        catch
        {
            // vec insert failed, but main memory is stored
        }
    }

    /// <summary>
    /// Serializes a float array embedding to byte array for storage.
    /// </summary>
    private byte[] SerializeEmbedding(float[] embedding)
    {
        var bytes = new byte[embedding.Length * sizeof(float)];
        Buffer.BlockCopy(embedding, 0, bytes, 0, bytes.Length);
        return bytes;
    }

    /// <summary>
    /// Deserializes a byte array back to float array embedding.
    /// </summary>
    private float[] DeserializeEmbedding(byte[] bytes)
    {
        var embedding = new float[bytes.Length / sizeof(float)];
        Buffer.BlockCopy(bytes, 0, embedding, 0, bytes.Length);
        return embedding;
    }

    /// <summary>
    /// Calculates cosine similarity between two embedding vectors.
    /// </summary>
    private double CalculateCosineSimilarity(float[] a, float[] b)
    {
        if (a.Length != b.Length)
            return 0;

        double dotProduct = 0;
        double normA = 0;
        double normB = 0;

        for (int i = 0; i < a.Length; i++)
        {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }

        if (normA == 0 || normB == 0)
            return 0;

        return dotProduct / (Math.Sqrt(normA) * Math.Sqrt(normB));
    }

    /// <summary>
    /// Disposes the MemoryVault and releases database resources.
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _connection?.Dispose();
            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// Finalizer to ensure proper cleanup.
    /// </summary>
    ~MemoryVault()
    {
        Dispose();
    }
}

/// <summary>
/// Represents a similar memory found during vector search.
/// </summary>
internal class SimilarMemory
{
    public long Id { get; set; }
    public int ObservationCount { get; set; }
    public double Similarity { get; set; }
}

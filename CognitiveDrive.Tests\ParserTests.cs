using System;
using System.Collections.Generic;
using System.Windows.Automation;
using CognitiveDrive.CoreUIA;
using Moq;
using NUnit.Framework;

namespace CognitiveDrive.Tests
{
    /// <summary>
    /// Unit tests for the resilient HET parser to verify stack overflow prevention
    /// and proper handling of deeply nested UI hierarchies.
    /// </summary>
    [TestFixture]
    public class ParserTests
    {
        /// <summary>
        /// Tests that the iterative BuildHET method can handle deeply nested UI trees
        /// without causing stack overflow exceptions.
        /// </summary>
        [Test]
        public void BuildHET_WithDeepNesting_DoesNotCauseStackOverflow()
        {
            // Arrange: Create a mock AutomationElement tree 200 levels deep
            var mockElements = CreateDeepMockElementTree(200);
            var rootElement = mockElements[0];

            // Act & Assert: This should not throw a StackOverflowException
            Assert.DoesNotThrow(() =>
            {
                var result = UiaScanner.BuildHET(rootElement);
                
                // Verify the structure was built correctly
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Name, Is.EqualTo("Root Element"));
                
                // Verify deep nesting was handled
                var currentNode = result;
                int depth = 0;
                while (currentNode.Children.Count > 0)
                {
                    currentNode = currentNode.Children[0];
                    depth++;
                }
                
                // Should have processed all 200 levels
                Assert.That(depth, Is.EqualTo(199), "Should have processed 199 child levels (200 total elements)");
            });
        }

        /// <summary>
        /// Tests that BuildHET properly handles null input.
        /// </summary>
        [Test]
        public void BuildHET_WithNullElement_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => UiaScanner.BuildHET(null));
        }

        /// <summary>
        /// Tests that BuildHET handles elements with missing properties gracefully.
        /// </summary>
        [Test]
        public void BuildHET_WithElementPropertyErrors_CreatesErrorNodes()
        {
            // Arrange: Create a mock element that throws exceptions when accessing properties
            var mockElement = new Mock<AutomationElement>();
            
            // Setup the mock to throw exceptions for property access
            mockElement.Setup(e => e.GetCurrentPropertyValue(AutomationElement.NameProperty, true))
                      .Throws(new ElementNotAvailableException("Element not available"));
            
            mockElement.Setup(e => e.GetCurrentPropertyValue(AutomationElement.ControlTypeProperty, true))
                      .Throws(new ElementNotAvailableException("Element not available"));
            
            mockElement.Setup(e => e.GetCurrentPropertyValue(AutomationElement.AutomationIdProperty, true))
                      .Throws(new ElementNotAvailableException("Element not available"));

            // Act
            var result = UiaScanner.BuildHET(mockElement.Object);

            // Assert: Should create a node with error indicators
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Contains.Substring("Error"));
            Assert.That(result.ControlType, Is.EqualTo("ControlType.Custom"));
            Assert.That(result.AutomationId, Contains.Substring("Error"));
        }

        /// <summary>
        /// Tests that BuildHET handles elements with children correctly.
        /// </summary>
        [Test]
        public void BuildHET_WithChildren_BuildsCompleteHierarchy()
        {
            // Arrange: Create a simple mock element tree
            var mockElements = CreateSimpleMockElementTree();
            var rootElement = mockElements["root"];

            // Act
            var result = UiaScanner.BuildHET(rootElement);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Root"));
            Assert.That(result.Children.Count, Is.EqualTo(2));
            
            Assert.That(result.Children[0].Name, Is.EqualTo("Child1"));
            Assert.That(result.Children[1].Name, Is.EqualTo("Child2"));
            
            // Check grandchildren
            Assert.That(result.Children[0].Children.Count, Is.EqualTo(1));
            Assert.That(result.Children[0].Children[0].Name, Is.EqualTo("Grandchild1"));
        }

        /// <summary>
        /// Creates a deep mock AutomationElement tree for testing stack overflow prevention.
        /// </summary>
        /// <param name="depth">The depth of the tree to create</param>
        /// <returns>Array of mock AutomationElements, with [0] being the root</returns>
        private AutomationElement[] CreateDeepMockElementTree(int depth)
        {
            var elements = new AutomationElement[depth];
            var mocks = new Mock<AutomationElement>[depth];

            // Create all mock elements
            for (int i = 0; i < depth; i++)
            {
                mocks[i] = new Mock<AutomationElement>();
                elements[i] = mocks[i].Object;

                // Setup basic properties
                mocks[i].Setup(e => e.GetCurrentPropertyValue(AutomationElement.NameProperty, true))
                        .Returns(i == 0 ? "Root Element" : $"Element {i}");
                
                mocks[i].Setup(e => e.GetCurrentPropertyValue(AutomationElement.ControlTypeProperty, true))
                        .Returns(ControlType.Pane);
                
                mocks[i].Setup(e => e.GetCurrentPropertyValue(AutomationElement.AutomationIdProperty, true))
                        .Returns($"element_{i}");
                
                mocks[i].Setup(e => e.GetCurrentPropertyValue(AutomationElement.IsEnabledProperty, true))
                        .Returns(true);
                
                mocks[i].Setup(e => e.GetCurrentPropertyValue(AutomationElement.IsOffscreenProperty, true))
                        .Returns(false);
                
                mocks[i].Setup(e => e.GetCurrentPropertyValue(AutomationElement.BoundingRectangleProperty, true))
                        .Returns(new System.Windows.Rect(i * 10, i * 10, 100, 50));
            }

            // Setup parent-child relationships
            var walker = TreeWalker.RawViewWalker;
            for (int i = 0; i < depth - 1; i++)
            {
                // Each element has the next element as its only child
                var childMock = Mock.Get(elements[i + 1]);
                mocks[i].Setup(e => TreeWalker.RawViewWalker.GetFirstChild(e))
                        .Returns(elements[i + 1]);
                
                // No siblings for this test
                mocks[i].Setup(e => TreeWalker.RawViewWalker.GetNextSibling(e))
                        .Returns((AutomationElement)null);
            }

            // Last element has no children
            mocks[depth - 1].Setup(e => TreeWalker.RawViewWalker.GetFirstChild(e))
                           .Returns((AutomationElement)null);

            return elements;
        }

        /// <summary>
        /// Creates a simple mock AutomationElement tree for basic hierarchy testing.
        /// </summary>
        /// <returns>Dictionary of mock elements by name</returns>
        private Dictionary<string, AutomationElement> CreateSimpleMockElementTree()
        {
            var elements = new Dictionary<string, AutomationElement>();
            var mocks = new Dictionary<string, Mock<AutomationElement>>();

            // Create elements
            var elementNames = new[] { "root", "child1", "child2", "grandchild1" };
            foreach (var name in elementNames)
            {
                mocks[name] = new Mock<AutomationElement>();
                elements[name] = mocks[name].Object;

                // Setup basic properties
                mocks[name].Setup(e => e.GetCurrentPropertyValue(AutomationElement.NameProperty, true))
                          .Returns(char.ToUpper(name[0]) + name.Substring(1));
                
                mocks[name].Setup(e => e.GetCurrentPropertyValue(AutomationElement.ControlTypeProperty, true))
                          .Returns(ControlType.Pane);
                
                mocks[name].Setup(e => e.GetCurrentPropertyValue(AutomationElement.AutomationIdProperty, true))
                          .Returns(name);
                
                mocks[name].Setup(e => e.GetCurrentPropertyValue(AutomationElement.IsEnabledProperty, true))
                          .Returns(true);
                
                mocks[name].Setup(e => e.GetCurrentPropertyValue(AutomationElement.IsOffscreenProperty, true))
                          .Returns(false);
                
                mocks[name].Setup(e => e.GetCurrentPropertyValue(AutomationElement.BoundingRectangleProperty, true))
                          .Returns(new System.Windows.Rect(0, 0, 100, 50));
            }

            // Setup relationships
            // Root has child1 and child2
            mocks["root"].Setup(e => TreeWalker.RawViewWalker.GetFirstChild(e))
                        .Returns(elements["child1"]);
            
            // child1 has child2 as sibling and grandchild1 as child
            mocks["child1"].Setup(e => TreeWalker.RawViewWalker.GetNextSibling(e))
                          .Returns(elements["child2"]);
            mocks["child1"].Setup(e => TreeWalker.RawViewWalker.GetFirstChild(e))
                          .Returns(elements["grandchild1"]);
            
            // child2 has no siblings or children
            mocks["child2"].Setup(e => TreeWalker.RawViewWalker.GetNextSibling(e))
                          .Returns((AutomationElement)null);
            mocks["child2"].Setup(e => TreeWalker.RawViewWalker.GetFirstChild(e))
                          .Returns((AutomationElement)null);
            
            // grandchild1 has no siblings or children
            mocks["grandchild1"].Setup(e => TreeWalker.RawViewWalker.GetNextSibling(e))
                               .Returns((AutomationElement)null);
            mocks["grandchild1"].Setup(e => TreeWalker.RawViewWalker.GetFirstChild(e))
                               .Returns((AutomationElement)null);

            return elements;
        }
    }
}

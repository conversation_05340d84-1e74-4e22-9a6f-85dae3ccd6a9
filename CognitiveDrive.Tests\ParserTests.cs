using System;
using System.Collections.Generic;
using System.Windows.Automation;
using CognitiveDrive.CoreUIA;
using NUnit.Framework;

namespace CognitiveDrive.Tests
{
    /// <summary>
    /// Unit tests for the resilient HET parser to verify stack overflow prevention
    /// and proper handling of deeply nested UI hierarchies.
    /// </summary>
    [TestFixture]
    public class ParserTests
    {
        /// <summary>
        /// Tests that the iterative BuildHET method can handle deeply nested UI trees
        /// without causing stack overflow exceptions by testing with a real AutomationElement.
        /// </summary>
        [Test]
        public void BuildHET_WithRealElement_DoesNotCauseStackOverflow()
        {
            // Arrange: Use AutomationElement.RootElement which is always available
            var rootElement = AutomationElement.RootElement;

            // Act & Assert: This should not throw a StackOverflowException
            Assert.DoesNotThrow(() =>
            {
                var result = UiaScanner.BuildHET(rootElement);

                // Verify the structure was built correctly
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Name, Is.Not.Null);
                Assert.That(result.ControlType, Is.Not.Null);

                // The desktop should have at least some elements
                Assert.That(result.TotalDescendantCount, Is.GreaterThan(0));
            });
        }

        /// <summary>
        /// Tests that BuildHET properly handles null input.
        /// </summary>
        [Test]
        public void BuildHET_WithNullElement_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => UiaScanner.BuildHET(null));
        }



        /// <summary>
        /// Tests that BuildHET can handle elements with valid properties.
        /// </summary>
        [Test]
        public void BuildHET_WithValidElement_CreatesValidNode()
        {
            // Arrange: Use a real AutomationElement (desktop root)
            var rootElement = AutomationElement.RootElement;

            // Act
            var result = UiaScanner.BuildHET(rootElement);

            // Assert: Should create a valid node
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.Not.Null);
            Assert.That(result.ControlType, Is.Not.Null);
            Assert.That(result.ControlType, Does.StartWith("ControlType."));
        }

        /// <summary>
        /// Tests that BuildHET can handle elements with children by using a real window.
        /// </summary>
        [Test]
        public void BuildHET_WithChildren_BuildsCompleteHierarchy()
        {
            // Arrange: Try to find a real window with children (like VS Code if available)
            AutomationElement targetElement = null;

            try
            {
                // Try to find VS Code window first
                targetElement = UiaScanner.FindWindowByProcessName("Code");
            }
            catch
            {
                // Fallback to desktop root which always has children
                targetElement = AutomationElement.RootElement;
            }

            // Act
            var result = UiaScanner.BuildHET(targetElement);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.Not.Null);

            // Should have some children (desktop always has windows, VS Code has UI elements)
            Assert.That(result.TotalDescendantCount, Is.GreaterThan(0));
        }

        /// <summary>
        /// Tests that the iterative algorithm can handle reasonable depth without issues.
        /// This test verifies the algorithm works correctly by testing with real UI elements.
        /// </summary>
        [Test]
        public void BuildHET_IterativeAlgorithm_HandlesDepthCorrectly()
        {
            // Arrange: Use desktop root which typically has multiple levels of nesting
            var rootElement = AutomationElement.RootElement;

            // Act: Build HET and measure performance
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = UiaScanner.BuildHET(rootElement);
            stopwatch.Stop();

            // Assert: Should complete in reasonable time and produce valid results
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.Not.Null);
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(10000), "Should complete within 10 seconds");

            // Desktop should have multiple windows/elements
            Assert.That(result.TotalDescendantCount, Is.GreaterThan(0));

            Console.WriteLine($"BuildHET processed {result.TotalDescendantCount + 1} elements in {stopwatch.ElapsedMilliseconds}ms");
        }
    }
}

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Timer;
import java.util.TimerTask;

/**
 * A hostile Java Swing application designed to test CognitiveDrive's robustness.
 * This app intentionally violates accessibility standards and creates challenging UI scenarios.
 */
public class HostileSwingApp extends JFrame {
    private JLabel dynamicLabel;
    private JPanel chaosPanel;
    private Timer chaosTimer;
    private int updateCounter = 0;

    public HostileSwingApp() {
        setTitle("Hostile Swing App - Accessibility Nightmare");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        
        initializeComponents();
        startChaosMode();
    }

    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        // Panel with no accessibility information
        JPanel topPanel = new JPanel();
        topPanel.setBackground(Color.RED);
        // Intentionally no accessible name or description
        
        // Button with misleading accessibility info
        JButton misleadingButton = new JButton("Click Me");
        misleadingButton.setName(""); // Empty name
        misleadingButton.getAccessibleContext().setAccessibleName(""); // Empty accessible name
        misleadingButton.getAccessibleContext().setAccessibleDescription(""); // Empty description
        misleadingButton.addActionListener(e -> {
            // Do nothing - misleading button
        });
        
        // Text field with no labels
        JTextField unlabeledField = new JTextField(20);
        unlabeledField.setName(""); // No name
        unlabeledField.getAccessibleContext().setAccessibleName(""); // No accessible name
        
        // Dynamic label that changes rapidly
        dynamicLabel = new JLabel("Dynamic Content: 0");
        dynamicLabel.setName(""); // No name for accessibility
        
        // Panel that will have rapidly changing content
        chaosPanel = new JPanel();
        chaosPanel.setLayout(new GridLayout(0, 1));
        chaosPanel.setBackground(Color.YELLOW);
        
        // Add components with poor accessibility
        topPanel.add(misleadingButton);
        topPanel.add(unlabeledField);
        topPanel.add(dynamicLabel);
        
        add(topPanel, BorderLayout.NORTH);
        add(new JScrollPane(chaosPanel), BorderLayout.CENTER);
        
        // Bottom panel with overlapping components
        JPanel bottomPanel = new JPanel();
        bottomPanel.setLayout(null); // Absolute positioning - bad for accessibility
        
        JButton overlappingButton1 = new JButton("Button 1");
        overlappingButton1.setBounds(10, 10, 100, 30);
        overlappingButton1.setName(""); // No name
        
        JButton overlappingButton2 = new JButton("Button 2");
        overlappingButton2.setBounds(50, 10, 100, 30); // Overlapping
        overlappingButton2.setName(""); // No name
        
        bottomPanel.add(overlappingButton1);
        bottomPanel.add(overlappingButton2);
        bottomPanel.setPreferredSize(new Dimension(800, 100));
        
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private void startChaosMode() {
        // Timer that rapidly changes the UI structure
        chaosTimer = new Timer();
        chaosTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                SwingUtilities.invokeLater(() -> {
                    updateCounter++;
                    
                    // Update dynamic label
                    dynamicLabel.setText("Dynamic Content: " + updateCounter);
                    
                    // Rapidly add/remove components from chaos panel
                    chaosPanel.removeAll();
                    
                    // Add random number of components
                    int componentCount = (updateCounter % 10) + 1;
                    for (int i = 0; i < componentCount; i++) {
                        JLabel chaosLabel = new JLabel("Chaos " + i + " (Update " + updateCounter + ")");
                        chaosLabel.setName(""); // No accessibility name
                        chaosLabel.getAccessibleContext().setAccessibleName(""); // No accessible name
                        chaosPanel.add(chaosLabel);
                    }
                    
                    // Force repaint and revalidate
                    chaosPanel.revalidate();
                    chaosPanel.repaint();
                });
            }
        }, 100, 100); // Update every 100ms - very rapid
    }

    public static void main(String[] args) {
        // Disable accessibility features to make it even more hostile
        System.setProperty("javax.accessibility.assistive_technologies", "");
        
        SwingUtilities.invokeLater(() -> {
            new HostileSwingApp().setVisible(true);
            
            System.out.println("Hostile Swing App started - designed to challenge accessibility tools");
            System.out.println("This app intentionally:");
            System.out.println("- Has no accessibility names/descriptions");
            System.out.println("- Uses absolute positioning");
            System.out.println("- Rapidly changes UI structure every 100ms");
            System.out.println("- Has overlapping components");
            System.out.println("- Disables accessibility features");
        });
    }
}

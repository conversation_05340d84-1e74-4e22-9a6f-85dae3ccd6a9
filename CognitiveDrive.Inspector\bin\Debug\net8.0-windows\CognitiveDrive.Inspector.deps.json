{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"CognitiveDrive.Inspector/1.0.0": {"dependencies": {"CognitiveDrive.CoreUIA": "1.0.0", "System.CommandLine": "2.0.0-beta4.22272.1"}, "runtime": {"CognitiveDrive.Inspector.dll": {}}}, "System.CommandLine/2.0.0-beta4.22272.1": {"runtime": {"lib/net6.0/System.CommandLine.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.22.27201"}}, "resources": {"lib/net6.0/cs/System.CommandLine.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.CommandLine.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.CommandLine.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.CommandLine.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.CommandLine.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.CommandLine.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.CommandLine.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.CommandLine.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.CommandLine.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.CommandLine.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.CommandLine.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.CommandLine.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.CommandLine.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "CognitiveDrive.CoreUIA/1.0.0": {"runtime": {"CognitiveDrive.CoreUIA.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"CognitiveDrive.Inspector/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "System.CommandLine/2.0.0-beta4.22272.1": {"type": "package", "serviceable": true, "sha512": "sha512-1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg==", "path": "system.commandline/2.0.0-beta4.22272.1", "hashPath": "system.commandline.2.0.0-beta4.22272.1.nupkg.sha512"}, "CognitiveDrive.CoreUIA/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}
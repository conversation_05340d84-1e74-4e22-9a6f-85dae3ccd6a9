{"name": "Untitled - Notepad", "controlType": "ControlType.Window", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 320, "y": 320}, "size": {"isEmpty": false, "width": 679, "height": 440}, "x": 320, "y": 320, "width": 679, "height": 440, "left": 320, "top": 320, "right": 999, "bottom": 760, "topLeft": {"x": 320, "y": 320}, "topRight": {"x": 999, "y": 320}, "bottomLeft": {"x": 320, "y": 760}, "bottomRight": {"x": 999, "y": 760}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 328, "y": 413}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 328, "y": 413, "width": 663, "height": 299, "left": 328, "top": 413, "right": 991, "bottom": 712, "topLeft": {"x": 328, "y": 413}, "topRight": {"x": 991, "y": 413}, "bottomLeft": {"x": 328, "y": 712}, "bottomRight": {"x": 991, "y": 712}}, "children": [{"name": "Text editor", "controlType": "ControlType.Document", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 328, "y": 413}, "size": {"isEmpty": false, "width": 663, "height": 299}, "x": 328, "y": 413, "width": 663, "height": 299, "left": 328, "top": 413, "right": 991, "bottom": 712, "topLeft": {"x": 328, "y": 413}, "topRight": {"x": 991, "y": 413}, "bottomLeft": {"x": 328, "y": 712}, "bottomRight": {"x": 991, "y": 712}}, "children": []}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 378, "y": 332}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 378, "y": 332, "width": 359, "height": 40, "left": 378, "top": 332, "right": 737, "bottom": 372, "topLeft": {"x": 378, "y": 332}, "topRight": {"x": 737, "y": 332}, "bottomLeft": {"x": 378, "y": 372}, "bottomRight": {"x": 737, "y": 372}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 378, "y": 332}, "size": {"isEmpty": false, "width": 287, "height": 32}, "x": 378, "y": 332, "width": 287, "height": 32, "left": 378, "top": 332, "right": 665, "bottom": 364, "topLeft": {"x": 378, "y": 332}, "topRight": {"x": 665, "y": 332}, "bottomLeft": {"x": 378, "y": 364}, "bottomRight": {"x": 665, "y": 364}}, "children": [{"name": "", "controlType": "ControlType.Tab", "automationId": "Tabs", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 378, "y": 332}, "size": {"isEmpty": false, "width": 359, "height": 40}, "x": 378, "y": 332, "width": 359, "height": 40, "left": 378, "top": 332, "right": 737, "bottom": 372, "topLeft": {"x": 378, "y": 332}, "topRight": {"x": 737, "y": 332}, "bottomLeft": {"x": 378, "y": 372}, "bottomRight": {"x": 737, "y": 372}}, "children": [{"name": "", "controlType": "ControlType.List", "automationId": "TabListView", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 380, "y": 332}, "size": {"isEmpty": false, "width": 312, "height": 40}, "x": 380, "y": 332, "width": 312, "height": 40, "left": 380, "top": 332, "right": 692, "bottom": 372, "topLeft": {"x": 380, "y": 332}, "topRight": {"x": 692, "y": 332}, "bottomLeft": {"x": 380, "y": 372}, "bottomRight": {"x": 692, "y": 372}}, "children": [{"name": "Untitled. Unmodified.", "controlType": "ControlType.TabItem", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 383, "y": 332}, "size": {"isEmpty": false, "width": 307, "height": 40}, "x": 383, "y": 332, "width": 307, "height": 40, "left": 383, "top": 332, "right": 690, "bottom": 372, "topLeft": {"x": 383, "y": 332}, "topRight": {"x": 690, "y": 332}, "bottomLeft": {"x": 383, "y": 372}, "bottomRight": {"x": 690, "y": 372}}, "children": [{"name": "Untitled", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 399, "y": 342}, "size": {"isEmpty": false, "width": 56, "height": 19}, "x": 399, "y": 342, "width": 56, "height": 19, "left": 399, "top": 342, "right": 455, "bottom": 361, "topLeft": {"x": 399, "y": 342}, "topRight": {"x": 455, "y": 342}, "bottomLeft": {"x": 399, "y": 361}, "bottomRight": {"x": 455, "y": 361}}, "children": []}, {"name": "Close Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "CloseButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 639, "y": 337}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 639, "y": 337, "width": 40, "height": 30, "left": 639, "top": 337, "right": 679, "bottom": 367, "topLeft": {"x": 639, "y": 337}, "topRight": {"x": 679, "y": 337}, "bottomLeft": {"x": 639, "y": 367}, "bottomRight": {"x": 679, "y": 367}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 652, "y": 344}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 652, "y": 344, "width": 15, "height": 15, "left": 652, "top": 344, "right": 667, "bottom": 359, "topLeft": {"x": 652, "y": 344}, "topRight": {"x": 667, "y": 344}, "bottomLeft": {"x": 652, "y": 359}, "bottomRight": {"x": 667, "y": 359}}, "children": []}]}]}]}, {"name": "Add New Tab", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AddButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 697, "y": 338}, "size": {"isEmpty": false, "width": 40, "height": 30}, "x": 697, "y": 338, "width": 40, "height": 30, "left": 697, "top": 338, "right": 737, "bottom": 368, "topLeft": {"x": 697, "y": 338}, "topRight": {"x": 737, "y": 338}, "bottomLeft": {"x": 697, "y": 368}, "bottomRight": {"x": 737, "y": 368}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 709, "y": 345}, "size": {"isEmpty": false, "width": 15, "height": 15}, "x": 709, "y": 345, "width": 15, "height": 15, "left": 709, "top": 345, "right": 724, "bottom": 360, "topLeft": {"x": 709, "y": 345}, "topRight": {"x": 724, "y": 345}, "bottomLeft": {"x": 709, "y": 360}, "bottomRight": {"x": 724, "y": 360}}, "children": []}]}]}, {"name": "Notepad automatically saves your progress. All your content will be available the next time you open Notepad.", "controlType": "ControlType.Pane", "automationId": "TeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 328, "y": 372}, "size": {"isEmpty": false, "width": 663, "height": 41}, "x": 328, "y": 372, "width": 663, "height": 41, "left": 328, "top": 372, "right": 991, "bottom": 413, "topLeft": {"x": 328, "y": 372}, "topRight": {"x": 991, "y": 372}, "bottomLeft": {"x": 328, "y": 413}, "bottomRight": {"x": 991, "y": 413}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 328, "y": 372}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 328, "y": 372, "width": 530, "height": 32, "left": 328, "top": 372, "right": 858, "bottom": 404, "topLeft": {"x": 328, "y": 372}, "topRight": {"x": 858, "y": 372}, "bottomLeft": {"x": 328, "y": 404}, "bottomRight": {"x": 858, "y": 404}}, "children": [{"name": "", "controlType": "ControlType.MenuBar", "automationId": "<PERSON><PERSON><PERSON><PERSON>", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 328, "y": 372}, "size": {"isEmpty": false, "width": 199, "height": 40}, "x": 328, "y": 372, "width": 199, "height": 40, "left": 328, "top": 372, "right": 527, "bottom": 412, "topLeft": {"x": 328, "y": 372}, "topRight": {"x": 527, "y": 372}, "bottomLeft": {"x": 328, "y": 412}, "bottomRight": {"x": 527, "y": 412}}, "children": [{"name": "File", "controlType": "ControlType.MenuItem", "automationId": "File", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 333, "y": 372}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 333, "y": 372, "width": 52, "height": 40, "left": 333, "top": 372, "right": 385, "bottom": 412, "topLeft": {"x": 333, "y": 372}, "topRight": {"x": 385, "y": 372}, "bottomLeft": {"x": 333, "y": 412}, "bottomRight": {"x": 385, "y": 412}}, "children": [{"name": "File", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 333, "y": 372}, "size": {"isEmpty": false, "width": 52, "height": 40}, "x": 333, "y": 372, "width": 52, "height": 40, "left": 333, "top": 372, "right": 385, "bottom": 412, "topLeft": {"x": 333, "y": 372}, "topRight": {"x": 385, "y": 372}, "bottomLeft": {"x": 333, "y": 412}, "bottomRight": {"x": 385, "y": 412}}, "children": [{"name": "File", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 345, "y": 380}, "size": {"isEmpty": false, "width": 27, "height": 24}, "x": 345, "y": 380, "width": 27, "height": 24, "left": 345, "top": 380, "right": 372, "bottom": 404, "topLeft": {"x": 345, "y": 380}, "topRight": {"x": 372, "y": 380}, "bottomLeft": {"x": 345, "y": 404}, "bottomRight": {"x": 372, "y": 404}}, "children": []}]}]}, {"name": "Edit", "controlType": "ControlType.MenuItem", "automationId": "Edit", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 395, "y": 372}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 395, "y": 372, "width": 55, "height": 40, "left": 395, "top": 372, "right": 450, "bottom": 412, "topLeft": {"x": 395, "y": 372}, "topRight": {"x": 450, "y": 372}, "bottomLeft": {"x": 395, "y": 412}, "bottomRight": {"x": 450, "y": 412}}, "children": [{"name": "Edit", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 395, "y": 372}, "size": {"isEmpty": false, "width": 55, "height": 40}, "x": 395, "y": 372, "width": 55, "height": 40, "left": 395, "top": 372, "right": 450, "bottom": 412, "topLeft": {"x": 395, "y": 372}, "topRight": {"x": 450, "y": 372}, "bottomLeft": {"x": 395, "y": 412}, "bottomRight": {"x": 450, "y": 412}}, "children": [{"name": "Edit", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 408, "y": 380}, "size": {"isEmpty": false, "width": 30, "height": 24}, "x": 408, "y": 380, "width": 30, "height": 24, "left": 408, "top": 380, "right": 438, "bottom": 404, "topLeft": {"x": 408, "y": 380}, "topRight": {"x": 438, "y": 380}, "bottomLeft": {"x": 408, "y": 404}, "bottomRight": {"x": 438, "y": 404}}, "children": []}]}]}, {"name": "View", "controlType": "ControlType.MenuItem", "automationId": "View", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 460, "y": 372}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 460, "y": 372, "width": 62, "height": 40, "left": 460, "top": 372, "right": 522, "bottom": 412, "topLeft": {"x": 460, "y": 372}, "topRight": {"x": 522, "y": 372}, "bottomLeft": {"x": 460, "y": 412}, "bottomRight": {"x": 522, "y": 412}}, "children": [{"name": "View", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "ContentButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 460, "y": 372}, "size": {"isEmpty": false, "width": 62, "height": 40}, "x": 460, "y": 372, "width": 62, "height": 40, "left": 460, "top": 372, "right": 522, "bottom": 412, "topLeft": {"x": 460, "y": 372}, "topRight": {"x": 522, "y": 372}, "bottomLeft": {"x": 460, "y": 412}, "bottomRight": {"x": 522, "y": 412}}, "children": [{"name": "View", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 472, "y": 380}, "size": {"isEmpty": false, "width": 37, "height": 24}, "x": 472, "y": 380, "width": 37, "height": 24, "left": 472, "top": 380, "right": 509, "bottom": 404, "topLeft": {"x": 472, "y": 380}, "topRight": {"x": 509, "y": 380}, "bottomLeft": {"x": 472, "y": 404}, "bottomRight": {"x": 509, "y": 404}}, "children": []}]}]}]}, {"name": "Headings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 563, "y": 372}, "size": {"isEmpty": false, "width": 71, "height": 40}, "x": 563, "y": 372, "width": 71, "height": 40, "left": 563, "top": 372, "right": 634, "bottom": 412, "topLeft": {"x": 563, "y": 372}, "topRight": {"x": 634, "y": 372}, "bottomLeft": {"x": 563, "y": 412}, "bottomRight": {"x": 634, "y": 412}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 577, "y": 382}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 577, "y": 382, "width": 18, "height": 18, "left": 577, "top": 382, "right": 595, "bottom": 400, "topLeft": {"x": 577, "y": 382}, "topRight": {"x": 595, "y": 382}, "bottomLeft": {"x": 577, "y": 400}, "bottomRight": {"x": 595, "y": 400}}, "children": []}]}, {"name": "Lists", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 634, "y": 372}, "size": {"isEmpty": false, "width": 70, "height": 40}, "x": 634, "y": 372, "width": 70, "height": 40, "left": 634, "top": 372, "right": 704, "bottom": 412, "topLeft": {"x": 634, "y": 372}, "topRight": {"x": 704, "y": 372}, "bottomLeft": {"x": 634, "y": 412}, "bottomRight": {"x": 704, "y": 412}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 648, "y": 382}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 648, "y": 382, "width": 18, "height": 18, "left": 648, "top": 382, "right": 666, "bottom": 400, "topLeft": {"x": 648, "y": 382}, "topRight": {"x": 666, "y": 382}, "bottomLeft": {"x": 648, "y": 400}, "bottomRight": {"x": 666, "y": 400}}, "children": []}]}, {"name": "Bold (Ctrl+B)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 704, "y": 372}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 704, "y": 372, "width": 40, "height": 40, "left": 704, "top": 372, "right": 744, "bottom": 412, "topLeft": {"x": 704, "y": 372}, "topRight": {"x": 744, "y": 372}, "bottomLeft": {"x": 704, "y": 412}, "bottomRight": {"x": 744, "y": 412}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 716, "y": 383}, "size": {"isEmpty": false, "width": 18, "height": 18}, "x": 716, "y": 383, "width": 18, "height": 18, "left": 716, "top": 383, "right": 734, "bottom": 401, "topLeft": {"x": 716, "y": 383}, "topRight": {"x": 734, "y": 383}, "bottomLeft": {"x": 716, "y": 401}, "bottomRight": {"x": 734, "y": 401}}, "children": []}]}, {"name": "Clear formatting (Ctrl+Space)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Link (Ctrl+K)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "Italic (Ctrl+I)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}, {"name": "More options", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "OverflowButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 745, "y": 372}, "size": {"isEmpty": false, "width": 40, "height": 40}, "x": 745, "y": 372, "width": 40, "height": 40, "left": 745, "top": 372, "right": 785, "bottom": 412, "topLeft": {"x": 745, "y": 372}, "topRight": {"x": 785, "y": 372}, "bottomLeft": {"x": 745, "y": 412}, "bottomRight": {"x": 785, "y": 412}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 756, "y": 383}, "size": {"isEmpty": false, "width": 17, "height": 18}, "x": 756, "y": 383, "width": 17, "height": 18, "left": 756, "top": 383, "right": 773, "bottom": 401, "topLeft": {"x": 756, "y": 383}, "topRight": {"x": 773, "y": 383}, "bottomLeft": {"x": 756, "y": 401}, "bottomRight": {"x": 773, "y": 401}}, "children": []}]}, {"name": "Copilot (Preview)", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "RewriteDropDownButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 822, "y": 372}, "size": {"isEmpty": false, "width": 73, "height": 40}, "x": 822, "y": 372, "width": 73, "height": 40, "left": 822, "top": 372, "right": 895, "bottom": 412, "topLeft": {"x": 822, "y": 372}, "topRight": {"x": 895, "y": 372}, "bottomLeft": {"x": 822, "y": 412}, "bottomRight": {"x": 895, "y": 412}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 836, "y": 381}, "size": {"isEmpty": false, "width": 19, "height": 20}, "x": 836, "y": 381, "width": 19, "height": 20, "left": 836, "top": 381, "right": 855, "bottom": 401, "topLeft": {"x": 836, "y": 381}, "topRight": {"x": 855, "y": 381}, "bottomLeft": {"x": 836, "y": 401}, "bottomRight": {"x": 855, "y": 401}}, "children": []}]}, {"name": "User avatar", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "AvatarButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 900, "y": 372}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 900, "y": 372, "width": 38, "height": 40, "left": 900, "top": 372, "right": 938, "bottom": 412, "topLeft": {"x": 900, "y": 372}, "topRight": {"x": 938, "y": 372}, "bottomLeft": {"x": 900, "y": 412}, "bottomRight": {"x": 938, "y": 412}}, "children": [{"name": "Person", "controlType": "ControlType.Text", "automationId": "Avatar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 906, "y": 380}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 906, "y": 380, "width": 25, "height": 25, "left": 906, "top": 380, "right": 931, "bottom": 405, "topLeft": {"x": 906, "y": 380}, "topRight": {"x": 931, "y": 380}, "bottomLeft": {"x": 906, "y": 405}, "bottomRight": {"x": 931, "y": 405}}, "children": [{"name": "", "controlType": "ControlType.Text", "automationId": "InitialsTextBlock", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "", "controlType": "ControlType.ProgressBar", "automationId": "LoadingRing", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 906, "y": 380}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 906, "y": 380, "width": 25, "height": 25, "left": 906, "top": 380, "right": 931, "bottom": 405, "topLeft": {"x": 906, "y": 380}, "topRight": {"x": 931, "y": 380}, "bottomLeft": {"x": 906, "y": 405}, "bottomRight": {"x": 931, "y": 405}}, "children": [{"name": "", "controlType": "ControlType.Image", "automationId": "<PERSON>tiePlayer", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 906, "y": 380}, "size": {"isEmpty": false, "width": 25, "height": 25}, "x": 906, "y": 380, "width": 25, "height": 25, "left": 906, "top": 380, "right": 931, "bottom": 405, "topLeft": {"x": 906, "y": 380}, "topRight": {"x": 931, "y": 380}, "bottomLeft": {"x": 906, "y": 405}, "bottomRight": {"x": 931, "y": 405}}, "children": []}]}, {"name": "Settings", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "SettingsButton", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 943, "y": 372}, "size": {"isEmpty": false, "width": 38, "height": 40}, "x": 943, "y": 372, "width": 38, "height": 40, "left": 943, "top": 372, "right": 981, "bottom": 412, "topLeft": {"x": 943, "y": 372}, "topRight": {"x": 981, "y": 372}, "bottomLeft": {"x": 943, "y": 412}, "bottomRight": {"x": 981, "y": 412}}, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "PrivacyTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}, {"name": "", "controlType": "ControlType.Window", "automationId": "CowriterTeachingTip", "isEnabled": true, "isOffscreen": true, "boundingRectangle": null, "children": []}]}]}, {"name": "DesktopWindowXamlSource", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 328, "y": 712}, "size": {"isEmpty": false, "width": 663, "height": 40}, "x": 328, "y": 712, "width": 663, "height": 40, "left": 328, "top": 712, "right": 991, "bottom": 752, "topLeft": {"x": 328, "y": 712}, "topRight": {"x": 991, "y": 712}, "bottomLeft": {"x": 328, "y": 752}, "bottomRight": {"x": 991, "y": 752}}, "children": [{"name": "", "controlType": "ControlType.Pane", "automationId": "", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 328, "y": 712}, "size": {"isEmpty": false, "width": 530, "height": 32}, "x": 328, "y": 712, "width": 530, "height": 32, "left": 328, "top": 712, "right": 858, "bottom": 744, "topLeft": {"x": 328, "y": 712}, "topRight": {"x": 858, "y": 712}, "bottomLeft": {"x": 328, "y": 744}, "bottomRight": {"x": 858, "y": 744}}, "children": [{"name": "Line 1,\nColumn 1", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 348, "y": 722}, "size": {"isEmpty": false, "width": 74, "height": 20}, "x": 348, "y": 722, "width": 74, "height": 20, "left": 348, "top": 722, "right": 422, "bottom": 742, "topLeft": {"x": 348, "y": 722}, "topRight": {"x": 422, "y": 722}, "bottomLeft": {"x": 348, "y": 742}, "bottomRight": {"x": 422, "y": 742}}, "children": []}, {"name": "0 characters", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 459, "y": 722}, "size": {"isEmpty": false, "width": 82, "height": 20}, "x": 459, "y": 722, "width": 82, "height": 20, "left": 459, "top": 722, "right": 541, "bottom": 742, "topLeft": {"x": 459, "y": 722}, "topRight": {"x": 541, "y": 722}, "bottomLeft": {"x": 459, "y": 742}, "bottomRight": {"x": 541, "y": 742}}, "children": []}, {"name": "Plain text", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 566, "y": 712}, "size": {"isEmpty": false, "width": 78, "height": 40}, "x": 566, "y": 712, "width": 78, "height": 40, "left": 566, "top": 712, "right": 644, "bottom": 752, "topLeft": {"x": 566, "y": 712}, "topRight": {"x": 644, "y": 712}, "bottomLeft": {"x": 566, "y": 752}, "bottomRight": {"x": 644, "y": 752}}, "children": []}, {"name": "Zoom", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 689, "y": 722}, "size": {"isEmpty": false, "width": 39, "height": 20}, "x": 689, "y": 722, "width": 39, "height": 20, "left": 689, "top": 722, "right": 728, "bottom": 742, "topLeft": {"x": 689, "y": 722}, "topRight": {"x": 728, "y": 722}, "bottomLeft": {"x": 689, "y": 742}, "bottomRight": {"x": 728, "y": 742}}, "children": []}, {"name": " Windows (CRLF)", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 761, "y": 722}, "size": {"isEmpty": false, "width": 94, "height": 20}, "x": 761, "y": 722, "width": 94, "height": 20, "left": 761, "top": 722, "right": 855, "bottom": 742, "topLeft": {"x": 761, "y": 722}, "topRight": {"x": 855, "y": 722}, "bottomLeft": {"x": 761, "y": 742}, "bottomRight": {"x": 855, "y": 742}}, "children": []}, {"name": " UTF-8", "controlType": "ControlType.Text", "automationId": "ContentTextBlock", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 877, "y": 722}, "size": {"isEmpty": false, "width": 45, "height": 20}, "x": 877, "y": 722, "width": 45, "height": 20, "left": 877, "top": 722, "right": 922, "bottom": 742, "topLeft": {"x": 877, "y": 722}, "topRight": {"x": 922, "y": 722}, "bottomLeft": {"x": 877, "y": 742}, "bottomRight": {"x": 922, "y": 742}}, "children": []}]}]}, {"name": "Untitled - Notepad", "controlType": "ControlType.TitleBar", "automationId": "TitleBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 329, "y": 329}, "size": {"isEmpty": false, "width": 661, "height": 29}, "x": 329, "y": 329, "width": 661, "height": 29, "left": 329, "top": 329, "right": 990, "bottom": 358, "topLeft": {"x": 329, "y": 329}, "topRight": {"x": 990, "y": 329}, "bottomLeft": {"x": 329, "y": 358}, "bottomRight": {"x": 990, "y": 358}}, "children": [{"name": "System Menu Bar", "controlType": "ControlType.MenuBar", "automationId": "SystemMenuBar", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 329, "y": 329}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 329, "y": 329, "width": 28, "height": 28, "left": 329, "top": 329, "right": 357, "bottom": 357, "topLeft": {"x": 329, "y": 329}, "topRight": {"x": 357, "y": 329}, "bottomLeft": {"x": 329, "y": 357}, "bottomRight": {"x": 357, "y": 357}}, "children": [{"name": "System", "controlType": "ControlType.MenuItem", "automationId": "Item 1", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 329, "y": 329}, "size": {"isEmpty": false, "width": 28, "height": 28}, "x": 329, "y": 329, "width": 28, "height": 28, "left": 329, "top": 329, "right": 357, "bottom": 357, "topLeft": {"x": 329, "y": 329}, "topRight": {"x": 357, "y": 329}, "bottomLeft": {"x": 329, "y": 357}, "bottomRight": {"x": 357, "y": 357}}, "children": []}]}, {"name": "Minimize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Minimize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 813, "y": 321}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 813, "y": 321, "width": 60, "height": 37, "left": 813, "top": 321, "right": 873, "bottom": 358, "topLeft": {"x": 813, "y": 321}, "topRight": {"x": 873, "y": 321}, "bottomLeft": {"x": 813, "y": 358}, "bottomRight": {"x": 873, "y": 358}}, "children": []}, {"name": "Maximize", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Maximize", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 873, "y": 321}, "size": {"isEmpty": false, "width": 59, "height": 37}, "x": 873, "y": 321, "width": 59, "height": 37, "left": 873, "top": 321, "right": 932, "bottom": 358, "topLeft": {"x": 873, "y": 321}, "topRight": {"x": 932, "y": 321}, "bottomLeft": {"x": 873, "y": 358}, "bottomRight": {"x": 932, "y": 358}}, "children": []}, {"name": "Close", "controlType": "ControlType.<PERSON><PERSON>", "automationId": "Close", "isEnabled": true, "isOffscreen": false, "boundingRectangle": {"isEmpty": false, "location": {"x": 932, "y": 321}, "size": {"isEmpty": false, "width": 60, "height": 37}, "x": 932, "y": 321, "width": 60, "height": 37, "left": 932, "top": 321, "right": 992, "bottom": 358, "topLeft": {"x": 932, "y": 321}, "topRight": {"x": 992, "y": 321}, "bottomLeft": {"x": 932, "y": 358}, "bottomRight": {"x": 992, "y": 358}}, "children": []}]}]}
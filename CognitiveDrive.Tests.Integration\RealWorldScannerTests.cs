using System.Diagnostics;
using FluentAssertions;
using NUnit.Framework;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Infrastructure;
using CognitiveDrive.Infrastructure.Interfaces;

namespace CognitiveDrive.Tests.Integration;

/// <summary>
/// Real-world integration tests that validate the scanner against actual Windows applications.
/// These tests use real applications to ensure production readiness and reliability.
/// </summary>
[TestFixture]
[Category("Integration")]
[Category("RealWorld")]
public class RealWorldScannerTests
{
    private WindowsUiaScanner? _scanner;
    private IUiaPropertyExtractor? _propertyExtractor;
    private IProcessManager? _processManager;
    private ScannerConfiguration? _configuration;

    [OneTimeSetUp]
    public void OneTimeSetUp()
    {
        // Initialize production-grade components
        _configuration = new ScannerConfiguration
        {
            MaxScanDepth = 25,
            MaxElementsPerScan = 5000,
            PropertyExtractionTimeout = TimeSpan.FromSeconds(2),
            ChildEnumerationTimeout = TimeSpan.FromSeconds(10),
            RetryCount = 3,
            RetryDelay = TimeSpan.FromMilliseconds(100)
        };

        // TODO: Implement these when we create the concrete classes
        // _propertyExtractor = new WindowsUiaPropertyExtractor(_configuration);
        // _processManager = new WindowsProcessManager();
        // _scanner = new WindowsUiaScanner(_propertyExtractor, _processManager, _configuration);
    }

    [OneTimeTearDown]
    public void OneTimeTearDown()
    {
        _scanner?.Dispose();
    }

    [Test]
    [Category("Notepad")]
    public async Task ScanNotepad_ShouldSucceed_AndProvideComprehensiveData()
    {
        // Arrange
        var notepadProcess = await LaunchNotepadAsync();
        
        try
        {
            // Act
            var result = await _scanner!.ScanProcessAsync(notepadProcess.Id);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue($"Scan should succeed, but got: {result.ErrorMessage}");
            result.Value.Should().NotBeNull();
            result.Duration.Should().BeLessThan(TimeSpan.FromSeconds(10), "Scan should complete within reasonable time");

            var rootElement = result.Value!;
            
            // Validate root element
            rootElement.Name.Should().Contain("Notepad", "Root element should be Notepad window");
            rootElement.ControlType.Should().Be("ControlType.Window");
            rootElement.ProcessId.Should().Be(notepadProcess.Id);
            rootElement.IsEnabled.Should().BeTrue();
            rootElement.Children.Should().NotBeEmpty("Notepad should have child elements");

            // Validate element tree structure
            rootElement.TotalDescendantCount.Should().BeGreaterThan(10, "Notepad should have multiple UI elements");
            rootElement.TotalDescendantCount.Should().BeLessThan(1000, "Element count should be reasonable");

            // Look for expected Notepad elements
            var menuBar = rootElement.FindFirstDescendant(e => e.ControlType == "ControlType.MenuBar");
            menuBar.Should().NotBeNull("Notepad should have a menu bar");

            var textEditor = rootElement.FindFirstDescendant(e => e.ControlType == "ControlType.Document");
            textEditor.Should().NotBeNull("Notepad should have a text editor");

            // Validate targeting capabilities
            var fileMenu = rootElement.FindFirstDescendant(e => e.Name == "File" && e.ControlType == "ControlType.MenuItem");
            fileMenu.Should().NotBeNull("Should be able to find File menu");

            Console.WriteLine($"✅ Notepad scan completed successfully:");
            Console.WriteLine($"   - Total elements: {rootElement.TotalDescendantCount + 1}");
            Console.WriteLine($"   - Scan duration: {result.Duration.TotalMilliseconds:F2}ms");
            Console.WriteLine($"   - Root element: {rootElement}");
        }
        finally
        {
            await CleanupProcessAsync(notepadProcess);
        }
    }

    [Test]
    [Category("Calculator")]
    public async Task ScanCalculator_ShouldSucceed_AndFindInteractiveElements()
    {
        // Arrange
        var calcProcess = await LaunchCalculatorAsync();
        
        try
        {
            // Act
            var result = await _scanner!.ScanProcessAsync(calcProcess.Id);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue($"Calculator scan should succeed, but got: {result.ErrorMessage}");
            
            var rootElement = result.Value!;
            rootElement.Name.Should().Contain("Calculator");
            
            // Find interactive elements
            var buttons = rootElement.FindDescendants(e => e.ControlType == "ControlType.Button" && e.IsActionable);
            buttons.Should().NotBeEmpty("Calculator should have interactive buttons");
            buttons.Count.Should().BeGreaterThan(10, "Calculator should have multiple buttons");

            // Look for specific calculator buttons
            var numberButtons = buttons.Where(b => IsNumericButton(b.Name)).ToList();
            numberButtons.Should().HaveCountGreaterThan(5, "Should find numeric buttons");

            var equalsButton = buttons.FirstOrDefault(b => b.Name.Contains("=") || b.Name.Contains("Equals"));
            equalsButton.Should().NotBeNull("Should find equals button");

            Console.WriteLine($"✅ Calculator scan completed successfully:");
            Console.WriteLine($"   - Total elements: {rootElement.TotalDescendantCount + 1}");
            Console.WriteLine($"   - Interactive buttons: {buttons.Count}");
            Console.WriteLine($"   - Numeric buttons: {numberButtons.Count}");
        }
        finally
        {
            await CleanupProcessAsync(calcProcess);
        }
    }

    [Test]
    [Category("Performance")]
    public async Task ScanLargeApplication_ShouldCompleteWithinPerformanceLimits()
    {
        // This test validates performance with a more complex application
        var processes = Process.GetProcessesByName("explorer");
        if (!processes.Any())
        {
            Assert.Ignore("Windows Explorer not running");
            return;
        }

        var explorerProcess = processes.First();

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await _scanner!.ScanProcessAsync(explorerProcess.Id);
        stopwatch.Stop();

        // Assert
        result.IsSuccess.Should().BeTrue("Explorer scan should succeed");
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(30), "Large application scan should complete within 30 seconds");
        
        var rootElement = result.Value!;
        rootElement.TotalDescendantCount.Should().BeGreaterThan(50, "Explorer should have many elements");

        // Validate memory usage didn't explode
        var currentMemory = GC.GetTotalMemory(false);
        (currentMemory / 1024 / 1024).Should().BeLessThan(500, "Memory usage should remain reasonable (< 500MB)");

        Console.WriteLine($"✅ Performance test completed:");
        Console.WriteLine($"   - Elements scanned: {rootElement.TotalDescendantCount + 1}");
        Console.WriteLine($"   - Duration: {stopwatch.Elapsed.TotalSeconds:F2}s");
        Console.WriteLine($"   - Memory usage: {currentMemory / 1024 / 1024:F2}MB");
    }

    [Test]
    [Category("ErrorHandling")]
    public async Task ScanNonExistentProcess_ShouldFailGracefully()
    {
        // Act
        var result = await _scanner!.ScanProcessAsync(99999); // Non-existent PID

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse("Scanning non-existent process should fail");
        result.ErrorMessage.Should().NotBeNullOrEmpty("Should provide meaningful error message");
        result.Exception.Should().NotBeNull("Should capture the underlying exception");
        
        Console.WriteLine($"✅ Error handling test passed: {result.ErrorMessage}");
    }

    [Test]
    [Category("Reliability")]
    public async Task ScanMultipleApplications_ShouldMaintainConsistentResults()
    {
        // Test scanning multiple different applications to ensure reliability
        var applications = new[]
        {
            ("notepad", LaunchNotepadAsync),
            ("calc", LaunchCalculatorAsync)
        };

        var results = new List<(string name, ScanResult<UiaElementNode> result, Process process)>();

        try
        {
            // Launch and scan all applications
            foreach (var (name, launcher) in applications)
            {
                var process = await launcher();
                var result = await _scanner!.ScanProcessAsync(process.Id);
                results.Add((name, result, process));
            }

            // Validate all scans succeeded
            foreach (var (name, result, _) in results)
            {
                result.IsSuccess.Should().BeTrue($"{name} scan should succeed");
                result.Value.Should().NotBeNull($"{name} should have element tree");
                result.Value!.TotalDescendantCount.Should().BeGreaterThan(0, $"{name} should have child elements");
            }

            // Validate scanner statistics
            var stats = _scanner!.Statistics;
            stats.SuccessfulScans.Should().BeGreaterOrEqualTo(2, "Should have at least 2 successful scans");
            stats.SuccessRate.Should().BeGreaterThan(50, "Success rate should be reasonable");

            Console.WriteLine($"✅ Reliability test completed:");
            Console.WriteLine($"   - Applications scanned: {results.Count}");
            Console.WriteLine($"   - Scanner statistics: {stats}");
        }
        finally
        {
            // Cleanup all processes
            foreach (var (_, _, process) in results)
            {
                await CleanupProcessAsync(process);
            }
        }
    }

    private static async Task<Process> LaunchNotepadAsync()
    {
        var process = Process.Start("notepad.exe");
        if (process == null)
            throw new InvalidOperationException("Failed to launch Notepad");

        // Wait for the window to be ready
        await WaitForProcessWindowAsync(process, TimeSpan.FromSeconds(5));
        return process;
    }

    private static async Task<Process> LaunchCalculatorAsync()
    {
        var process = Process.Start("calc.exe");
        if (process == null)
            throw new InvalidOperationException("Failed to launch Calculator");

        await WaitForProcessWindowAsync(process, TimeSpan.FromSeconds(5));
        return process;
    }

    private static async Task WaitForProcessWindowAsync(Process process, TimeSpan timeout)
    {
        var deadline = DateTime.UtcNow.Add(timeout);
        
        while (DateTime.UtcNow < deadline)
        {
            process.Refresh();
            if (process.MainWindowHandle != IntPtr.Zero)
                return;
                
            await Task.Delay(100);
        }
        
        throw new TimeoutException($"Process {process.ProcessName} did not create a window within {timeout}");
    }

    private static async Task CleanupProcessAsync(Process process)
    {
        try
        {
            if (!process.HasExited)
            {
                process.CloseMainWindow();
                await Task.Delay(1000); // Give it time to close gracefully
                
                if (!process.HasExited)
                {
                    process.Kill();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Failed to cleanup process {process.ProcessName}: {ex.Message}");
        }
        finally
        {
            process.Dispose();
        }
    }

    private static bool IsNumericButton(string buttonName)
    {
        return !string.IsNullOrEmpty(buttonName) && 
               buttonName.Length == 1 && 
               char.IsDigit(buttonName[0]);
    }
}

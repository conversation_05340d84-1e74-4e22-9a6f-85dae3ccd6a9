using System;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Automation;

namespace CognitiveDrive.CoreUIA
{
    /// <summary>
    /// Core class for scanning UI elements using Windows UI Automation framework
    /// </summary>
    public static class UiaScanner
    {
        /// <summary>
        /// Finds a window by process name and returns its root AutomationElement
        /// </summary>
        /// <param name="processName">Name of the process to find (without .exe extension)</param>
        /// <returns>AutomationElement representing the main window of the process</returns>
        /// <exception cref="ArgumentException">Thrown when no process with the given name is found</exception>
        public static AutomationElement FindWindowByProcessName(string processName)
        {
            // Get all processes with the specified name
            Process[] processes = Process.GetProcessesByName(processName);
            
            if (processes.Length == 0)
            {
                throw new ArgumentException($"No process found with name '{processName}'");
            }

            // Take the first process found
            Process targetProcess = processes[0];
            
            // Get the main window handle
            IntPtr mainWindowHandle = targetProcess.MainWindowHandle;
            
            // If handle is zero, wait briefly and retry once
            if (mainWindowHandle == IntPtr.Zero)
            {
                Task.Delay(1000).Wait(); // Wait 1 second
                targetProcess.Refresh();
                mainWindowHandle = targetProcess.MainWindowHandle;
                
                if (mainWindowHandle == IntPtr.Zero)
                {
                    throw new InvalidOperationException($"Process '{processName}' does not have a main window or window is not ready");
                }
            }

            // Get the AutomationElement from the window handle
            AutomationElement rootElement = AutomationElement.FromHandle(mainWindowHandle);
            
            if (rootElement == null)
            {
                throw new InvalidOperationException($"Could not create AutomationElement from window handle for process '{processName}'");
            }

            return rootElement;
        }

        /// <summary>
        /// Recursively traverses the UI element tree and builds a string representation
        /// </summary>
        /// <param name="rootElement">The root element to start traversal from</param>
        /// <param name="stringBuilder">StringBuilder to append the tree structure to</param>
        /// <param name="depth">Current depth level for indentation</param>
        public static void GetElementTreeRaw(AutomationElement rootElement, StringBuilder stringBuilder, int depth)
        {
            if (rootElement == null)
                return;

            // Create indentation based on depth
            string indent = new string(' ', depth * 2);
            
            try
            {
                // Get element properties
                string name = rootElement.Current.Name ?? "<No Name>";
                string controlType = rootElement.Current.ControlType?.ProgrammaticName ?? "<Unknown>";
                string automationId = rootElement.Current.AutomationId ?? "<No ID>";
                string boundingRect = rootElement.Current.BoundingRectangle.ToString();

                // Append element information to StringBuilder
                stringBuilder.AppendLine($"{indent}Element:");
                stringBuilder.AppendLine($"{indent}  Name: {name}");
                stringBuilder.AppendLine($"{indent}  ControlType: {controlType}");
                stringBuilder.AppendLine($"{indent}  AutomationId: {automationId}");
                stringBuilder.AppendLine($"{indent}  BoundingRectangle: {boundingRect}");
            }
            catch (ElementNotAvailableException)
            {
                stringBuilder.AppendLine($"{indent}Element: <Not Available>");
                return;
            }
            catch (Exception ex)
            {
                stringBuilder.AppendLine($"{indent}Element: <Error reading properties: {ex.Message}>");
                return;
            }

            // Use RawViewWalker to traverse all elements
            TreeWalker walker = TreeWalker.RawViewWalker;
            
            try
            {
                // Get the first child
                AutomationElement child = walker.GetFirstChild(rootElement);
                
                // Loop through all children
                while (child != null)
                {
                    // Recursively process each child
                    GetElementTreeRaw(child, stringBuilder, depth + 1);
                    
                    // Get the next sibling
                    child = walker.GetNextSibling(child);
                }
            }
            catch (ElementNotAvailableException)
            {
                stringBuilder.AppendLine($"{indent}  <Children not available>");
            }
            catch (Exception ex)
            {
                stringBuilder.AppendLine($"{indent}  <Error traversing children: {ex.Message}>");
            }
        }
    }
}

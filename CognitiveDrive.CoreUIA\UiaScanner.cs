using System;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Automation;

namespace CognitiveDrive.CoreUIA
{
    /// <summary>
    /// Core class for scanning UI elements using Windows UI Automation framework
    /// </summary>
    public static class UiaScanner
    {
        /// <summary>
        /// Finds a window by process name and returns its root AutomationElement
        /// </summary>
        /// <param name="processName">Name of the process to find (without .exe extension)</param>
        /// <returns>AutomationElement representing the main window of the process</returns>
        /// <exception cref="ArgumentException">Thrown when no process with the given name is found</exception>
        public static AutomationElement FindWindowByProcessName(string processName)
        {
            // Get all processes with the specified name
            Process[] processes = Process.GetProcessesByName(processName);
            
            if (processes.Length == 0)
            {
                throw new ArgumentException($"No process found with name '{processName}'");
            }

            // Take the first process found
            Process targetProcess = processes[0];
            
            // Get the main window handle
            IntPtr mainWindowHandle = targetProcess.MainWindowHandle;
            
            // If handle is zero, wait briefly and retry once
            if (mainWindowHandle == IntPtr.Zero)
            {
                Task.Delay(1000).Wait(); // Wait 1 second
                targetProcess.Refresh();
                mainWindowHandle = targetProcess.MainWindowHandle;
                
                if (mainWindowHandle == IntPtr.Zero)
                {
                    throw new InvalidOperationException($"Process '{processName}' does not have a main window or window is not ready");
                }
            }

            // Get the AutomationElement from the window handle
            AutomationElement rootElement = AutomationElement.FromHandle(mainWindowHandle);
            
            if (rootElement == null)
            {
                throw new InvalidOperationException($"Could not create AutomationElement from window handle for process '{processName}'");
            }

            return rootElement;
        }

        /// <summary>
        /// Recursively traverses the UI element tree and builds a string representation
        /// </summary>
        /// <param name="rootElement">The root element to start traversal from</param>
        /// <param name="stringBuilder">StringBuilder to append the tree structure to</param>
        /// <param name="depth">Current depth level for indentation</param>
        public static void GetElementTreeRaw(AutomationElement rootElement, StringBuilder stringBuilder, int depth)
        {
            if (rootElement == null)
                return;

            // Create indentation based on depth
            string indent = new string(' ', depth * 2);
            
            try
            {
                // Get element properties
                string name = rootElement.Current.Name ?? "<No Name>";
                string controlType = rootElement.Current.ControlType?.ProgrammaticName ?? "<Unknown>";
                string automationId = rootElement.Current.AutomationId ?? "<No ID>";
                string boundingRect = rootElement.Current.BoundingRectangle.ToString();

                // Append element information to StringBuilder
                stringBuilder.AppendLine($"{indent}Element:");
                stringBuilder.AppendLine($"{indent}  Name: {name}");
                stringBuilder.AppendLine($"{indent}  ControlType: {controlType}");
                stringBuilder.AppendLine($"{indent}  AutomationId: {automationId}");
                stringBuilder.AppendLine($"{indent}  BoundingRectangle: {boundingRect}");
            }
            catch (ElementNotAvailableException)
            {
                stringBuilder.AppendLine($"{indent}Element: <Not Available>");
                return;
            }
            catch (Exception ex)
            {
                stringBuilder.AppendLine($"{indent}Element: <Error reading properties: {ex.Message}>");
                return;
            }

            // Use RawViewWalker to traverse all elements
            TreeWalker walker = TreeWalker.RawViewWalker;
            
            try
            {
                // Get the first child
                AutomationElement child = walker.GetFirstChild(rootElement);
                
                // Loop through all children
                while (child != null)
                {
                    // Recursively process each child
                    GetElementTreeRaw(child, stringBuilder, depth + 1);
                    
                    // Get the next sibling
                    child = walker.GetNextSibling(child);
                }
            }
            catch (ElementNotAvailableException)
            {
                stringBuilder.AppendLine($"{indent}  <Children not available>");
            }
            catch (Exception ex)
            {
                stringBuilder.AppendLine($"{indent}  <Error traversing children: {ex.Message}>");
            }
        }

        /// <summary>
        /// Builds a Hierarchical Element Tree (HET) from a UI Automation element.
        /// This method recursively transforms raw UI data into a structured, serializable object model.
        /// </summary>
        /// <param name="rootElement">The root AutomationElement to start building the tree from</param>
        /// <returns>A UiaElementNode representing the structured hierarchy of UI elements</returns>
        /// <exception cref="ArgumentNullException">Thrown when rootElement is null</exception>
        public static UiaElementNode BuildHET(AutomationElement rootElement)
        {
            if (rootElement == null)
                throw new ArgumentNullException(nameof(rootElement));

            var node = new UiaElementNode();

            try
            {
                // Safely extract element properties with error handling
                node.Name = GetSafeProperty(rootElement, AutomationElement.NameProperty, string.Empty);
                node.ControlType = GetSafeProperty(rootElement, AutomationElement.ControlTypeProperty, ControlType.Custom)?.ProgrammaticName ?? "ControlType.Custom";
                node.AutomationId = GetSafeProperty(rootElement, AutomationElement.AutomationIdProperty, string.Empty);
                node.IsEnabled = GetSafeProperty(rootElement, AutomationElement.IsEnabledProperty, true);
                node.IsOffscreen = GetSafeProperty(rootElement, AutomationElement.IsOffscreenProperty, false);

                // Handle BoundingRectangle with special logic for virtualized elements
                var boundingRect = GetSafeProperty(rootElement, AutomationElement.BoundingRectangleProperty, System.Windows.Rect.Empty);

                // Set BoundingRectangle to null if element is offscreen or has no physical location
                if (node.IsOffscreen || boundingRect == System.Windows.Rect.Empty ||
                    (boundingRect.Width == 0 && boundingRect.Height == 0))
                {
                    node.BoundingRectangle = null;
                }
                else
                {
                    node.BoundingRectangle = boundingRect;
                }
            }
            catch (Exception ex)
            {
                // If we can't read basic properties, create a minimal node
                node.Name = "<Error reading properties>";
                node.ControlType = "ControlType.Custom";
                node.AutomationId = $"<Error: {ex.Message}>";
                node.IsEnabled = false;
                node.IsOffscreen = true;
                node.BoundingRectangle = null;
            }

            // Recursively build child nodes
            try
            {
                TreeWalker walker = TreeWalker.RawViewWalker;
                AutomationElement child = walker.GetFirstChild(rootElement);

                while (child != null)
                {
                    try
                    {
                        var childNode = BuildHET(child);
                        node.Children.Add(childNode);
                    }
                    catch (ElementNotAvailableException)
                    {
                        // Child element is no longer available, skip it
                    }
                    catch (Exception ex)
                    {
                        // Create an error node for problematic children
                        var errorNode = new UiaElementNode
                        {
                            Name = "<Error processing child>",
                            ControlType = "ControlType.Custom",
                            AutomationId = $"<Error: {ex.Message}>",
                            IsEnabled = false,
                            IsOffscreen = true,
                            BoundingRectangle = null
                        };
                        node.Children.Add(errorNode);
                    }

                    try
                    {
                        child = walker.GetNextSibling(child);
                    }
                    catch (ElementNotAvailableException)
                    {
                        // No more siblings available
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // If we can't traverse children, add an error indicator
                var errorNode = new UiaElementNode
                {
                    Name = "<Error traversing children>",
                    ControlType = "ControlType.Custom",
                    AutomationId = $"<Error: {ex.Message}>",
                    IsEnabled = false,
                    IsOffscreen = true,
                    BoundingRectangle = null
                };
                node.Children.Add(errorNode);
            }

            return node;
        }

        /// <summary>
        /// Safely retrieves a property value from an AutomationElement with error handling.
        /// </summary>
        /// <typeparam name="T">The type of the property value</typeparam>
        /// <param name="element">The AutomationElement to read from</param>
        /// <param name="property">The AutomationProperty to retrieve</param>
        /// <param name="defaultValue">The default value to return if the property cannot be read</param>
        /// <returns>The property value or the default value if an error occurs</returns>
        private static T GetSafeProperty<T>(AutomationElement element, AutomationProperty property, T defaultValue)
        {
            try
            {
                var value = element.GetCurrentPropertyValue(property);
                if (value == AutomationElement.NotSupported)
                    return defaultValue;

                return (T)value;
            }
            catch (Exception)
            {
                return defaultValue;
            }
        }
    }
}

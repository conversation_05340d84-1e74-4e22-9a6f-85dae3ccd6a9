﻿using System;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using CognitiveDrive.CoreUIA;

/// <summary>
/// Console test application that simulates Chariot's Named Pipe client functionality.
/// Tests the same connection logic that Chariot uses.
/// </summary>
class TestChariotConnection
{
    private static CancellationTokenSource? _cancellationTokenSource;
    private static bool _isConnected = false;
    private static readonly string _pipeName = "CognitiveDrivePipe";

    static async Task Main(string[] args)
    {
        Console.WriteLine("Testing Chariot-style connection to CognitiveDrive Daemon...");
        Console.WriteLine("This simulates the exact Named Pipe client logic used in Chariot.");
        Console.WriteLine();

        try
        {
            Console.WriteLine("Starting connection to Live Agent...");
            _cancellationTokenSource = new CancellationTokenSource();

            // Run the same Named Pipe client logic as <PERSON><PERSON>
            await RunNamedPipeClient(_cancellationTokenSource.Token);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Connection Error: {ex.Message}");
        }

        Console.WriteLine("Test completed. Press any key to exit...");
        Console.ReadKey();
    }

    /// <summary>
    /// Runs the Named Pipe client to receive live HET updates from the daemon.
    /// This is the same logic used in Chariot's MainWindow.xaml.cs
    /// </summary>
    private static async Task RunNamedPipeClient(CancellationToken cancellationToken)
    {
        try
        {
            using (var pipeClient = new NamedPipeClientStream(".", _pipeName, PipeDirection.In))
            {
                // Connect to the daemon's Named Pipe server
                Console.WriteLine("Connecting to daemon...");
                await pipeClient.ConnectAsync(5000, cancellationToken); // 5 second timeout

                // Update connection status
                _isConnected = true;
                Console.WriteLine("✅ Connected to Live Agent successfully!");
                Console.WriteLine();

                // Read HET updates from the pipe
                var buffer = new byte[1024 * 1024]; // 1MB buffer for large HET data
                int messageCount = 0;

                while (pipeClient.IsConnected && !cancellationToken.IsCancellationRequested && messageCount < 5)
                {
                    try
                    {
                        Console.WriteLine($"Reading HET update {messageCount + 1}...");
                        int bytesRead = await pipeClient.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                        if (bytesRead > 0)
                        {
                            string jsonData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                            await ProcessLiveHETUpdate(jsonData, messageCount + 1);
                            messageCount++;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ Data receive error: {ex.Message}");
                        break;
                    }
                }

                Console.WriteLine($"✅ Successfully received {messageCount} HET updates");
            }
        }
        catch (TimeoutException)
        {
            Console.WriteLine("❌ Connection timeout - Is the daemon service running?");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Connection failed: {ex.Message}");
        }
        finally
        {
            await DisconnectFromAgent();
        }
    }

    /// <summary>
    /// Processes a live HET update received from the daemon.
    /// This simulates the same logic used in Chariot's ProcessLiveHETUpdate method.
    /// </summary>
    private static async Task ProcessLiveHETUpdate(string jsonData, int messageNumber)
    {
        try
        {
            // Deserialize the HET data (same as Chariot)
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };

            var hetRoot = JsonSerializer.Deserialize<UiaElementNode>(jsonData, jsonOptions);

            if (hetRoot != null)
            {
                Console.WriteLine($"✅ HET Update {messageNumber}:");
                Console.WriteLine($"   Window: {hetRoot.Name}");
                Console.WriteLine($"   Control Type: {hetRoot.ControlType}");
                Console.WriteLine($"   Total Elements: {hetRoot.TotalDescendantCount + 1}");
                Console.WriteLine($"   Has Physical Location: {hetRoot.HasPhysicalLocation}");
                Console.WriteLine($"   JSON Size: {jsonData.Length:N0} bytes");

                if (hetRoot.BoundingRectangle.HasValue)
                {
                    var rect = hetRoot.BoundingRectangle.Value;
                    Console.WriteLine($"   Bounds: ({rect.Location.X}, {rect.Location.Y}) {rect.Size.Width}x{rect.Size.Height}");
                }

                Console.WriteLine($"   Children: {hetRoot.Children?.Count ?? 0}");
                Console.WriteLine();

                // Simulate what Chariot would do: call RenderNode method
                Console.WriteLine($"   🎨 Would render {CountRenderableElements(hetRoot)} visual overlay elements");
                Console.WriteLine();
            }
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"❌ JSON parsing error: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ HET update error: {ex.Message}");
        }
    }

    /// <summary>
    /// Counts elements that would be rendered in the visual overlay.
    /// </summary>
    private static int CountRenderableElements(UiaElementNode node)
    {
        int count = node.HasPhysicalLocation ? 1 : 0;

        if (node.Children != null)
        {
            foreach (var child in node.Children)
            {
                count += CountRenderableElements(child);
            }
        }

        return count;
    }

    /// <summary>
    /// Disconnects from the live agent and cleans up resources.
    /// This simulates the same logic used in Chariot's DisconnectFromAgent method.
    /// </summary>
    private static async Task DisconnectFromAgent()
    {
        try
        {
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
            _isConnected = false;

            Console.WriteLine("🔌 Disconnected from Live Agent");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Disconnect error: {ex.Message}");
        }
    }
}

<Window x:Class="CognitiveDrive.Chariot.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="using:CognitiveDrive.Chariot"
        Title="CognitiveDrive Chariot - HET Visualizer"
        Width="1200" Height="800">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Top toolbar -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Padding="10" Background="LightGray">
            <Button x:Name="LoadButton" 
                    Content="Load HET File" 
                    Click="LoadButton_Click"
                    Padding="15,8"
                    Margin="0,0,10,0"/>
            
            <TextBlock x:Name="StatusText" 
                       Text="No HET file loaded" 
                       VerticalAlignment="Center"
                       Margin="10,0"/>
            
            <Button x:Name="ClearButton" 
                    Content="Clear Overlay" 
                    Click="ClearButton_Click"
                    Padding="15,8"
                    Margin="10,0,0,0"
                    IsEnabled="False"/>
        </StackPanel>
        
        <!-- Main canvas for overlay rendering -->
        <ScrollViewer Grid.Row="1" 
                      ZoomMode="Enabled" 
                      HorizontalScrollMode="Enabled" 
                      VerticalScrollMode="Enabled"
                      HorizontalScrollBarVisibility="Auto"
                      VerticalScrollBarVisibility="Auto">
            
            <Canvas x:Name="OverlayCanvas" 
                    Background="Transparent"
                    Width="1920" 
                    Height="1080">
                
                <!-- Instructions text when no HET is loaded -->
                <TextBlock x:Name="InstructionsText"
                           Canvas.Left="50"
                           Canvas.Top="50"
                           Text="Click 'Load HET File' to visualize a UI element hierarchy"
                           FontSize="16"
                           Foreground="Gray"/>
                
            </Canvas>
        </ScrollViewer>
    </Grid>
</Window>

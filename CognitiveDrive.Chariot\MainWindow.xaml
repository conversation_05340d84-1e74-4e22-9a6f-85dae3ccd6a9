<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="CognitiveDrive.Chariot.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="using:CognitiveDrive.Chariot">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Top toolbar -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Padding="10" Background="LightGray">
            <Button x:Name="ConnectButton"
                    Content="Connect to Live Agent"
                    Click="ConnectButton_Click"
                    Padding="15,8"
                    Margin="0,0,10,0"/>

            <TextBlock x:Name="StatusText"
                       Text="Disconnected from Live Agent"
                       VerticalAlignment="Center"
                       Margin="10,0"/>

            <Button x:Name="ClearButton"
                    Content="Clear Overlay"
                    Click="ClearButton_Click"
                    Padding="15,8"
                    Margin="10,0,0,0"
                    IsEnabled="False"/>
        </StackPanel>

        <!-- Main canvas for overlay rendering -->
        <ScrollViewer Grid.Row="1"
                      ZoomMode="Enabled"
                      HorizontalScrollMode="Enabled"
                      VerticalScrollMode="Enabled"
                      HorizontalScrollBarVisibility="Auto"
                      VerticalScrollBarVisibility="Auto">

            <Canvas x:Name="OverlayCanvas"
                    Background="Transparent"
                    Width="1920"
                    Height="1080">

                <!-- Instructions text when no HET is loaded -->
                <TextBlock x:Name="InstructionsText"
                           Canvas.Left="50"
                           Canvas.Top="50"
                           Text="Click 'Connect to Live Agent' to visualize real-time UI hierarchy"
                           FontSize="16"
                           Foreground="Gray"/>

            </Canvas>
        </ScrollViewer>
    </Grid>
</Window>

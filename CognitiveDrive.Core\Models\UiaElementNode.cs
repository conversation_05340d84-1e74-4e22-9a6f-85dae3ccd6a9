using System.Text.Json.Serialization;

namespace CognitiveDrive.Core.Models;

/// <summary>
/// Represents a UI element node in the hierarchical element tree (HET).
/// This is the core data model for representing UI elements in a structured, serializable format.
/// Enhanced for production use with validation, performance optimizations, and comprehensive metadata.
/// </summary>
public sealed class UiaElementNode
{
    /// <summary>
    /// Gets or sets the name of the UI element.
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the control type of the UI element.
    /// </summary>
    [JsonPropertyName("controlType")]
    public string ControlType { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the automation ID of the UI element.
    /// </summary>
    [JsonPropertyName("automationId")]
    public string AutomationId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets a value indicating whether the element is enabled.
    /// </summary>
    [JsonPropertyName("isEnabled")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Gets or sets a value indicating whether the element is offscreen.
    /// </summary>
    [JsonPropertyName("isOffscreen")]
    public bool IsOffscreen { get; set; } = false;

    /// <summary>
    /// Gets or sets the bounding rectangle of the element.
    /// Null indicates the element has no physical location (e.g., virtualized elements).
    /// </summary>
    [JsonPropertyName("boundingRectangle")]
    public ElementBounds? BoundingRectangle { get; set; }

    /// <summary>
    /// Gets or sets the child elements.
    /// </summary>
    [JsonPropertyName("children")]
    public List<UiaElementNode> Children { get; set; } = new();

    /// <summary>
    /// Gets or sets additional properties that may be specific to certain control types.
    /// </summary>
    [JsonPropertyName("properties")]
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// Gets or sets the timestamp when this element was captured.
    /// </summary>
    [JsonPropertyName("capturedAt")]
    public DateTimeOffset CapturedAt { get; set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Gets or sets the process ID that owns this element.
    /// </summary>
    [JsonPropertyName("processId")]
    public int ProcessId { get; set; }

    /// <summary>
    /// Gets the total number of descendant nodes.
    /// </summary>
    [JsonIgnore]
    public int TotalDescendantCount
    {
        get
        {
            var count = Children.Count;
            foreach (var child in Children)
            {
                count += child.TotalDescendantCount;
            }
            return count;
        }
    }

    /// <summary>
    /// Gets a value indicating whether this element has a physical location on screen.
    /// </summary>
    [JsonIgnore]
    public bool HasPhysicalLocation => BoundingRectangle?.IsEmpty == false;

    /// <summary>
    /// Gets a value indicating whether this element is actionable (can be interacted with).
    /// </summary>
    [JsonIgnore]
    public bool IsActionable => IsEnabled && !IsOffscreen && IsInteractiveControlType();

    /// <summary>
    /// Gets a unique identifier for this element based on its properties.
    /// </summary>
    [JsonIgnore]
    public string ElementSignature => GenerateElementSignature();

    /// <summary>
    /// Validates the element node for consistency and completeness.
    /// </summary>
    /// <returns>A list of validation errors, empty if valid</returns>
    public List<string> Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(ControlType))
        {
            errors.Add("ControlType cannot be null or empty");
        }

        if (ProcessId <= 0)
        {
            errors.Add("ProcessId must be greater than zero");
        }

        if (CapturedAt == default)
        {
            errors.Add("CapturedAt must be set");
        }

        // Validate children recursively
        for (int i = 0; i < Children.Count; i++)
        {
            var childErrors = Children[i].Validate();
            foreach (var error in childErrors)
            {
                errors.Add($"Child[{i}]: {error}");
            }
        }

        return errors;
    }

    /// <summary>
    /// Finds all descendant elements that match the specified predicate.
    /// </summary>
    /// <param name="predicate">The predicate to match elements</param>
    /// <returns>A list of matching elements</returns>
    public List<UiaElementNode> FindDescendants(Func<UiaElementNode, bool> predicate)
    {
        var results = new List<UiaElementNode>();
        FindDescendantsRecursive(this, predicate, results);
        return results;
    }

    /// <summary>
    /// Finds the first descendant element that matches the specified predicate.
    /// </summary>
    /// <param name="predicate">The predicate to match elements</param>
    /// <returns>The first matching element, or null if none found</returns>
    public UiaElementNode? FindFirstDescendant(Func<UiaElementNode, bool> predicate)
    {
        if (predicate(this))
        {
            return this;
        }

        foreach (var child in Children)
        {
            var result = child.FindFirstDescendant(predicate);
            if (result != null)
            {
                return result;
            }
        }

        return null;
    }

    /// <summary>
    /// Creates a shallow copy of this element without children.
    /// </summary>
    /// <returns>A shallow copy of this element</returns>
    public UiaElementNode CreateShallowCopy()
    {
        return new UiaElementNode
        {
            Name = Name,
            ControlType = ControlType,
            AutomationId = AutomationId,
            IsEnabled = IsEnabled,
            IsOffscreen = IsOffscreen,
            BoundingRectangle = BoundingRectangle,
            Properties = new Dictionary<string, object>(Properties),
            CapturedAt = CapturedAt,
            ProcessId = ProcessId
        };
    }

    private bool IsInteractiveControlType()
    {
        var interactiveTypes = new[]
        {
            "ControlType.Button", "ControlType.Edit", "ControlType.ComboBox",
            "ControlType.ListItem", "ControlType.MenuItem", "ControlType.TabItem",
            "ControlType.CheckBox", "ControlType.RadioButton", "ControlType.Hyperlink",
            "ControlType.TreeItem", "ControlType.DataItem", "ControlType.Slider"
        };

        return interactiveTypes.Contains(ControlType);
    }

    private string GenerateElementSignature()
    {
        var parts = new[]
        {
            $"Type:{ControlType}",
            $"Name:{Name}",
            $"Id:{AutomationId}",
            $"Enabled:{IsEnabled}",
            $"Bounds:{BoundingRectangle?.ToString() ?? "None"}"
        };

        return string.Join("|", parts);
    }

    private static void FindDescendantsRecursive(UiaElementNode node, Func<UiaElementNode, bool> predicate, List<UiaElementNode> results)
    {
        if (predicate(node))
        {
            results.Add(node);
        }

        foreach (var child in node.Children)
        {
            FindDescendantsRecursive(child, predicate, results);
        }
    }

    public override string ToString()
    {
        var location = HasPhysicalLocation ? $"at {BoundingRectangle}" : "no location";
        return $"{ControlType} '{Name}' (ID: {AutomationId}) - {location} - {Children.Count} children";
    }
}

/// <summary>
/// Represents the bounding rectangle of a UI element.
/// </summary>
public sealed class ElementBounds
{
    [JsonPropertyName("x")]
    public double X { get; set; }

    [JsonPropertyName("y")]
    public double Y { get; set; }

    [JsonPropertyName("width")]
    public double Width { get; set; }

    [JsonPropertyName("height")]
    public double Height { get; set; }

    [JsonIgnore]
    public bool IsEmpty => Width <= 0 || Height <= 0;

    [JsonIgnore]
    public double Area => Width * Height;

    public override string ToString()
    {
        return $"({X}, {Y}, {Width}x{Height})";
    }
}

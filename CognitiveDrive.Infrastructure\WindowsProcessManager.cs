using System.Diagnostics;
using System.Runtime.InteropServices;
using CognitiveDrive.Core.Interfaces;
using CognitiveDrive.Infrastructure.Interfaces;

namespace CognitiveDrive.Infrastructure;

/// <summary>
/// Production-grade Windows process manager for UI automation.
/// Provides robust process discovery and management with comprehensive error handling.
/// </summary>
public sealed class WindowsProcessManager : IProcessManager
{
    [DllImport("user32.dll")]
    private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);

    [DllImport("user32.dll")]
    private static extern bool IsWindowVisible(IntPtr hWnd);

    [DllImport("user32.dll")]
    private static extern bool IsWindow(IntPtr hWnd);

    [DllImport("user32.dll")]
    private static extern int GetWindowTextLength(IntPtr hWnd);

    [DllImport("user32.dll")]
    private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

    [DllImport("user32.dll")]
    private static extern bool IsWindowEnabled(IntPtr hWnd);

    private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

    public async Task<ProcessUiInfo?> GetProcessInfoAsync(int processId, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                var process = Process.GetProcessById(processId);
                return CreateProcessUiInfo(process);
            }
            catch (ArgumentException)
            {
                // Process not found
                return null;
            }
            catch (Exception)
            {
                // Other errors
                return null;
            }
        }, cancellationToken);
    }

    public async Task<IReadOnlyList<ProcessUiInfo>> FindProcessesByNameAsync(string processName, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                var processes = Process.GetProcessesByName(processName);
                var results = new List<ProcessUiInfo>();

                foreach (var process in processes)
                {
                    try
                    {
                        var info = CreateProcessUiInfo(process);
                        if (info != null)
                        {
                            results.Add(info);
                        }
                    }
                    catch (Exception)
                    {
                        // Skip processes we can't access
                        continue;
                    }
                }

                return (IReadOnlyList<ProcessUiInfo>)results;
            }
            catch (Exception)
            {
                return new List<ProcessUiInfo>();
            }
        }, cancellationToken);
    }

    public async Task<IReadOnlyList<ProcessUiInfo>> GetAllProcessesWithUiAsync(CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                var allProcesses = Process.GetProcesses();
                var results = new List<ProcessUiInfo>();

                foreach (var process in allProcesses)
                {
                    try
                    {
                        // PRODUCTION-GRADE: Filter out system processes to reduce noise
                        if (IsSystemProcess(process))
                        {
                            continue;
                        }

                        var info = CreateProcessUiInfo(process);
                        if (info != null && info.HasVisibleUi)
                        {
                            results.Add(info);
                        }
                    }
                    catch (Exception)
                    {
                        // Skip processes we can't access (no logging to reduce noise)
                        continue;
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                return (IReadOnlyList<ProcessUiInfo>)results;
            }
            catch (Exception)
            {
                return new List<ProcessUiInfo>();
            }
        }, cancellationToken);
    }

    public async Task<int> GetProcessIdFromWindowAsync(IntPtr windowHandle, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            GetWindowThreadProcessId(windowHandle, out uint processId);
            return (int)processId;
        }, cancellationToken);
    }

    private ProcessUiInfo? CreateProcessUiInfo(Process process)
    {
        try
        {
            // PRODUCTION-GRADE PROCESS DETECTION WITH MULTIPLE STRATEGIES

            // Strategy 1: Traditional main window detection
            var hasMainWindow = process.MainWindowHandle != IntPtr.Zero;
            var mainWindowVisible = hasMainWindow && IsWindowVisible(process.MainWindowHandle);

            // Strategy 2: Enhanced visibility detection for modern apps
            var hasVisibleUi = mainWindowVisible || HasVisibleWindowsAdvanced(process);

            // Strategy 3: Special handling for known application types
            var isKnownUiApp = IsKnownUiApplication(process.ProcessName);

            // Strategy 4: Special handling for WebView2 processes (often have no main window)
            var isWebView2Process = process.ProcessName.ToLowerInvariant().Contains("msedgewebview2");

            // Final visibility determination - exclude WebView2 processes without main windows
            var finalVisibility = (hasVisibleUi || isKnownUiApp) && !(isWebView2Process && !hasMainWindow);

            var info = new ProcessUiInfo
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                MainWindowTitle = hasMainWindow ? process.MainWindowTitle : GetProcessWindowTitle(process),
                MainWindowHandle = process.MainWindowHandle,
                HasVisibleUi = finalVisibility,
                EstimatedElementCount = EstimateElementCount(process),
                UiFramework = DetectUiFramework(process),
                Metadata = new Dictionary<string, object>
                {
                    ["StartTime"] = process.StartTime,
                    ["WorkingSet"] = process.WorkingSet64,
                    ["HasMainWindow"] = hasMainWindow,
                    ["MainWindowVisible"] = mainWindowVisible,
                    ["HasVisibleWindowsAdvanced"] = HasVisibleWindowsAdvanced(process),
                    ["IsKnownUiApp"] = isKnownUiApp,
                    ["DetectionStrategy"] = GetDetectionStrategy(hasMainWindow, mainWindowVisible, isKnownUiApp)
                }
            };

            return info;
        }
        catch (Exception ex)
        {
            // Only log errors for non-system processes to reduce noise
            if (!IsSystemProcess(process))
            {
                Console.WriteLine($"⚠️ Failed to create ProcessUiInfo for {process.ProcessName} (PID: {process.Id}): {ex.Message}");
            }
            return null;
        }
    }

    private static int EstimateElementCount(Process process)
    {
        // Simple heuristic based on process characteristics
        try
        {
            var workingSet = process.WorkingSet64;
            var hasMainWindow = process.MainWindowHandle != IntPtr.Zero;

            if (!hasMainWindow)
                return 0;

            // Rough estimation based on memory usage
            if (workingSet > 500_000_000) // > 500MB
                return 1000;
            else if (workingSet > 100_000_000) // > 100MB
                return 500;
            else if (workingSet > 50_000_000) // > 50MB
                return 200;
            else
                return 50;
        }
        catch (Exception)
        {
            return 50; // Default estimate
        }
    }

    /// <summary>
    /// PRODUCTION-GRADE: Advanced window detection for modern applications.
    /// Handles UWP apps, PWAs, and other non-traditional window types.
    /// </summary>
    private bool HasVisibleWindowsAdvanced(Process process)
    {
        try
        {
            var processId = process.Id;
            var hasVisibleWindow = false;

            // Enumerate all windows to find ones belonging to this process
            EnumWindows((hWnd, lParam) =>
            {
                try
                {
                    GetWindowThreadProcessId(hWnd, out uint windowProcessId);

                    if (windowProcessId == processId)
                    {
                        // Check if this window is visible and has content
                        if (IsWindow(hWnd) && IsWindowVisible(hWnd))
                        {
                            // Additional checks for meaningful windows
                            var textLength = GetWindowTextLength(hWnd);
                            if (textLength > 0 || IsWindowEnabled(hWnd))
                            {
                                hasVisibleWindow = true;
                                return false; // Stop enumeration
                            }
                        }
                    }
                }
                catch
                {
                    // Continue enumeration even if individual window check fails
                }

                return true; // Continue enumeration
            }, IntPtr.Zero);

            return hasVisibleWindow;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// PRODUCTION-GRADE: Identifies known UI applications that should always be considered.
    /// </summary>
    private bool IsKnownUiApplication(string processName)
    {
        var knownUiApps = new[]
        {
            // Browsers
            "chrome", "firefox", "edge", "brave", "opera", "safari",
            // Microsoft Apps
            "notepad", "calc", "calculator", "mspaint", "winword", "excel", "powerpnt",
            // Development Tools
            "devenv", "code", "notepad++", "sublime_text",
            // Media
            "vlc", "wmplayer", "spotify", "itunes",
            // Communication
            "discord", "slack", "teams", "zoom", "skype",
            // System
            "explorer", "taskmgr", "regedit", "cmd", "powershell"
        };

        var lowerProcessName = processName.ToLowerInvariant();
        return knownUiApps.Any(app => lowerProcessName.Contains(app));
    }

    /// <summary>
    /// Gets window title for processes that might not have a main window handle.
    /// </summary>
    private string GetProcessWindowTitle(Process process)
    {
        try
        {
            // Try to get title from main window first
            if (!string.IsNullOrEmpty(process.MainWindowTitle))
                return process.MainWindowTitle;

            // For processes without main window, use process name as fallback
            return $"{process.ProcessName} Application";
        }
        catch (Exception)
        {
            return process.ProcessName;
        }
    }

    /// <summary>
    /// Determines which detection strategy was used for debugging purposes.
    /// </summary>
    private string GetDetectionStrategy(bool hasMainWindow, bool mainWindowVisible, bool isKnownUiApp)
    {
        if (mainWindowVisible)
            return "MainWindow";
        else if (isKnownUiApp)
            return "KnownApp";
        else if (hasMainWindow)
            return "AdvancedDetection";
        else
            return "None";
    }

    /// <summary>
    /// PRODUCTION-GRADE: Identifies system processes that should be filtered out.
    /// Reduces noise and improves performance by skipping processes we can't access.
    /// </summary>
    private bool IsSystemProcess(Process process)
    {
        try
        {
            var processName = process.ProcessName.ToLowerInvariant();

            // System processes that typically don't have UI or can't be accessed
            var systemProcesses = new[]
            {
                "idle", "system", "secure system", "registry", "smss", "csrss",
                "wininit", "winlogon", "services", "lsaiso", "lsass", "svchost",
                "fontdrvhost", "wudfhost", "dwm", "memory compression", "ngciso",
                "searchindexer", "wmiprvse", "unsecapp", "dashost", "conhost",
                "securityhealthservice", "locator", "wmiapsrv", "searchprotocolhost",
                "searchfilterhost", "aggregatorhost", "approvisioningplugin"
            };

            // Check if this is a known system process
            if (systemProcesses.Contains(processName))
                return true;

            // Check for service host processes
            if (processName.Contains("svchost") || processName.Contains("service"))
                return true;

            // Check for crash handlers and background services
            if (processName.Contains("crashpad") || processName.Contains("crashhandler"))
                return true;

            // Check for very low PID (system processes)
            if (process.Id <= 10)
                return true;

            return false;
        }
        catch (Exception)
        {
            // If we can't determine, assume it's a system process to be safe
            return true;
        }
    }

    private static string DetectUiFramework(Process process)
    {
        try
        {
            var processName = process.ProcessName.ToLowerInvariant();

            // Enhanced framework detection
            if (processName.Contains("wpf") || processName.Contains("xaml"))
                return "WPF";
            else if (processName.Contains("winui") || processName.Contains("uwp") || processName.Contains("calculator"))
                return "WinUI/UWP";
            else if (processName.Contains("electron") || processName.Contains("chrome") || processName.Contains("brave"))
                return "Electron/Chromium";
            else if (processName.Contains("java") || processName.Contains("javaw"))
                return "Java/Swing";
            else if (processName.Contains("notepad") || processName.Contains("calc"))
                return "Win32";
            else if (processName.Contains("firefox"))
                return "Gecko";
            else if (processName.Contains("edge"))
                return "EdgeHTML/Chromium";
            else
                return "Unknown";
        }
        catch (Exception)
        {
            return "Unknown";
        }
    }
}

using System.Diagnostics;
using System.Runtime.InteropServices;
using CognitiveDrive.Core.Interfaces;
using CognitiveDrive.Infrastructure.Interfaces;

namespace CognitiveDrive.Infrastructure;

/// <summary>
/// Production-grade Windows process manager for UI automation.
/// Provides robust process discovery and management with comprehensive error handling.
/// </summary>
public sealed class WindowsProcessManager : IProcessManager
{
    [DllImport("user32.dll")]
    private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);

    [DllImport("user32.dll")]
    private static extern bool IsWindowVisible(IntPtr hWnd);

    public async Task<ProcessUiInfo?> GetProcessInfoAsync(int processId, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                var process = Process.GetProcessById(processId);
                return CreateProcessUiInfo(process);
            }
            catch (ArgumentException)
            {
                // Process not found
                return null;
            }
            catch (Exception)
            {
                // Other errors
                return null;
            }
        }, cancellationToken);
    }

    public async Task<IReadOnlyList<ProcessUiInfo>> FindProcessesByNameAsync(string processName, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                var processes = Process.GetProcessesByName(processName);
                var results = new List<ProcessUiInfo>();

                foreach (var process in processes)
                {
                    try
                    {
                        var info = CreateProcessUiInfo(process);
                        if (info != null)
                        {
                            results.Add(info);
                        }
                    }
                    catch (Exception)
                    {
                        // Skip processes we can't access
                        continue;
                    }
                }

                return (IReadOnlyList<ProcessUiInfo>)results;
            }
            catch (Exception)
            {
                return new List<ProcessUiInfo>();
            }
        }, cancellationToken);
    }

    public async Task<IReadOnlyList<ProcessUiInfo>> GetAllProcessesWithUiAsync(CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                var allProcesses = Process.GetProcesses();
                var results = new List<ProcessUiInfo>();

                foreach (var process in allProcesses)
                {
                    try
                    {
                        var info = CreateProcessUiInfo(process);
                        if (info != null && info.HasVisibleUi)
                        {
                            results.Add(info);
                        }
                    }
                    catch (Exception)
                    {
                        // Skip processes we can't access
                        continue;
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                return (IReadOnlyList<ProcessUiInfo>)results;
            }
            catch (Exception)
            {
                return new List<ProcessUiInfo>();
            }
        }, cancellationToken);
    }

    public async Task<int> GetProcessIdFromWindowAsync(IntPtr windowHandle, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            GetWindowThreadProcessId(windowHandle, out uint processId);
            return (int)processId;
        }, cancellationToken);
    }

    private ProcessUiInfo? CreateProcessUiInfo(Process process)
    {
        try
        {
            // Check if process has a main window
            var hasMainWindow = process.MainWindowHandle != IntPtr.Zero;
            var isVisible = hasMainWindow && IsWindowVisible(process.MainWindowHandle);

            var info = new ProcessUiInfo
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                MainWindowTitle = hasMainWindow ? process.MainWindowTitle : string.Empty,
                MainWindowHandle = process.MainWindowHandle,
                HasVisibleUi = isVisible,
                EstimatedElementCount = EstimateElementCount(process),
                UiFramework = DetectUiFramework(process),
                Metadata = new Dictionary<string, object>
                {
                    ["StartTime"] = process.StartTime,
                    ["WorkingSet"] = process.WorkingSet64,
                    ["HasMainWindow"] = hasMainWindow
                }
            };

            return info;
        }
        catch (Exception)
        {
            // If we can't get process info, return null
            return null;
        }
    }

    private static int EstimateElementCount(Process process)
    {
        // Simple heuristic based on process characteristics
        try
        {
            var workingSet = process.WorkingSet64;
            var hasMainWindow = process.MainWindowHandle != IntPtr.Zero;

            if (!hasMainWindow)
                return 0;

            // Rough estimation based on memory usage
            if (workingSet > 500_000_000) // > 500MB
                return 1000;
            else if (workingSet > 100_000_000) // > 100MB
                return 500;
            else if (workingSet > 50_000_000) // > 50MB
                return 200;
            else
                return 50;
        }
        catch (Exception)
        {
            return 50; // Default estimate
        }
    }

    private static string DetectUiFramework(Process process)
    {
        try
        {
            var processName = process.ProcessName.ToLowerInvariant();
            
            // Simple framework detection based on process name and characteristics
            if (processName.Contains("wpf") || processName.Contains("xaml"))
                return "WPF";
            else if (processName.Contains("winui") || processName.Contains("uwp"))
                return "WinUI/UWP";
            else if (processName.Contains("electron") || processName.Contains("chrome"))
                return "Electron/Chromium";
            else if (processName.Contains("java") || processName.Contains("javaw"))
                return "Java/Swing";
            else if (processName.Contains("notepad") || processName.Contains("calc"))
                return "Win32";
            else
                return "Unknown";
        }
        catch (Exception)
        {
            return "Unknown";
        }
    }
}

using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Shapes;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Windows.Storage;
using Windows.Storage.Pickers;
using CognitiveDrive.CoreUIA;

namespace CognitiveDrive.Chariot
{
    /// <summary>
    /// Main window for the Chariot HET visualizer application.
    /// Provides functionality to load HET JSON files and render visual overlays.
    /// </summary>
    public sealed partial class MainWindow : Window
    {
        private UiaElementNode? _currentHetRoot;
        private int _totalElementsRendered;

        public MainWindow()
        {
            this.InitializeComponent();
            this.Title = "CognitiveDrive Chariot - HET Visualizer";
        }

        /// <summary>
        /// Handles the Load HET File button click event.
        /// Opens a file picker to select a JSON file and loads the HET data.
        /// </summary>
        private async void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create file picker
                var picker = new FileOpenPicker();
                
                // Initialize the picker with the window handle
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(this);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);
                
                // Configure picker
                picker.ViewMode = PickerViewMode.List;
                picker.SuggestedStartLocation = PickerLocationId.Desktop;
                picker.FileTypeFilter.Add(".json");

                // Show picker and get selected file
                StorageFile file = await picker.PickSingleFileAsync();
                if (file != null)
                {
                    await LoadHetFile(file);
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error opening file picker: {ex.Message}";
            }
        }

        /// <summary>
        /// Handles the Clear Overlay button click event.
        /// Clears all rendered elements from the canvas.
        /// </summary>
        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ClearOverlay();
            StatusText.Text = "Overlay cleared";
            ClearButton.IsEnabled = false;
        }

        /// <summary>
        /// Loads and processes a HET JSON file.
        /// </summary>
        /// <param name="file">The StorageFile containing the HET JSON data</param>
        private async Task LoadHetFile(StorageFile file)
        {
            try
            {
                StatusText.Text = "Loading HET file...";
                
                // Read file content
                string jsonContent = await FileIO.ReadTextAsync(file);
                
                StatusText.Text = "Parsing JSON...";
                
                // Deserialize JSON to HET object model
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                };
                
                _currentHetRoot = JsonSerializer.Deserialize<UiaElementNode>(jsonContent, jsonOptions);
                
                if (_currentHetRoot == null)
                {
                    StatusText.Text = "Error: Failed to parse HET JSON";
                    return;
                }

                StatusText.Text = "Rendering overlay...";
                
                // Clear existing overlay
                ClearOverlay();
                
                // Reset counter
                _totalElementsRendered = 0;
                
                // Render the HET structure
                RenderNode(_currentHetRoot);
                
                StatusText.Text = $"HET loaded successfully! Rendered {_totalElementsRendered} elements with physical locations";
                ClearButton.IsEnabled = true;
                
                // Hide instructions text
                InstructionsText.Visibility = Visibility.Collapsed;
            }
            catch (JsonException ex)
            {
                StatusText.Text = $"JSON parsing error: {ex.Message}";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error loading HET file: {ex.Message}";
            }
        }

        /// <summary>
        /// Recursively renders a HET node and its children as visual overlays.
        /// </summary>
        /// <param name="node">The UiaElementNode to render</param>
        private void RenderNode(UiaElementNode node)
        {
            if (node == null) return;

            // Only render nodes that have a physical location
            if (node.BoundingRectangle.HasValue && node.HasPhysicalLocation)
            {
                var rect = node.BoundingRectangle.Value;
                
                // Create visual rectangle
                var rectangle = new Rectangle
                {
                    Width = rect.Width,
                    Height = rect.Height,
                    Stroke = new SolidColorBrush(Microsoft.UI.Colors.Red),
                    StrokeThickness = 2,
                    Fill = new SolidColorBrush(Microsoft.UI.Colors.Transparent)
                };

                // Position the rectangle on the canvas
                Canvas.SetLeft(rectangle, rect.X);
                Canvas.SetTop(rectangle, rect.Y);

                // Create tooltip with element information
                var tooltip = new ToolTip();
                var tooltipContent = CreateTooltipContent(node);
                tooltip.Content = tooltipContent;
                ToolTipService.SetToolTip(rectangle, tooltip);

                // Add to canvas
                OverlayCanvas.Children.Add(rectangle);
                _totalElementsRendered++;
            }

            // Recursively render all children
            foreach (var child in node.Children)
            {
                RenderNode(child);
            }
        }

        /// <summary>
        /// Creates formatted tooltip content for a UI element node.
        /// </summary>
        /// <param name="node">The UiaElementNode to create tooltip content for</param>
        /// <returns>A formatted string with element information</returns>
        private string CreateTooltipContent(UiaElementNode node)
        {
            var content = new System.Text.StringBuilder();
            
            content.AppendLine($"Name: {(string.IsNullOrEmpty(node.Name) ? "<No Name>" : node.Name)}");
            content.AppendLine($"Control Type: {node.ControlType}");
            content.AppendLine($"Automation ID: {(string.IsNullOrEmpty(node.AutomationId) ? "<No ID>" : node.AutomationId)}");
            content.AppendLine($"Enabled: {node.IsEnabled}");
            content.AppendLine($"Offscreen: {node.IsOffscreen}");
            
            if (node.BoundingRectangle.HasValue)
            {
                var rect = node.BoundingRectangle.Value;
                content.AppendLine($"Position: ({rect.X:F0}, {rect.Y:F0})");
                content.AppendLine($"Size: {rect.Width:F0} x {rect.Height:F0}");
            }
            
            content.AppendLine($"Children: {node.Children.Count}");
            
            return content.ToString().TrimEnd();
        }

        /// <summary>
        /// Clears all rendered elements from the overlay canvas.
        /// </summary>
        private void ClearOverlay()
        {
            // Remove all children except the instructions text
            var elementsToRemove = new System.Collections.Generic.List<UIElement>();
            
            foreach (UIElement child in OverlayCanvas.Children)
            {
                if (child != InstructionsText)
                {
                    elementsToRemove.Add(child);
                }
            }
            
            foreach (var element in elementsToRemove)
            {
                OverlayCanvas.Children.Remove(element);
            }
            
            // Show instructions text if no HET is loaded
            if (_currentHetRoot == null)
            {
                InstructionsText.Visibility = Visibility.Visible;
            }
            
            _totalElementsRendered = 0;
        }
    }
}

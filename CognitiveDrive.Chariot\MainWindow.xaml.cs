using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Shapes;
using System;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using CognitiveDrive.CoreUIA;

namespace CognitiveDrive.Chariot
{
    /// <summary>
    /// Main window for the Chariot HET visualizer application.
    /// Provides real-time visual overlay rendering of live HET data from the daemon service.
    /// </summary>
    public sealed partial class MainWindow : Window
    {
        private UiaElementNode? _currentHetRoot;
        private int _totalElementsRendered;
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _namedPipeClientTask;
        private bool _isConnected = false;
        private readonly string _pipeName = "CognitiveDrivePipe";

        public MainWindow()
        {
            this.InitializeComponent();
            this.Title = "CognitiveDrive Chariot - Live HET Visualizer";
        }

        /// <summary>
        /// Handles the Connect to Live Agent button click event.
        /// Connects to the CognitiveDrive Daemon via Named Pipe for real-time HET updates.
        /// </summary>
        private async void ConnectButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!_isConnected)
                {
                    // Start connection
                    StatusText.Text = "Connecting to Live Agent...";
                    ConnectButton.Content = "Disconnect";

                    _cancellationTokenSource = new CancellationTokenSource();
                    _namedPipeClientTask = Task.Run(() => RunNamedPipeClient(_cancellationTokenSource.Token));
                }
                else
                {
                    // Stop connection
                    await DisconnectFromAgent();
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Connection Error: {ex.Message}";
                await DisconnectFromAgent();
            }
        }

        /// <summary>
        /// Handles the Clear Overlay button click event.
        /// Clears all rendered elements from the canvas.
        /// </summary>
        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ClearOverlay();
            StatusText.Text = "Overlay cleared";
            ClearButton.IsEnabled = false;
        }

        /// <summary>
        /// Runs the Named Pipe client to receive live HET updates from the daemon.
        /// </summary>
        private async Task RunNamedPipeClient(CancellationToken cancellationToken)
        {
            try
            {
                using (var pipeClient = new NamedPipeClientStream(".", _pipeName, PipeDirection.In))
                {
                    // Connect to the daemon's Named Pipe server
                    await pipeClient.ConnectAsync(5000, cancellationToken); // 5 second timeout

                    // Update UI on successful connection
                    this.DispatcherQueue.TryEnqueue(() =>
                    {
                        _isConnected = true;
                        StatusText.Text = "Connected to Live Agent";
                        InstructionsText.Visibility = Visibility.Collapsed;
                    });

                    // Read HET updates from the pipe
                    var buffer = new byte[1024 * 1024]; // 1MB buffer for large HET data

                    while (pipeClient.IsConnected && !cancellationToken.IsCancellationRequested)
                    {
                        try
                        {
                            int bytesRead = await pipeClient.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                            if (bytesRead > 0)
                            {
                                string jsonData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                                await ProcessLiveHETUpdate(jsonData);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            break;
                        }
                        catch (Exception ex)
                        {
                            this.DispatcherQueue.TryEnqueue(() =>
                            {
                                StatusText.Text = $"Data receive error: {ex.Message}";
                            });
                            break;
                        }
                    }
                }
            }
            catch (TimeoutException)
            {
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    StatusText.Text = "Connection timeout - Is the daemon service running?";
                });
            }
            catch (Exception ex)
            {
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    StatusText.Text = $"Connection failed: {ex.Message}";
                });
            }
            finally
            {
                await DisconnectFromAgent();
            }
        }

        /// <summary>
        /// Processes a live HET update received from the daemon.
        /// </summary>
        private async Task ProcessLiveHETUpdate(string jsonData)
        {
            try
            {
                // Deserialize the HET data
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                };

                var hetRoot = JsonSerializer.Deserialize<UiaElementNode>(jsonData, jsonOptions);

                if (hetRoot != null)
                {
                    // Update UI on the main thread
                    this.DispatcherQueue.TryEnqueue(() =>
                    {
                        _currentHetRoot = hetRoot;

                        // Clear and re-render the overlay
                        ClearOverlay();
                        _totalElementsRendered = 0;
                        RenderNode(_currentHetRoot);

                        StatusText.Text = $"Live HET updated - {_totalElementsRendered} elements rendered";
                        ClearButton.IsEnabled = true;
                    });
                }
            }
            catch (JsonException ex)
            {
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    StatusText.Text = $"JSON parsing error: {ex.Message}";
                });
            }
            catch (Exception ex)
            {
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    StatusText.Text = $"HET update error: {ex.Message}";
                });
            }
        }

        /// <summary>
        /// Disconnects from the live agent and cleans up resources.
        /// </summary>
        private async Task DisconnectFromAgent()
        {
            try
            {
                _cancellationTokenSource?.Cancel();

                if (_namedPipeClientTask != null)
                {
                    await _namedPipeClientTask;
                }

                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                _namedPipeClientTask = null;
                _isConnected = false;

                StatusText.Text = "Disconnected from Live Agent";
                ConnectButton.Content = "Connect to Live Agent";
                ClearButton.IsEnabled = false;

                // Show instructions text
                if (_currentHetRoot == null)
                {
                    InstructionsText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Disconnect error: {ex.Message}";
            }
        }

        /// <summary>
        /// Recursively renders a HET node and its children as visual overlays.
        /// </summary>
        /// <param name="node">The UiaElementNode to render</param>
        private void RenderNode(UiaElementNode node)
        {
            if (node == null) return;

            // Only render nodes that have a physical location
            if (node.BoundingRectangle.HasValue && node.HasPhysicalLocation)
            {
                var rect = node.BoundingRectangle.Value;
                
                // Create visual rectangle
                var rectangle = new Rectangle
                {
                    Width = rect.Width,
                    Height = rect.Height,
                    Stroke = new SolidColorBrush(Microsoft.UI.Colors.Red),
                    StrokeThickness = 2,
                    Fill = new SolidColorBrush(Microsoft.UI.Colors.Transparent)
                };

                // Position the rectangle on the canvas
                Canvas.SetLeft(rectangle, rect.X);
                Canvas.SetTop(rectangle, rect.Y);

                // Create tooltip with element information
                var tooltip = new ToolTip();
                var tooltipContent = CreateTooltipContent(node);
                tooltip.Content = tooltipContent;
                ToolTipService.SetToolTip(rectangle, tooltip);

                // Add to canvas
                OverlayCanvas.Children.Add(rectangle);
                _totalElementsRendered++;
            }

            // Recursively render all children
            foreach (var child in node.Children)
            {
                RenderNode(child);
            }
        }

        /// <summary>
        /// Creates formatted tooltip content for a UI element node.
        /// </summary>
        /// <param name="node">The UiaElementNode to create tooltip content for</param>
        /// <returns>A formatted string with element information</returns>
        private string CreateTooltipContent(UiaElementNode node)
        {
            var content = new System.Text.StringBuilder();
            
            content.AppendLine($"Name: {(string.IsNullOrEmpty(node.Name) ? "<No Name>" : node.Name)}");
            content.AppendLine($"Control Type: {node.ControlType}");
            content.AppendLine($"Automation ID: {(string.IsNullOrEmpty(node.AutomationId) ? "<No ID>" : node.AutomationId)}");
            content.AppendLine($"Enabled: {node.IsEnabled}");
            content.AppendLine($"Offscreen: {node.IsOffscreen}");
            
            if (node.BoundingRectangle.HasValue)
            {
                var rect = node.BoundingRectangle.Value;
                content.AppendLine($"Position: ({rect.X:F0}, {rect.Y:F0})");
                content.AppendLine($"Size: {rect.Width:F0} x {rect.Height:F0}");
            }
            
            content.AppendLine($"Children: {node.Children.Count}");
            
            return content.ToString().TrimEnd();
        }

        /// <summary>
        /// Clears all rendered elements from the overlay canvas.
        /// </summary>
        private void ClearOverlay()
        {
            // Remove all children except the instructions text
            var elementsToRemove = new System.Collections.Generic.List<UIElement>();
            
            foreach (UIElement child in OverlayCanvas.Children)
            {
                if (child != InstructionsText)
                {
                    elementsToRemove.Add(child);
                }
            }
            
            foreach (var element in elementsToRemove)
            {
                OverlayCanvas.Children.Remove(element);
            }
            
            // Show instructions text if no HET is loaded
            if (_currentHetRoot == null)
            {
                InstructionsText.Visibility = Visibility.Visible;
            }
            
            _totalElementsRendered = 0;
        }
    }
}

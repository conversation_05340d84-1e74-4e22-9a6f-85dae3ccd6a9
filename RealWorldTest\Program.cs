﻿using System;
using System.IO;
using System.Text.Json;
using CognitiveDrive.CoreUIA;

namespace RealWorldTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("🧪 REAL-WORLD TARGETING TEST: Edge Browser HET");

            try
            {
                // Load the actual Edge HET JSON
                var jsonContent = File.ReadAllText(@"C:\Users\<USER>\OneDrive\Desktop\Project\edge_stress_test.json");
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                };

                var hetRoot = JsonSerializer.Deserialize<UiaElementNode>(jsonContent, jsonOptions);
                if (hetRoot == null)
                {
                    Console.WriteLine("❌ Failed to deserialize Edge HET");
                    return;
                }

                Console.WriteLine($"✅ Loaded Edge HET: {hetRoot.TotalDescendantCount + 1} elements");

                // Test 1: Find elements by common browser AutomationIds
                Console.WriteLine("\n🎯 Testing AutomationId targeting:");

                var addressBar = Targeting.FindElement(hetRoot, "id=addressEditBox");
                Console.WriteLine($"Address Bar: {(addressBar != null ? "✅ FOUND" : "❌ NOT FOUND")}");

                var backButton = Targeting.FindElement(hetRoot, "id=backButton");
                Console.WriteLine($"Back Button: {(backButton != null ? "✅ FOUND" : "❌ NOT FOUND")}");

                var forwardButton = Targeting.FindElement(hetRoot, "id=forwardButton");
                Console.WriteLine($"Forward Button: {(forwardButton != null ? "✅ FOUND" : "❌ NOT FOUND")}");

                // Test 2: Find elements by Name (fallback strategy)
                Console.WriteLine("\n🎯 Testing Name targeting (fallback):");

                var closeButton = Targeting.FindElement(hetRoot, "name=Close");
                Console.WriteLine($"Close Button: {(closeButton != null ? "✅ FOUND" : "❌ NOT FOUND")}");

                var minimizeButton = Targeting.FindElement(hetRoot, "name=Minimize");
                Console.WriteLine($"Minimize Button: {(minimizeButton != null ? "✅ FOUND" : "❌ NOT FOUND")}");

                // Test 3: Performance test with large HET
                Console.WriteLine("\n⚡ Performance testing:");
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                for (int i = 0; i < 100; i++)
                {
                    Targeting.FindElement(hetRoot, "name=Close");
                }

                stopwatch.Stop();
                Console.WriteLine($"100 searches completed in {stopwatch.ElapsedMilliseconds}ms");
                Console.WriteLine($"Average: {stopwatch.ElapsedMilliseconds / 100.0:F2}ms per search");

                // Test 4: Find all elements with same name
                Console.WriteLine("\n🔍 Testing FindAllElements:");
                var allButtons = Targeting.FindAllElements(hetRoot, "name=Button");
                Console.WriteLine($"Found {allButtons.Count} elements named 'Button'");

                // Test 5: Error handling
                Console.WriteLine("\n🛡️ Testing error handling:");
                try
                {
                    Targeting.FindElement(hetRoot, "invalid_query");
                    Console.WriteLine("❌ Should have thrown exception for invalid query");
                }
                catch (ArgumentException)
                {
                    Console.WriteLine("✅ Correctly handled invalid query");
                }

                try
                {
                    Targeting.FindElement(null, "id=test");
                    Console.WriteLine("❌ Should have thrown exception for null root");
                }
                catch (ArgumentNullException)
                {
                    Console.WriteLine("✅ Correctly handled null root");
                }

                Console.WriteLine("\n🎉 REAL-WORLD TARGETING TEST COMPLETE");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}

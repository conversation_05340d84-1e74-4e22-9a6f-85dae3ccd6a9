﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using CognitiveDrive.CoreUIA;

/// <summary>
/// Proper HET analyzer that actually examines what AutomationIds and Names are available
/// instead of making assumptions about what should be actionable.
/// </summary>
class ProperHETAnalyzer
{
    static void Main(string[] args)
    {
        Console.WriteLine("🔍 PROPER HET ANALYSIS - Let's see what's actually there");
        Console.WriteLine("========================================================");

        var files = new[]
        {
            ("Edge Browser", @"C:\Users\<USER>\OneDrive\Desktop\Project\edge_stress_test.json"),
            ("VS Code", @"C:\Users\<USER>\OneDrive\Desktop\Project\vscode_automation_analysis.json"),
            ("Notepad", @"C:\Users\<USER>\OneDrive\Desktop\Project\notepad_automation_analysis.json")
        };

        foreach (var (name, file) in files)
        {
            if (File.Exists(file))
            {
                Console.WriteLine($"\n📊 ANALYZING {name.ToUpper()}:");
                AnalyzeHETFile(file);
            }
            else
            {
                Console.WriteLine($"\n❌ {file} not found");
            }
        }
    }

    static void AnalyzeHETFile(string fileName)
    {
        try
        {
            var jsonContent = File.ReadAllText(fileName);
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };

            var hetRoot = JsonSerializer.Deserialize<UiaElementNode>(jsonContent, jsonOptions);
            if (hetRoot == null)
            {
                Console.WriteLine("❌ Failed to deserialize HET");
                return;
            }

            var allElements = new List<UiaElementNode>();
            CollectAllElements(hetRoot, allElements);

            Console.WriteLine($"Total elements: {allElements.Count}");

            // Group by control type to see what we actually have
            var controlTypes = allElements.GroupBy(e => e.ControlType)
                                        .OrderByDescending(g => g.Count())
                                        .Take(10);

            Console.WriteLine("\nTop 10 Control Types:");
            foreach (var group in controlTypes)
            {
                var withId = group.Count(e => !string.IsNullOrEmpty(e.AutomationId));
                var withName = group.Count(e => !string.IsNullOrEmpty(e.Name));
                Console.WriteLine($"  {group.Key}: {group.Count()} total, {withId} with AutomationId, {withName} with Name");
            }

            // Show actual AutomationIds that exist
            var elementsWithIds = allElements.Where(e => !string.IsNullOrEmpty(e.AutomationId)).ToList();
            Console.WriteLine($"\nElements with AutomationId: {elementsWithIds.Count}");

            if (elementsWithIds.Count > 0)
            {
                Console.WriteLine("Sample AutomationIds found:");
                var sampleIds = elementsWithIds.Take(15).Select(e => $"  '{e.AutomationId}' ({e.ControlType})");
                foreach (var id in sampleIds)
                {
                    Console.WriteLine(id);
                }
                if (elementsWithIds.Count > 15)
                {
                    Console.WriteLine($"  ... and {elementsWithIds.Count - 15} more");
                }
            }

            // Show actual Names that exist for elements without AutomationId
            var elementsWithNamesOnly = allElements.Where(e => string.IsNullOrEmpty(e.AutomationId) && !string.IsNullOrEmpty(e.Name)).ToList();
            Console.WriteLine($"\nElements with Name only (no AutomationId): {elementsWithNamesOnly.Count}");

            if (elementsWithNamesOnly.Count > 0)
            {
                Console.WriteLine("Sample Names found:");
                var sampleNames = elementsWithNamesOnly.Take(15).Select(e => $"  '{e.Name}' ({e.ControlType})");
                foreach (var name in sampleNames)
                {
                    Console.WriteLine(name);
                }
                if (elementsWithNamesOnly.Count > 15)
                {
                    Console.WriteLine($"  ... and {elementsWithNamesOnly.Count - 15} more");
                }
            }

            // Test actual targeting with real IDs/Names found
            Console.WriteLine("\n🎯 TESTING ACTUAL TARGETING:");

            // Test with real AutomationIds found
            if (elementsWithIds.Count > 0)
            {
                var testId = elementsWithIds.First().AutomationId;
                var found = Targeting.FindElement(hetRoot, $"id={testId}");
                Console.WriteLine($"✅ Testing id={testId}: {(found != null ? "FOUND" : "NOT FOUND")}");
            }

            // Test with real Names found
            if (elementsWithNamesOnly.Count > 0)
            {
                var testName = elementsWithNamesOnly.First().Name;
                var found = Targeting.FindElement(hetRoot, $"name={testName}");
                Console.WriteLine($"✅ Testing name={testName}: {(found != null ? "FOUND" : "NOT FOUND")}");
            }

            // Look for common UI elements that should be targetable
            Console.WriteLine("\n🔍 LOOKING FOR COMMON UI ELEMENTS:");
            TestCommonElements(hetRoot);

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error analyzing {fileName}: {ex.Message}");
        }
    }

    static void CollectAllElements(UiaElementNode node, List<UiaElementNode> allElements)
    {
        allElements.Add(node);
        foreach (var child in node.Children)
        {
            CollectAllElements(child, allElements);
        }
    }

    static void TestCommonElements(UiaElementNode root)
    {
        var commonTargets = new[]
        {
            // Window controls
            ("name=Close", "Close button"),
            ("name=Minimize", "Minimize button"),
            ("name=Maximize", "Maximize button"),
            ("name=Restore", "Restore button"),

            // Browser controls
            ("id=addressEditBox", "Address bar"),
            ("id=backButton", "Back button"),
            ("id=forwardButton", "Forward button"),
            ("id=refreshButton", "Refresh button"),
            ("name=Back", "Back button by name"),
            ("name=Forward", "Forward button by name"),
            ("name=Refresh", "Refresh button by name"),

            // Common buttons
            ("name=OK", "OK button"),
            ("name=Cancel", "Cancel button"),
            ("name=Save", "Save button"),
            ("name=Open", "Open button"),

            // Menu items
            ("name=File", "File menu"),
            ("name=Edit", "Edit menu"),
            ("name=View", "View menu"),
            ("name=Help", "Help menu")
        };

        foreach (var (query, description) in commonTargets)
        {
            var found = Targeting.FindElement(root, query);
            if (found != null)
            {
                Console.WriteLine($"  ✅ {description}: FOUND");
            }
        }
    }
}

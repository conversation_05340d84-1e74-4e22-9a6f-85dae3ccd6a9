using System;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Threading.Tasks;

class TestPipeClient
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Testing Named Pipe connection to CognitiveDrive Daemon...");
        
        try
        {
            using (var pipeClient = new NamedPipeClientStream(".", "CognitiveDrivePipe", PipeDirection.In))
            {
                Console.WriteLine("Connecting to daemon...");
                await pipeClient.ConnectAsync(5000); // 5 second timeout
                Console.WriteLine("Connected successfully!");
                
                var buffer = new byte[1024 * 1024]; // 1MB buffer
                
                for (int i = 0; i < 3; i++) // Read 3 messages
                {
                    Console.WriteLine($"Reading message {i + 1}...");
                    int bytesRead = await pipeClient.ReadAsync(buffer, 0, buffer.Length);
                    
                    if (bytesRead > 0)
                    {
                        string jsonData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        Console.WriteLine($"Received {bytesRead} bytes");
                        Console.WriteLine($"First 200 chars: {jsonData.Substring(0, Math.Min(200, jsonData.Length))}...");
                        Console.WriteLine();
                    }
                }
            }
        }
        catch (TimeoutException)
        {
            Console.WriteLine("Connection timeout - Is the daemon running?");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        
        Console.WriteLine("Test completed. Press any key to exit...");
        Console.ReadKey();
    }
}

using System;
using System.ServiceProcess;

namespace CognitiveDrive.Daemon
{
    /// <summary>
    /// Main entry point for the CognitiveDrive Daemon Windows Service.
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main(string[] args)
        {
            // Check if running in console mode for debugging
            if (Environment.UserInteractive)
            {
                // Running as console application for debugging
                Console.WriteLine("CognitiveDrive Daemon - Console Mode");
                Console.WriteLine("Press any key to start the service...");
                Console.ReadKey();

                var service = new CognitiveDaemon();
                
                // Start the service manually
                service.TestStartupAndStop(args);
                
                Console.WriteLine("Press any key to stop the service...");
                Console.ReadKey();
            }
            else
            {
                // Running as Windows Service
                ServiceBase[] ServicesToRun;
                ServicesToRun = new ServiceBase[]
                {
                    new CognitiveDaemon()
                };
                ServiceBase.Run(ServicesToRun);
            }
        }
    }
}

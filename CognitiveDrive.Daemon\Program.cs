using CognitiveDrive.Core;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Infrastructure;
using CognitiveDrive.Infrastructure.Interfaces;

/// <summary>
/// THE ARCHIVIST - CognitiveDrive Daemon with MemoryVault integration.
/// Continuously monitors UI states and stores semantic memories for deduplication.
/// Sprint 5: Implements the agent's long-term memory system.
/// </summary>
class Program
{
    private static MemoryVault? _memoryVault;
    private static WindowsUiaScanner? _scanner;
    private static bool _running = true;

    static async Task Main(string[] args)
    {
        Console.WriteLine("🧠 THE ARCHIVIST - CognitiveDrive Daemon");
        Console.WriteLine("========================================");
        Console.WriteLine("Sprint 5: MemoryVault Integration");
        Console.WriteLine("Monitoring UI states for semantic learning...\n");

        try
        {
            // Initialize components
            await InitializeAsync();

            // Start the main monitoring loop
            await RunMainLoopAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DAEMON ERROR: {ex.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");
        }
        finally
        {
            await CleanupAsync();
        }
    }

    /// <summary>
    /// Initializes the daemon components.
    /// </summary>
    private static async Task InitializeAsync()
    {
        Console.WriteLine("🚀 Initializing daemon components...");

        // Initialize MemoryVault
        _memoryVault = new MemoryVault();
        Console.WriteLine("   - MemoryVault initialized ✅");

        // Initialize scanner components
        var configuration = new ScannerConfiguration
        {
            MaxScanDepth = 10,
            MaxElementsPerScan = 500,
            PropertyExtractionTimeout = TimeSpan.FromSeconds(2),
            ChildEnumerationTimeout = TimeSpan.FromSeconds(5)
        };

        var propertyExtractor = new WindowsUiaPropertyExtractor(configuration);
        var processManager = new WindowsProcessManager();
        _scanner = new WindowsUiaScanner(propertyExtractor, processManager, configuration);

        Console.WriteLine("   - UIA Scanner initialized ✅");
        Console.WriteLine();
    }

    /// <summary>
    /// Main monitoring loop that processes UI states.
    /// </summary>
    private static async Task RunMainLoopAsync()
    {
        Console.WriteLine("🔄 Starting main monitoring loop...");
        Console.WriteLine("Press Ctrl+C to stop the daemon\n");

        // Handle Ctrl+C gracefully
        Console.CancelKeyPress += (sender, e) =>
        {
            e.Cancel = true;
            _running = false;
            Console.WriteLine("\n🛑 Shutdown requested...");
        };

        var loopCount = 0;
        while (_running)
        {
            try
            {
                loopCount++;
                Console.WriteLine($"📊 Loop #{loopCount} - {DateTime.Now:HH:mm:ss}");

                // Get available processes
                var processesResult = await _scanner!.GetAvailableProcessesAsync();
                if (!processesResult.IsSuccess)
                {
                    Console.WriteLine($"⚠️ Failed to get processes: {processesResult.ErrorMessage}");
                    await Task.Delay(5000);
                    continue;
                }

                var processes = processesResult.Value!.Where(p => p.HasMainWindow).Take(3).ToList();
                Console.WriteLine($"   - Found {processes.Count} processes with main windows");

                // Process each application
                foreach (var process in processes)
                {
                    try
                    {
                        Console.WriteLine($"   🔍 Scanning: {process.ProcessName} (PID: {process.ProcessId})");

                        // Scan the UI tree
                        var scanResult = await _scanner.ScanProcessAsync(process.ProcessId);
                        if (scanResult.IsSuccess)
                        {
                            var newHET = scanResult.Value!;
                            Console.WriteLine($"      - Elements found: {newHET.TotalDescendantCount + 1}");

                            // THE CORE INTEGRATION: Process UI state with MemoryVault
                            var memoryResult = await _memoryVault!.ProcessUIStateAsync(newHET);
                            Console.WriteLine($"      - Memory result: {memoryResult}");
                        }
                        else
                        {
                            Console.WriteLine($"      - Scan failed: {scanResult.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"      - Process error: {ex.Message}");
                    }
                }

                Console.WriteLine($"   ✅ Loop #{loopCount} completed\n");

                // Wait before next iteration
                await Task.Delay(10000); // 10 second intervals
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Loop error: {ex.Message}");
                await Task.Delay(5000);
            }
        }
    }

    /// <summary>
    /// Cleanup resources on shutdown.
    /// </summary>
    private static async Task CleanupAsync()
    {
        Console.WriteLine("🧹 Cleaning up resources...");

        try
        {
            _scanner?.Dispose();
            _memoryVault?.Dispose();

            Console.WriteLine("   - All resources disposed ✅");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ Cleanup error: {ex.Message}");
        }

        Console.WriteLine("🏁 Daemon shutdown complete");
        await Task.Delay(1000); // Brief pause before exit
    }
}

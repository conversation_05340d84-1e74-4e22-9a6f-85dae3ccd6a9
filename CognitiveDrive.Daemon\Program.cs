﻿using CognitiveDrive.Core;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Infrastructure;
using CognitiveDrive.Infrastructure.Interfaces;

/// <summary>
/// THE ARCHIVIST - CognitiveDrive Daemon with MemoryVault integration.
/// Continuously monitors UI states and stores semantic memories for deduplication.
/// Sprint 5: Implements the agent's long-term memory system.
/// </summary>
class Program
{
    private static MemoryVault? _memoryVault;
    private static WindowsUiaScanner? _scanner;
    private static bool _running = true;

    static async Task Main(string[] args)
    {
        Console.WriteLine("🧠 THE ARCHIVIST - CognitiveDrive Daemon");
        Console.WriteLine("========================================");
        Console.WriteLine("Sprint 5: MemoryVault Integration");
        Console.WriteLine("Monitoring UI states for semantic learning...\n");

        try
        {
            // Initialize components
            await InitializeAsync();

            // Start the main monitoring loop
            await RunMainLoopAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DAEMON ERROR: {ex.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");
        }
        finally
        {
            await CleanupAsync();
        }
    }

    /// <summary>
    /// Initializes the daemon components with comprehensive error handling.
    /// </summary>
    private static async Task InitializeAsync()
    {
        Console.WriteLine("🚀 Initializing daemon components...");

        try
        {
            // Initialize MemoryVault with detailed error reporting
            Console.WriteLine("   - Initializing MemoryVault...");
            _memoryVault = new MemoryVault();
            Console.WriteLine("   - MemoryVault initialized ✅");
        }
        catch (FileNotFoundException ex)
        {
            Console.WriteLine($"❌ CRITICAL ERROR: Required model files not found");
            Console.WriteLine($"   - {ex.Message}");
            Console.WriteLine($"   - Please ensure ONNX model files are in the models directory");
            throw new InvalidOperationException("Cannot start daemon without required model files", ex);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ CRITICAL ERROR: MemoryVault initialization failed");
            Console.WriteLine($"   - {ex.Message}");
            Console.WriteLine($"   - Stack trace: {ex.StackTrace}");
            throw;
        }

        try
        {
            // Initialize scanner components
            Console.WriteLine("   - Initializing UIA Scanner...");
            var configuration = new ScannerConfiguration
            {
                MaxScanDepth = 10,
                MaxElementsPerScan = 500,
                PropertyExtractionTimeout = TimeSpan.FromSeconds(2),
                ChildEnumerationTimeout = TimeSpan.FromSeconds(5)
            };

            var propertyExtractor = new WindowsUiaPropertyExtractor(configuration);
            var processManager = new WindowsProcessManager();
            _scanner = new WindowsUiaScanner(propertyExtractor, processManager, configuration);

            Console.WriteLine("   - UIA Scanner initialized ✅");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ CRITICAL ERROR: UIA Scanner initialization failed");
            Console.WriteLine($"   - {ex.Message}");
            throw;
        }

        Console.WriteLine();
    }

    /// <summary>
    /// Main monitoring loop that processes UI states.
    /// </summary>
    private static async Task RunMainLoopAsync()
    {
        Console.WriteLine("🔄 Starting main monitoring loop...");
        Console.WriteLine("Press Ctrl+C to stop the daemon\n");

        // Handle Ctrl+C gracefully
        Console.CancelKeyPress += (sender, e) =>
        {
            e.Cancel = true;
            _running = false;
            Console.WriteLine("\n🛑 Shutdown requested...");
        };

        var loopCount = 0;
        while (_running)
        {
            try
            {
                loopCount++;
                Console.WriteLine($"📊 Loop #{loopCount} - {DateTime.Now:HH:mm:ss}");

                // Get available processes
                var processesResult = await _scanner!.GetAvailableProcessesAsync();
                if (!processesResult.IsSuccess)
                {
                    Console.WriteLine($"⚠️ Failed to get processes: {processesResult.ErrorMessage}");
                    await Task.Delay(5000);
                    continue;
                }

                var allProcesses = processesResult.Value!;
                var visibleProcesses = allProcesses.Where(p => p.HasVisibleUi).ToList();
                var processes = visibleProcesses.Take(10).ToList(); // Increased from 3 to 10 for better detection

                Console.WriteLine($"   - Total processes scanned: {allProcesses.Count}");
                Console.WriteLine($"   - Processes with visible UI: {visibleProcesses.Count}");
                Console.WriteLine($"   - Processing top {processes.Count} processes");

                // Enhanced logging for debugging
                if (visibleProcesses.Count > 0)
                {
                    Console.WriteLine($"   - Visible processes: {string.Join(", ", visibleProcesses.Take(5).Select(p => $"{p.ProcessName}({p.ProcessId})"))}");
                    if (visibleProcesses.Count > 5)
                        Console.WriteLine($"     ... and {visibleProcesses.Count - 5} more");
                }

                // Process each application
                foreach (var process in processes)
                {
                    try
                    {
                        Console.WriteLine($"   🔍 Scanning: {process.ProcessName} (PID: {process.ProcessId})");

                        // Scan the UI tree
                        var scanResult = await _scanner.ScanProcessAsync(process.ProcessId);
                        if (scanResult.IsSuccess)
                        {
                            var newHET = scanResult.Value!;
                            Console.WriteLine($"      - Elements found: {newHET.TotalDescendantCount + 1}");

                            // THE CORE INTEGRATION: Process UI state with MemoryVault
                            var memoryResult = await _memoryVault!.ProcessUIStateAsync(newHET);
                            Console.WriteLine($"      - Memory result: {memoryResult}");
                        }
                        else
                        {
                            Console.WriteLine($"      - Scan failed: {scanResult.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"      - Process error: {ex.Message}");
                    }
                }

                Console.WriteLine($"   ✅ Loop #{loopCount} completed\n");

                // Wait before next iteration
                await Task.Delay(10000); // 10 second intervals
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Loop error: {ex.Message}");
                await Task.Delay(5000);
            }
        }
    }

    /// <summary>
    /// Cleanup resources on shutdown.
    /// </summary>
    private static async Task CleanupAsync()
    {
        Console.WriteLine("🧹 Cleaning up resources...");

        try
        {
            _scanner?.Dispose();
            _memoryVault?.Dispose();

            Console.WriteLine("   - All resources disposed ✅");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ Cleanup error: {ex.Message}");
        }

        Console.WriteLine("🏁 Daemon shutdown complete");
        await Task.Delay(1000); // Brief pause before exit
    }
}

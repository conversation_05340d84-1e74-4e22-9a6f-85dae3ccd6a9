C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\bin\Debug\net8.0\ArchitecturalPurityTest.exe
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\bin\Debug\net8.0\ArchitecturalPurityTest.deps.json
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\bin\Debug\net8.0\ArchitecturalPurityTest.runtimeconfig.json
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\bin\Debug\net8.0\ArchitecturalPurityTest.dll
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\bin\Debug\net8.0\ArchitecturalPurityTest.pdb
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\bin\Debug\net8.0\CognitiveDrive.Core.dll
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\bin\Debug\net8.0\CognitiveDrive.Core.pdb
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.AssemblyInfoInputs.cache
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.AssemblyInfo.cs
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\Architec.FF27F526.Up2Date
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.dll
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\refint\ArchitecturalPurityTest.dll
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.pdb
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ArchitecturalPurityTest.genruntimeconfig.cache
C:\Users\<USER>\OneDrive\Desktop\Project\ArchitecturalPurityTest\obj\Debug\net8.0\ref\ArchitecturalPurityTest.dll

﻿<?xml version="1.0" encoding="utf-8"?><XamlCompilerSaveState><XamlFeatureControlFlags>EnableXBindDiagnostics, EnableDefaultValidationContextGeneration, EnableWin32Codegen, UsingCSWinRT</XamlFeatureControlFlags><ReferenceAssemblyList><LocalAssembly PathName="c:\users\<USER>\onedrive\desktop\project\cognitivedrive.chariot\obj\debug\net8.0-windows10.0.19041.0\intermediatexaml\cognitivedrive.chariot.dll" HashGuid="40b45a58-1d95-4820-d4da-da38064d75ab" /><ReferenceAssembly PathName="c:\users\<USER>\onedrive\desktop\project\cognitivedrive.coreuia\bin\debug\net8.0-windows\cognitivedrive.coreuia.dll" HashGuid="eb5da406-f393-2b44-7d89-e6a3e5387483" /></ReferenceAssemblyList><XamlSourceFileDataList><XamlSourceFileData XamlFileName="App.xaml" ClassFullName="CognitiveDrive.Chariot.App" GeneratedCodePathPrefix="C:\Users\<USER>\OneDrive\Desktop\Project\CognitiveDrive.Chariot\obj\Debug\net8.0-windows10.0.19041.0\App" XamlFileTimeAtLastCompileInTicks="638885642655329645" HasBoundEventAssignments="False" /><XamlSourceFileData XamlFileName="MainWindow.xaml" ClassFullName="CognitiveDrive.Chariot.MainWindow" GeneratedCodePathPrefix="C:\Users\<USER>\OneDrive\Desktop\Project\CognitiveDrive.Chariot\obj\Debug\net8.0-windows10.0.19041.0\MainWindow" XamlFileTimeAtLastCompileInTicks="638885656273765648" HasBoundEventAssignments="False" /></XamlSourceFileDataList></XamlCompilerSaveState>
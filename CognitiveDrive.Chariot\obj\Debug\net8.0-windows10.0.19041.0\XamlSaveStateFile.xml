﻿<?xml version="1.0" encoding="utf-8"?><XamlCompilerSaveState><XamlFeatureControlFlags>EnableXBindDiagnostics, EnableDefaultValidationContextGeneration, EnableWin32Codegen, UsingCSWinRT</XamlFeatureControlFlags><ReferenceAssemblyList><LocalAssembly PathName="c:\users\<USER>\onedrive\desktop\project\cognitivedrive.chariot\obj\debug\net8.0-windows10.0.19041.0\intermediatexaml\cognitivedrive.chariot.dll" HashGuid="a318cf06-3c0d-281b-5999-41a154c388b6" /><ReferenceAssembly PathName="c:\users\<USER>\onedrive\desktop\project\cognitivedrive.coreuia\bin\debug\net8.0-windows\cognitivedrive.coreuia.dll" HashGuid="7ff1edfb-a5c5-fec0-8e46-2a49d0f21ec2" /></ReferenceAssemblyList><XamlSourceFileDataList><XamlSourceFileData XamlFileName="App.xaml" ClassFullName="CognitiveDrive.Chariot.App" GeneratedCodePathPrefix="C:\Users\<USER>\OneDrive\Desktop\Project\CognitiveDrive.Chariot\obj\Debug\net8.0-windows10.0.19041.0\App" XamlFileTimeAtLastCompileInTicks="638885642655329645" HasBoundEventAssignments="False" /><XamlSourceFileData XamlFileName="MainWindow.xaml" ClassFullName="CognitiveDrive.Chariot.MainWindow" GeneratedCodePathPrefix="C:\Users\<USER>\OneDrive\Desktop\Project\CognitiveDrive.Chariot\obj\Debug\net8.0-windows10.0.19041.0\MainWindow" XamlFileTimeAtLastCompileInTicks="638885666064794971" HasBoundEventAssignments="False" /></XamlSourceFileDataList></XamlCompilerSaveState>
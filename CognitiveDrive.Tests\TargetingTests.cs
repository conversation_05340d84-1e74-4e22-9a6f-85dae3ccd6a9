using System;
using System.Collections.Generic;
using CognitiveDrive.CoreUIA;
using NUnit.Framework;

namespace CognitiveDrive.Tests
{
    /// <summary>
    /// Unit tests for the multi-tier targeting system to verify reliable element identification.
    /// </summary>
    [TestFixture]
    public class TargetingTests
    {
        private UiaElementNode _testHetRoot;

        /// <summary>
        /// Sets up a mock HET structure representing a Notepad-like application for testing.
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            // Create a mock HET structure similar to Notepad
            _testHetRoot = new UiaElementNode
            {
                Name = "Untitled - Notepad",
                ControlType = "ControlType.Window",
                AutomationId = "MainWindow",
                IsEnabled = true,
                IsOffscreen = false,
                BoundingRectangle = new System.Windows.Rect(100, 100, 800, 600),
                Children = new List<UiaElementNode>
                {
                    new UiaElementNode
                    {
                        Name = "File",
                        ControlType = "ControlType.MenuItem",
                        AutomationId = "FileMenu",
                        IsEnabled = true,
                        IsOffscreen = false,
                        BoundingRectangle = new System.Windows.Rect(110, 120, 50, 20),
                        Children = new List<UiaElementNode>
                        {
                            new UiaElementNode
                            {
                                Name = "Save",
                                ControlType = "ControlType.MenuItem",
                                AutomationId = "SaveMenuItem",
                                IsEnabled = true,
                                IsOffscreen = false,
                                BoundingRectangle = new System.Windows.Rect(110, 140, 80, 20)
                            },
                            new UiaElementNode
                            {
                                Name = "Exit",
                                ControlType = "ControlType.MenuItem",
                                AutomationId = "ExitMenuItem",
                                IsEnabled = true,
                                IsOffscreen = false,
                                BoundingRectangle = new System.Windows.Rect(110, 160, 80, 20)
                            }
                        }
                    },
                    new UiaElementNode
                    {
                        Name = "",
                        ControlType = "ControlType.Edit",
                        AutomationId = "TextEditor",
                        IsEnabled = true,
                        IsOffscreen = false,
                        BoundingRectangle = new System.Windows.Rect(110, 200, 680, 400)
                    },
                    new UiaElementNode
                    {
                        Name = "Close",
                        ControlType = "ControlType.Button",
                        AutomationId = "", // No AutomationId - only Name available
                        IsEnabled = true,
                        IsOffscreen = false,
                        BoundingRectangle = new System.Windows.Rect(850, 110, 30, 20)
                    }
                }
            };
        }

        /// <summary>
        /// Tests that FindElement can successfully find an element by unique AutomationId.
        /// </summary>
        [Test]
        public void FindElement_ByUniqueAutomationId_ReturnsCorrectElement()
        {
            // Act
            var result = Targeting.FindElement(_testHetRoot, "id=SaveMenuItem");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Save"));
            Assert.That(result.AutomationId, Is.EqualTo("SaveMenuItem"));
            Assert.That(result.ControlType, Is.EqualTo("ControlType.MenuItem"));
        }

        /// <summary>
        /// Tests that FindElement can find an element by Name when AutomationId is not available.
        /// </summary>
        [Test]
        public void FindElement_ByName_ReturnsCorrectElement()
        {
            // Act
            var result = Targeting.FindElement(_testHetRoot, "name=Close");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Close"));
            Assert.That(result.ControlType, Is.EqualTo("ControlType.Button"));
            Assert.That(result.AutomationId, Is.EqualTo("")); // Verify this element has no AutomationId
        }

        /// <summary>
        /// Tests that FindElement returns null for a query that matches no elements.
        /// </summary>
        [Test]
        public void FindElement_WithNonExistentQuery_ReturnsNull()
        {
            // Act
            var result = Targeting.FindElement(_testHetRoot, "id=NonExistentButton");

            // Assert
            Assert.That(result, Is.Null);
        }

        /// <summary>
        /// Tests that FindElement returns null for a name query that matches no elements.
        /// </summary>
        [Test]
        public void FindElement_WithNonExistentName_ReturnsNull()
        {
            // Act
            var result = Targeting.FindElement(_testHetRoot, "name=NonExistentElement");

            // Assert
            Assert.That(result, Is.Null);
        }

        /// <summary>
        /// Tests that FindElement throws ArgumentNullException for null root node.
        /// </summary>
        [Test]
        public void FindElement_WithNullRootNode_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                Targeting.FindElement(null, "id=TestId"));
        }

        /// <summary>
        /// Tests that FindElement throws ArgumentException for null or empty query.
        /// </summary>
        [Test]
        public void FindElement_WithNullOrEmptyQuery_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => 
                Targeting.FindElement(_testHetRoot, null));
            
            Assert.Throws<ArgumentException>(() => 
                Targeting.FindElement(_testHetRoot, ""));
            
            Assert.Throws<ArgumentException>(() => 
                Targeting.FindElement(_testHetRoot, "   "));
        }

        /// <summary>
        /// Tests that FindElement throws ArgumentException for invalid query format.
        /// </summary>
        [Test]
        public void FindElement_WithInvalidQueryFormat_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => 
                Targeting.FindElement(_testHetRoot, "invalidquery"));
            
            Assert.Throws<ArgumentException>(() => 
                Targeting.FindElement(_testHetRoot, "id"));
            
            Assert.Throws<ArgumentException>(() => 
                Targeting.FindElement(_testHetRoot, "=value"));
        }

        /// <summary>
        /// Tests that FindElement throws ArgumentException for unsupported search type.
        /// </summary>
        [Test]
        public void FindElement_WithUnsupportedSearchType_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => 
                Targeting.FindElement(_testHetRoot, "class=Button"));
        }

        /// <summary>
        /// Tests that FindElement can find the root element itself.
        /// </summary>
        [Test]
        public void FindElement_FindingRootElement_ReturnsRootElement()
        {
            // Act
            var result = Targeting.FindElement(_testHetRoot, "id=MainWindow");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.SameAs(_testHetRoot));
            Assert.That(result.Name, Is.EqualTo("Untitled - Notepad"));
        }

        /// <summary>
        /// Tests that FindAllElements returns all matching elements.
        /// </summary>
        [Test]
        public void FindAllElements_WithMultipleMatches_ReturnsAllMatches()
        {
            // Arrange: Add another element with the same name
            _testHetRoot.Children.Add(new UiaElementNode
            {
                Name = "File",
                ControlType = "ControlType.Button",
                AutomationId = "FileButton",
                IsEnabled = true,
                IsOffscreen = false,
                BoundingRectangle = new System.Windows.Rect(200, 120, 50, 20)
            });

            // Act
            var results = Targeting.FindAllElements(_testHetRoot, "name=File");

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results.Count, Is.EqualTo(2));
            Assert.That(results[0].ControlType, Is.EqualTo("ControlType.MenuItem"));
            Assert.That(results[1].ControlType, Is.EqualTo("ControlType.Button"));
        }

        /// <summary>
        /// Tests that FindAllElements returns empty list when no matches found.
        /// </summary>
        [Test]
        public void FindAllElements_WithNoMatches_ReturnsEmptyList()
        {
            // Act
            var results = Targeting.FindAllElements(_testHetRoot, "name=NonExistent");

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results.Count, Is.EqualTo(0));
        }

        /// <summary>
        /// Tests case sensitivity of element matching.
        /// </summary>
        [Test]
        public void FindElement_IsCaseSensitive()
        {
            // Act
            var result1 = Targeting.FindElement(_testHetRoot, "name=Save");
            var result2 = Targeting.FindElement(_testHetRoot, "name=save");

            // Assert
            Assert.That(result1, Is.Not.Null);
            Assert.That(result2, Is.Null); // Case sensitive, so lowercase should not match
        }

        /// <summary>
        /// Tests that targeting works with deeply nested elements.
        /// </summary>
        [Test]
        public void FindElement_WithDeeplyNestedElement_FindsElement()
        {
            // Act - Find a deeply nested element (Save is nested under File menu)
            var result = Targeting.FindElement(_testHetRoot, "id=SaveMenuItem");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Save"));
            Assert.That(result.AutomationId, Is.EqualTo("SaveMenuItem"));
        }
    }
}

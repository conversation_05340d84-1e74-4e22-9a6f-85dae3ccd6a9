{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"CognitiveDrive.Chariot/1.0.0": {"dependencies": {"CognitiveDrive.CoreUIA": "1.0.0", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.2428", "Microsoft.WindowsAppSDK": "1.5.240802000", "System.Text.Json": "8.0.5", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.19041.56"}, "runtime": {"CognitiveDrive.Chariot.dll": {}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.56": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.19041.38", "fileVersion": "10.0.19041.55"}, "WinRT.Runtime.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.48161"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.2428": {}, "Microsoft.WindowsAppSDK/1.5.240802000": {"dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.22621.2428"}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.26105.1006"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.2408"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.5.0.0"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.5.0.0"}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.5.0.0"}, "runtimes/win10-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"rid": "win10-arm64", "assetType": "native", "fileVersion": "1.5.0.0"}, "runtimes/win10-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"rid": "win10-x64", "assetType": "native", "fileVersion": "1.5.0.0"}, "runtimes/win10-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"rid": "win10-x86", "assetType": "native", "fileVersion": "1.5.0.0"}}}, "System.Text.Json/8.0.5": {}, "CognitiveDrive.CoreUIA/1.0.0": {"dependencies": {"System.Text.Json": "8.0.5"}, "runtime": {"CognitiveDrive.CoreUIA.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"CognitiveDrive.Chariot/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.56": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.2428": {"type": "package", "serviceable": true, "sha512": "sha512-qCNAxtpkIvn/mTXLoj5uW80qPyPvNcnTPTLgm2U+d53LS75DT6iQSpLnkVYCvxuRLx4FYPAcKackwpc0cDZGUg==", "path": "microsoft.windows.sdk.buildtools/10.0.22621.2428", "hashPath": "microsoft.windows.sdk.buildtools.10.0.22621.2428.nupkg.sha512"}, "Microsoft.WindowsAppSDK/1.5.240802000": {"type": "package", "serviceable": true, "sha512": "sha512-XW6cNzbGKDuzn+iCY1L9pNDm3pF78te8PXjKWEYk+rgePVPV/j2otr75Q5NMbWzsMF461Qq7aPJZKUQAQJNISQ==", "path": "microsoft.windowsappsdk/1.5.240802000", "hashPath": "microsoft.windowsappsdk.1.5.240802000.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "CognitiveDrive.CoreUIA/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}
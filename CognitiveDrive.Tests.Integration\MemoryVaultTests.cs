using CognitiveDrive.Core;
using CognitiveDrive.Core.Models;
using FluentAssertions;

namespace CognitiveDrive.Tests.Integration;

/// <summary>
/// THE ARCHIVIST - Unit tests for MemoryVault semantic deduplication.
/// Tests the core functionality of the agent's long-term memory system.
/// </summary>
[TestFixture]
public class MemoryVaultTests
{
    private MemoryVault? _memoryVault;
    private string _testDatabasePath = string.Empty;

    [SetUp]
    public void Setup()
    {
        // Create a unique test database for each test
        _testDatabasePath = Path.Combine(Path.GetTempPath(), $"test_memory_{Guid.NewGuid()}.db");
        _memoryVault = new MemoryVault();
        
        Console.WriteLine($"🧪 Test setup: Using database {_testDatabasePath}");
    }

    [TearDown]
    public void TearDown()
    {
        _memoryVault?.Dispose();
        
        // Clean up test database
        if (File.Exists(_testDatabasePath))
        {
            try
            {
                File.Delete(_testDatabasePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Could not delete test database: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Test 1: Verify that two identical UI states result in a "Known State" on the second call.
    /// </summary>
    [Test]
    public async Task ProcessUIStateAsync_IdenticalStates_ShouldReturnKnownStateOnSecondCall()
    {
        // Arrange
        var identicalUIState = CreateTestUIState("Button", "Click Me", "Submit button for form");

        // Act - First call should store new learning
        var firstResult = await _memoryVault!.ProcessUIStateAsync(identicalUIState);
        
        // Act - Second call with identical state should return known state
        var secondResult = await _memoryVault.ProcessUIStateAsync(identicalUIState);

        // Assert
        firstResult.Should().Be("New Learning Stored", "first identical state should be stored as new learning");
        secondResult.Should().Be("Known State (Vector Match)", "second identical state should be recognized as known");
        
        Console.WriteLine($"✅ Test 1 passed: {firstResult} → {secondResult}");
    }

    /// <summary>
    /// Test 2: Verify that two different UI states result in two "New Learning" calls.
    /// </summary>
    [Test]
    public async Task ProcessUIStateAsync_DifferentStates_ShouldReturnNewLearningForBoth()
    {
        // Arrange
        var firstUIState = CreateTestUIState("Button", "Submit", "Submit form button");
        var secondUIState = CreateTestUIState("TextBox", "Enter name", "Name input field");

        // Act
        var firstResult = await _memoryVault!.ProcessUIStateAsync(firstUIState);
        var secondResult = await _memoryVault.ProcessUIStateAsync(secondUIState);

        // Assert
        firstResult.Should().Be("New Learning Stored", "first different state should be stored as new learning");
        secondResult.Should().Be("New Learning Stored", "second different state should also be stored as new learning");
        
        Console.WriteLine($"✅ Test 2 passed: {firstResult} & {secondResult}");
    }

    /// <summary>
    /// Test 3: Verify that similar but not identical states are handled correctly.
    /// </summary>
    [Test]
    public async Task ProcessUIStateAsync_SimilarStates_ShouldHandleCorrectly()
    {
        // Arrange
        var originalState = CreateTestUIState("Button", "Click Me", "Submit button");
        var slightlyDifferentState = CreateTestUIState("Button", "Click Me", "Submit button for form"); // Added text

        // Act
        var firstResult = await _memoryVault!.ProcessUIStateAsync(originalState);
        var secondResult = await _memoryVault.ProcessUIStateAsync(slightlyDifferentState);

        // Assert
        firstResult.Should().Be("New Learning Stored", "original state should be stored as new learning");
        
        // The second result depends on similarity threshold - could be either new learning or known state
        secondResult.Should().BeOneOf("New Learning Stored", "Known State (Vector Match)", 
            "similar states should be handled based on similarity threshold");
        
        Console.WriteLine($"✅ Test 3 passed: {firstResult} → {secondResult}");
    }

    /// <summary>
    /// Test 4: Verify that empty or null states are handled gracefully.
    /// </summary>
    [Test]
    public async Task ProcessUIStateAsync_EmptyState_ShouldHandleGracefully()
    {
        // Arrange
        var emptyState = CreateEmptyUIState();

        // Act
        var result = await _memoryVault!.ProcessUIStateAsync(emptyState);

        // Assert
        result.Should().Be("Empty State (No Content)", "empty state should be handled gracefully");
        
        Console.WriteLine($"✅ Test 4 passed: {result}");
    }

    /// <summary>
    /// Test 5: Verify that complex nested UI states work correctly.
    /// </summary>
    [Test]
    public async Task ProcessUIStateAsync_ComplexNestedState_ShouldWork()
    {
        // Arrange
        var complexState = CreateComplexNestedUIState();

        // Act
        var firstResult = await _memoryVault!.ProcessUIStateAsync(complexState);
        var secondResult = await _memoryVault.ProcessUIStateAsync(complexState);

        // Assert
        firstResult.Should().Be("New Learning Stored", "complex state should be stored as new learning");
        secondResult.Should().Be("Known State (Vector Match)", "identical complex state should be recognized");
        
        Console.WriteLine($"✅ Test 5 passed: {firstResult} → {secondResult}");
    }

    /// <summary>
    /// Test 6: Performance test - verify that the system can handle multiple states efficiently.
    /// </summary>
    [Test]
    public async Task ProcessUIStateAsync_MultipleStates_ShouldPerformWell()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var states = new List<UiaElementNode>();
        
        for (int i = 0; i < 10; i++)
        {
            states.Add(CreateTestUIState($"Button{i}", $"Click {i}", $"Button number {i}"));
        }

        // Act
        var results = new List<string>();
        foreach (var state in states)
        {
            var result = await _memoryVault!.ProcessUIStateAsync(state);
            results.Add(result);
        }
        
        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(10, "should process all 10 states");
        results.Should().OnlyContain(r => r == "New Learning Stored", "all different states should be new learning");
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(10000, "should complete within 10 seconds");
        
        Console.WriteLine($"✅ Test 6 passed: Processed {states.Count} states in {stopwatch.ElapsedMilliseconds}ms");
    }

    /// <summary>
    /// Creates a test UI state with specified properties.
    /// </summary>
    private UiaElementNode CreateTestUIState(string controlType, string name, string helpText)
    {
        return new UiaElementNode
        {
            ControlType = controlType,
            Name = name,
            HelpText = helpText,
            AutomationId = $"test_{Guid.NewGuid()}",
            IsEnabled = true,
            ProcessId = 1234,
            BoundingRectangle = new ElementBounds { X = 10, Y = 20, Width = 100, Height = 30 },
            Children = new List<UiaElementNode>()
        };
    }

    /// <summary>
    /// Creates an empty UI state for testing edge cases.
    /// </summary>
    private UiaElementNode CreateEmptyUIState()
    {
        return new UiaElementNode
        {
            ControlType = string.Empty,
            Name = string.Empty,
            HelpText = string.Empty,
            AutomationId = string.Empty,
            IsEnabled = false,
            ProcessId = 0,
            BoundingRectangle = new ElementBounds(),
            Children = new List<UiaElementNode>()
        };
    }

    /// <summary>
    /// Creates a complex nested UI state for testing hierarchical structures.
    /// </summary>
    private UiaElementNode CreateComplexNestedUIState()
    {
        var root = new UiaElementNode
        {
            ControlType = "ControlType.Window",
            Name = "Test Application",
            HelpText = "Main application window",
            AutomationId = "MainWindow",
            IsEnabled = true,
            ProcessId = 1234,
            BoundingRectangle = new ElementBounds { X = 0, Y = 0, Width = 800, Height = 600 },
            Children = new List<UiaElementNode>
            {
                new UiaElementNode
                {
                    ControlType = "ControlType.Button",
                    Name = "Submit",
                    HelpText = "Submit the form",
                    AutomationId = "SubmitButton",
                    IsEnabled = true,
                    ProcessId = 1234,
                    BoundingRectangle = new ElementBounds { X = 100, Y = 500, Width = 80, Height = 30 },
                    Children = new List<UiaElementNode>()
                },
                new UiaElementNode
                {
                    ControlType = "ControlType.Edit",
                    Name = "Name Field",
                    HelpText = "Enter your name",
                    AutomationId = "NameEdit",
                    Value = "John Doe",
                    IsEnabled = true,
                    ProcessId = 1234,
                    BoundingRectangle = new ElementBounds { X = 100, Y = 100, Width = 200, Height = 25 },
                    Children = new List<UiaElementNode>()
                }
            }
        };

        return root;
    }
}

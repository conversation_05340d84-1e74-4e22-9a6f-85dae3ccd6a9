{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ConcurrentStressTest\\ConcurrentStressTest.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj", "projectName": "CognitiveDrive.Core", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj", "projectName": "CognitiveDrive.Infrastructure", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ConcurrentStressTest\\ConcurrentStressTest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ConcurrentStressTest\\ConcurrentStressTest.csproj", "projectName": "ConcurrentStressTest", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ConcurrentStressTest\\ConcurrentStressTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\ConcurrentStressTest\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Core\\CognitiveDrive.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Infrastructure\\CognitiveDrive.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}
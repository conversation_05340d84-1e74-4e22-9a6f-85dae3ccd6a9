using System;
using System.IO.Pipes;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Test program to simulate broken pipe scenarios and analyze daemon resilience.
/// Tests Named Pipe server behavior when clients terminate unexpectedly.
/// </summary>
class BrokenPipeTest
{
    private static readonly string _pipeName = "CognitiveDrivePipe";
    private static volatile int _connectionCount = 0;
    private static volatile int _brokenPipeCount = 0;

    static async Task Main(string[] args)
    {
        Console.WriteLine("=== CognitiveDrive Broken Pipe Test ===");
        Console.WriteLine("This test simulates clients that connect and terminate unexpectedly");
        Console.WriteLine("to analyze Named Pipe server resilience and resource leak prevention.");
        Console.WriteLine();

        try
        {
            Console.WriteLine("Starting broken pipe simulation...");
            Console.WriteLine("Creating multiple clients that will terminate unexpectedly...");
            Console.WriteLine("Press any key to stop the test.");
            Console.WriteLine();

            // Start multiple broken client tasks
            var clientTasks = new Task[10];
            for (int i = 0; i < clientTasks.Length; i++)
            {
                int clientId = i;
                clientTasks[i] = Task.Run(() => SimulateBrokenClient(clientId));
            }

            // Monitor daemon behavior
            var monitorTask = Task.Run(MonitorDaemonBehavior);

            // Wait for user input to stop
            Console.ReadKey();

            // Wait for all tasks to complete
            await Task.WhenAll(clientTasks);
            await monitorTask;

            Console.WriteLine();
            Console.WriteLine("=== Broken Pipe Test Results ===");
            Console.WriteLine($"Total connections attempted: {_connectionCount}");
            Console.WriteLine($"Broken pipes created: {_brokenPipeCount}");
            Console.WriteLine();
            Console.WriteLine("Check daemon log for:");
            Console.WriteLine("- Broken pipe handling");
            Console.WriteLine("- Resource cleanup");
            Console.WriteLine("- New connection acceptance");
            Console.WriteLine("- Memory leaks");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    static async Task SimulateBrokenClient(int clientId)
    {
        var random = new Random(clientId);
        
        for (int attempt = 0; attempt < 5; attempt++)
        {
            try
            {
                Console.WriteLine($"[Client {clientId}] Attempting connection #{attempt + 1}...");
                
                using (var pipeClient = new NamedPipeClientStream(".", _pipeName, PipeDirection.In))
                {
                    // Connect to the daemon
                    await pipeClient.ConnectAsync(5000); // 5 second timeout
                    Interlocked.Increment(ref _connectionCount);
                    
                    Console.WriteLine($"✅ [Client {clientId}] Connected successfully!");
                    
                    // Start reading data
                    var buffer = new byte[1024];
                    var readTask = pipeClient.ReadAsync(buffer, 0, buffer.Length);
                    
                    // Wait a random short time, then terminate unexpectedly
                    var waitTime = random.Next(100, 1000); // 100ms to 1s
                    await Task.Delay(waitTime);
                    
                    Console.WriteLine($"💥 [Client {clientId}] Terminating unexpectedly after {waitTime}ms!");
                    
                    // Simulate unexpected termination by just disposing without clean shutdown
                    // This should create a broken pipe scenario
                    Interlocked.Increment(ref _brokenPipeCount);
                    
                    // The using statement will dispose the pipe client abruptly
                }
                
                // Wait before next attempt
                await Task.Delay(random.Next(500, 2000));
            }
            catch (TimeoutException)
            {
                Console.WriteLine($"⏰ [Client {clientId}] Connection timeout - daemon may be overloaded");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ [Client {clientId}] Connection error: {ex.Message}");
                await Task.Delay(1000); // Back off on error
            }
        }
        
        Console.WriteLine($"[Client {clientId}] Completed all connection attempts");
    }

    static async Task MonitorDaemonBehavior()
    {
        var lastConnectionCount = 0;
        var lastBrokenPipeCount = 0;

        while (true)
        {
            await Task.Delay(2000); // Check every 2 seconds

            var currentConnections = _connectionCount;
            var currentBrokenPipes = _brokenPipeCount;

            if (currentConnections != lastConnectionCount || currentBrokenPipes != lastBrokenPipeCount)
            {
                Console.WriteLine($"[Monitor] Connections: {currentConnections}, Broken Pipes: {currentBrokenPipes}");
                
                // Test if daemon can still accept new connections
                try
                {
                    using (var testClient = new NamedPipeClientStream(".", _pipeName, PipeDirection.In))
                    {
                        await testClient.ConnectAsync(1000); // 1 second timeout
                        Console.WriteLine($"✅ [Monitor] Daemon still accepting new connections");
                    }
                }
                catch (TimeoutException)
                {
                    Console.WriteLine($"⚠️  [Monitor] Daemon not accepting new connections - possible hang");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️  [Monitor] Connection test failed: {ex.Message}");
                }

                lastConnectionCount = currentConnections;
                lastBrokenPipeCount = currentBrokenPipes;
            }
        }
    }
}

﻿using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Automation;
using CognitiveDrive.CoreUIA;

/// <summary>
/// Test program to demonstrate race conditions in BuildHET process.
/// Simulates rapid UI changes while BuildHET is executing.
/// </summary>
class RaceConditionTest
{
    private static volatile bool _stopTest = false;
    private static int _buildHETCallCount = 0;
    private static int _interruptionCount = 0;
    private static readonly object _lockObject = new object();

    static async Task Main(string[] args)
    {
        Console.WriteLine("=== CognitiveDrive Race Condition Test ===");
        Console.WriteLine("This test simulates rapid UI events during BuildHET execution");
        Console.WriteLine("to identify potential race conditions and data integrity issues.");
        Console.WriteLine();

        try
        {
            // Find a target window (VS Code)
            Console.WriteLine("Finding target window (VS Code)...");
            AutomationElement targetWindow = null;

            try
            {
                targetWindow = UiaScanner.FindWindowByProcessName("Code");
                Console.WriteLine($"✅ Found VS Code window: {targetWindow.Current.Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Could not find VS Code: {ex.Message}");
                Console.WriteLine("Please ensure VS Code is running and try again.");
                return;
            }

            Console.WriteLine();
            Console.WriteLine("Starting race condition test...");
            Console.WriteLine("Press any key to stop the test.");
            Console.WriteLine();

            // Start the race condition test
            var testTask = RunRaceConditionTest(targetWindow);

            // Wait for user input to stop
            Console.ReadKey();
            _stopTest = true;

            await testTask;

            Console.WriteLine();
            Console.WriteLine("=== Test Results ===");
            Console.WriteLine($"Total BuildHET calls: {_buildHETCallCount}");
            Console.WriteLine($"Detected interruptions: {_interruptionCount}");
            Console.WriteLine($"Race condition rate: {(_interruptionCount * 100.0 / _buildHETCallCount):F2}%");

            if (_interruptionCount > 0)
            {
                Console.WriteLine("⚠️  RACE CONDITIONS DETECTED - Data integrity at risk!");
            }
            else
            {
                Console.WriteLine("✅ No race conditions detected in this test run.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    static async Task RunRaceConditionTest(AutomationElement targetWindow)
    {
        // Create multiple concurrent tasks that simulate rapid BuildHET calls
        var tasks = new Task[10];

        for (int i = 0; i < tasks.Length; i++)
        {
            int taskId = i;
            tasks[i] = Task.Run(() => SimulateBuildHETCalls(targetWindow, taskId));
        }

        // Wait for all tasks to complete
        await Task.WhenAll(tasks);
    }

    static void SimulateBuildHETCalls(AutomationElement targetWindow, int taskId)
    {
        var random = new Random(taskId);

        while (!_stopTest)
        {
            try
            {
                // Simulate the same pattern as the daemon's UpdateLiveHET
                var startTime = DateTime.UtcNow;

                // Increment call count
                Interlocked.Increment(ref _buildHETCallCount);

                Console.WriteLine($"[Task {taskId}] Starting BuildHET call #{_buildHETCallCount}...");

                // Call BuildHET (this is where race conditions can occur)
                var het = UiaScanner.BuildHET(targetWindow);

                var duration = DateTime.UtcNow - startTime;

                Console.WriteLine($"[Task {taskId}] BuildHET completed in {duration.TotalMilliseconds:F0}ms - {het.TotalDescendantCount + 1} elements");

                // Check for potential race condition indicators
                if (het.TotalDescendantCount == 0)
                {
                    Interlocked.Increment(ref _interruptionCount);
                    Console.WriteLine($"⚠️  [Task {taskId}] POTENTIAL RACE CONDITION: Empty HET detected!");
                }

                // Check for error nodes that might indicate interruption
                if (ContainsErrorNodes(het))
                {
                    Interlocked.Increment(ref _interruptionCount);
                    Console.WriteLine($"⚠️  [Task {taskId}] POTENTIAL RACE CONDITION: Error nodes detected!");
                }

                // Random delay to simulate real-world timing
                Thread.Sleep(random.Next(10, 100));
            }
            catch (ElementNotAvailableException)
            {
                Interlocked.Increment(ref _interruptionCount);
                Console.WriteLine($"⚠️  [Task {taskId}] RACE CONDITION: ElementNotAvailableException during BuildHET");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ [Task {taskId}] BuildHET error: {ex.Message}");
            }
        }
    }

    static bool ContainsErrorNodes(UiaElementNode node)
    {
        // Check if this node is an error node
        if (node.Name.Contains("<Error") || node.AutomationId.Contains("<Error"))
        {
            return true;
        }

        // Recursively check children
        foreach (var child in node.Children)
        {
            if (ContainsErrorNodes(child))
            {
                return true;
            }
        }

        return false;
    }
}

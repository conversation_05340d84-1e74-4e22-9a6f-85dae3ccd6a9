using System.Diagnostics;

namespace CognitiveDrive.Core.Models;

/// <summary>
/// Represents the result of a UI scanning operation with comprehensive error handling and performance metrics.
/// This is a production-grade result wrapper that provides detailed information about operation success/failure.
/// </summary>
/// <typeparam name="T">The type of the result value</typeparam>
public sealed class ScanResult<T>
{
    /// <summary>
    /// Gets a value indicating whether the operation completed successfully.
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// Gets the result value if the operation was successful, otherwise null.
    /// </summary>
    public T? Value { get; }

    /// <summary>
    /// Gets the error message if the operation failed, otherwise null.
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// Gets the exception that caused the failure, if any.
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// Gets the duration of the operation.
    /// </summary>
    public TimeSpan Duration { get; }

    /// <summary>
    /// Gets additional metadata about the operation.
    /// </summary>
    public IReadOnlyDictionary<string, object> Metadata { get; }

    /// <summary>
    /// Gets a value indicating whether the operation failed.
    /// </summary>
    public bool IsFailure => !IsSuccess;

    private ScanResult(bool isSuccess, T? value, string? errorMessage, Exception? exception, TimeSpan duration, IReadOnlyDictionary<string, object>? metadata = null)
    {
        IsSuccess = isSuccess;
        Value = value;
        ErrorMessage = errorMessage;
        Exception = exception;
        Duration = duration;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a successful result with the specified value.
    /// </summary>
    /// <param name="value">The result value</param>
    /// <param name="duration">The operation duration</param>
    /// <param name="metadata">Optional metadata</param>
    /// <returns>A successful ScanResult</returns>
    public static ScanResult<T> Success(T value, TimeSpan duration, IReadOnlyDictionary<string, object>? metadata = null)
    {
        return new ScanResult<T>(true, value, null, null, duration, metadata);
    }

    /// <summary>
    /// Creates a failed result with the specified error message.
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <param name="duration">The operation duration</param>
    /// <param name="exception">The optional exception</param>
    /// <param name="metadata">Optional metadata</param>
    /// <returns>A failed ScanResult</returns>
    public static ScanResult<T> Failure(string errorMessage, TimeSpan duration, Exception? exception = null, IReadOnlyDictionary<string, object>? metadata = null)
    {
        return new ScanResult<T>(false, default, errorMessage, exception, duration, metadata);
    }

    /// <summary>
    /// Creates a failed result from an exception.
    /// </summary>
    /// <param name="exception">The exception that caused the failure</param>
    /// <param name="duration">The operation duration</param>
    /// <param name="metadata">Optional metadata</param>
    /// <returns>A failed ScanResult</returns>
    public static ScanResult<T> FromException(Exception exception, TimeSpan duration, IReadOnlyDictionary<string, object>? metadata = null)
    {
        return new ScanResult<T>(false, default, exception.Message, exception, duration, metadata);
    }

    /// <summary>
    /// Executes a function and wraps the result in a ScanResult with timing information.
    /// </summary>
    /// <param name="operation">The operation to execute</param>
    /// <param name="metadata">Optional metadata</param>
    /// <returns>A ScanResult containing the operation result</returns>
    public static ScanResult<T> Execute(Func<T> operation, IReadOnlyDictionary<string, object>? metadata = null)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = operation();
            stopwatch.Stop();
            return Success(result, stopwatch.Elapsed, metadata);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return FromException(ex, stopwatch.Elapsed, metadata);
        }
    }

    /// <summary>
    /// Executes an async function and wraps the result in a ScanResult with timing information.
    /// </summary>
    /// <param name="operation">The async operation to execute</param>
    /// <param name="metadata">Optional metadata</param>
    /// <returns>A ScanResult containing the operation result</returns>
    public static async Task<ScanResult<T>> ExecuteAsync(Func<Task<T>> operation, IReadOnlyDictionary<string, object>? metadata = null)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await operation();
            stopwatch.Stop();
            return Success(result, stopwatch.Elapsed, metadata);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return FromException(ex, stopwatch.Elapsed, metadata);
        }
    }

    /// <summary>
    /// Maps the value of this result to a new type if successful.
    /// </summary>
    /// <typeparam name="TNew">The new result type</typeparam>
    /// <param name="mapper">The mapping function</param>
    /// <returns>A new ScanResult with the mapped value</returns>
    public ScanResult<TNew> Map<TNew>(Func<T, TNew> mapper)
    {
        if (IsFailure)
        {
            return ScanResult<TNew>.Failure(ErrorMessage!, Duration, Exception, Metadata);
        }

        try
        {
            var mappedValue = mapper(Value!);
            return ScanResult<TNew>.Success(mappedValue, Duration, Metadata);
        }
        catch (Exception ex)
        {
            return ScanResult<TNew>.FromException(ex, Duration, Metadata);
        }
    }

    /// <summary>
    /// Returns a string representation of this result for debugging purposes.
    /// </summary>
    public override string ToString()
    {
        if (IsSuccess)
        {
            return $"Success: {Value} (Duration: {Duration.TotalMilliseconds:F2}ms)";
        }
        else
        {
            return $"Failure: {ErrorMessage} (Duration: {Duration.TotalMilliseconds:F2}ms)";
        }
    }
}

﻿using System.Diagnostics;
using System.Text.Json;
using CognitiveDrive.Core.Models;
using CognitiveDrive.Infrastructure;
using CognitiveDrive.Infrastructure.Interfaces;

/// <summary>
/// THE CRUCIBLE - Suite 4: The Deep Introspection Test (The "Dragon" Revisited)
/// This test pushes the perception engine to its absolute limit against a pathologically complex target.
/// </summary>
class DragonTest
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🐉 THE CRUCIBLE - SUITE 4: THE DEEP INTROSPECTION TEST");
        Console.WriteLine("======================================================");
        Console.WriteLine("The Dragon Revisited - Testing against maximum complexity...\n");

        // Launch Brave browser for the ultimate test
        Console.WriteLine("🚀 Launching Brave browser...");
        Process? braveProcess = null;

        try
        {
            // Try multiple possible Brave locations
            var bravePaths = new[]
            {
                @"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe",
                @"C:\Program Files (x86)\BraveSoftware\Brave-Browser\Application\brave.exe",
                "brave.exe"
            };

            foreach (var path in bravePaths)
            {
                try
                {
                    braveProcess = Process.Start(new ProcessStartInfo
                    {
                        FileName = path,
                        Arguments = "https://news.ycombinator.com", // Complex, dynamic content
                        UseShellExecute = true
                    });

                    if (braveProcess != null)
                    {
                        Console.WriteLine($"✅ Brave launched successfully from: {path}");
                        break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ Failed to launch from {path}: {ex.Message}");
                }
            }

            if (braveProcess == null)
            {
                Console.WriteLine("❌ Could not launch Brave browser. Testing with existing browser...");

                // Find any existing browser process
                var browserProcesses = Process.GetProcesses()
                    .Where(p => p.ProcessName.Contains("brave", StringComparison.OrdinalIgnoreCase) ||
                               p.ProcessName.Contains("chrome", StringComparison.OrdinalIgnoreCase) ||
                               p.ProcessName.Contains("msedge", StringComparison.OrdinalIgnoreCase))
                    .Where(p => p.MainWindowHandle != IntPtr.Zero)
                    .FirstOrDefault();

                if (browserProcesses != null)
                {
                    braveProcess = browserProcesses;
                    Console.WriteLine($"✅ Using existing browser: {braveProcess.ProcessName} (PID: {braveProcess.Id})");
                }
                else
                {
                    Console.WriteLine("❌ No suitable browser found. Cannot proceed with Dragon Test.");
                    return;
                }
            }
            else
            {
                // Wait for Brave to fully load
                Console.WriteLine("⏳ Waiting for Brave to fully load complex content...");
                await Task.Delay(10000); // 10 seconds for page to load
            }

            // Initialize the production scanner with maximum settings
            var configuration = new ScannerConfiguration
            {
                MaxScanDepth = 50,        // Maximum depth for the dragon
                MaxElementsPerScan = 5000, // Allow large element counts
                PropertyExtractionTimeout = TimeSpan.FromSeconds(3),
                ChildEnumerationTimeout = TimeSpan.FromSeconds(15),
                RetryCount = 3,
                RetryDelay = TimeSpan.FromMilliseconds(200)
            };

            var propertyExtractor = new WindowsUiaPropertyExtractor(configuration);
            var processManager = new WindowsProcessManager();
            var scanner = new WindowsUiaScanner(propertyExtractor, processManager, configuration);

            Console.WriteLine("🐉 INITIATING DRAGON SCAN - MAXIMUM COMPLEXITY TEST");
            Console.WriteLine($"Target: {braveProcess.ProcessName} (PID: {braveProcess.Id})");
            Console.WriteLine($"Max Depth: {configuration.MaxScanDepth}");
            Console.WriteLine($"Max Elements: {configuration.MaxElementsPerScan}");

            // Record system state before the dragon battle
            var initialMemory = GC.GetTotalMemory(false);
            var dragonStopwatch = Stopwatch.StartNew();

            // FACE THE DRAGON
            var scanResult = await scanner.ScanProcessAsync(braveProcess.Id);
            dragonStopwatch.Stop();

            // Analyze the battle results
            var finalMemory = GC.GetTotalMemory(true);

            Console.WriteLine($"\n🐉 DRAGON BATTLE RESULTS:");
            Console.WriteLine($"   - Scan Success: {(scanResult.IsSuccess ? "✅ VICTORY" : "❌ DEFEAT")}");
            Console.WriteLine($"   - Battle Duration: {dragonStopwatch.Elapsed.TotalSeconds:F2} seconds");
            Console.WriteLine($"   - Memory Usage: {initialMemory / 1024 / 1024:F2} MB → {finalMemory / 1024 / 1024:F2} MB");
            Console.WriteLine($"   - Memory Delta: {(finalMemory - initialMemory) / 1024 / 1024:F2} MB");

            if (scanResult.IsSuccess)
            {
                var rootElement = scanResult.Value!;
                Console.WriteLine($"   - Total Elements Conquered: {rootElement.TotalDescendantCount + 1}");
                Console.WriteLine($"   - Root Element: {rootElement.Name}");
                Console.WriteLine($"   - Window Title: {rootElement.Name}");

                // Test JSON serialization (the old nemesis)
                Console.WriteLine($"\n🧪 Testing JSON Serialization (Previous Failure Point):");
                try
                {
                    var jsonOptions = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        WriteIndented = false
                    };

                    var serializationStopwatch = Stopwatch.StartNew();
                    var jsonString = JsonSerializer.Serialize(rootElement, jsonOptions);
                    serializationStopwatch.Stop();

                    Console.WriteLine($"   - JSON Serialization: ✅ SUCCESS");
                    Console.WriteLine($"   - JSON Size: {jsonString.Length / 1024 / 1024:F2} MB");
                    Console.WriteLine($"   - Serialization Time: {serializationStopwatch.Elapsed.TotalMilliseconds:F2}ms");

                    // Test deserialization
                    var deserializationStopwatch = Stopwatch.StartNew();
                    var deserializedElement = JsonSerializer.Deserialize<UiaElementNode>(jsonString, jsonOptions);
                    deserializationStopwatch.Stop();

                    if (deserializedElement != null)
                    {
                        Console.WriteLine($"   - JSON Deserialization: ✅ SUCCESS");
                        Console.WriteLine($"   - Deserialization Time: {deserializationStopwatch.Elapsed.TotalMilliseconds:F2}ms");
                        Console.WriteLine($"   - Element Count Match: {(deserializedElement.TotalDescendantCount == rootElement.TotalDescendantCount ? "✅" : "❌")}");
                    }
                    else
                    {
                        Console.WriteLine($"   - JSON Deserialization: ❌ FAILED");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   - JSON Serialization: ❌ FAILED - {ex.Message}");
                }

                // Analyze element complexity
                var actionableElements = rootElement.FindDescendants(e => e.IsActionable);
                var elementsWithIds = rootElement.FindDescendants(e => !string.IsNullOrEmpty(e.AutomationId));
                var elementsWithNames = rootElement.FindDescendants(e => !string.IsNullOrEmpty(e.Name));
                var elementsWithBounds = rootElement.FindDescendants(e => e.HasPhysicalLocation);

                Console.WriteLine($"\n📊 ELEMENT ANALYSIS:");
                Console.WriteLine($"   - Actionable Elements: {actionableElements.Count}");
                Console.WriteLine($"   - Elements with AutomationId: {elementsWithIds.Count}");
                Console.WriteLine($"   - Elements with Name: {elementsWithNames.Count}");
                Console.WriteLine($"   - Elements with Physical Location: {elementsWithBounds.Count}");

                // Test targeting on the complex tree
                Console.WriteLine($"\n🎯 TARGETING TEST ON COMPLEX TREE:");
                var targetingStopwatch = Stopwatch.StartNew();

                var query = TargetingQuery.Builder()
                    .WithControlType("ControlType.Button")
                    .WithMaxDepth(20)
                    .Build();

                var buttons = rootElement.FindDescendants(e => query.Matches(e));
                targetingStopwatch.Stop();

                Console.WriteLine($"   - Button Search: Found {buttons.Count} buttons in {targetingStopwatch.Elapsed.TotalMilliseconds:F2}ms");

                // Final Dragon Test Verdict
                Console.WriteLine($"\n🐉 DRAGON TEST FINAL VERDICT:");

                var elementCount = rootElement.TotalDescendantCount + 1;
                var scanTime = dragonStopwatch.Elapsed.TotalSeconds;
                var memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024;

                if (elementCount >= 100 && scanTime < 60 && memoryIncrease < 200)
                {
                    Console.WriteLine("🏆 DRAGON SLAIN! The new architecture has conquered the ultimate test!");
                    Console.WriteLine($"   - Complex UI tree successfully scanned ({elementCount} elements)");
                    Console.WriteLine($"   - Performance remained excellent ({scanTime:F2}s)");
                    Console.WriteLine($"   - Memory usage stayed reasonable ({memoryIncrease:F2}MB increase)");
                    Console.WriteLine($"   - JSON serialization works flawlessly");
                    Console.WriteLine($"   - No stack overflow or recursion issues");
                    Console.WriteLine($"   - Targeting system performs well on complex trees");
                    Console.WriteLine("\n✅ THE BLACKSMITH'S HARDENING WAS SUCCESSFUL!");
                }
                else
                {
                    Console.WriteLine("⚠️ DRAGON WOUNDED BUT NOT SLAIN");
                    if (elementCount < 100) Console.WriteLine("   - Element count lower than expected for complex app");
                    if (scanTime >= 60) Console.WriteLine("   - Scan time exceeded acceptable limits");
                    if (memoryIncrease >= 200) Console.WriteLine("   - Memory usage too high");
                }
            }
            else
            {
                Console.WriteLine($"❌ DRAGON VICTORIOUS - Scan failed: {scanResult.ErrorMessage}");
                Console.WriteLine($"   - Error Duration: {scanResult.Duration.TotalMilliseconds:F2}ms");

                if (scanResult.Exception != null)
                {
                    Console.WriteLine($"   - Exception Type: {scanResult.Exception.GetType().Name}");
                    Console.WriteLine($"   - Stack Trace Available: {!string.IsNullOrEmpty(scanResult.Exception.StackTrace)}");
                }
            }

            scanner.Dispose();
        }
        finally
        {
            // Clean up - but don't kill user's browser if we didn't launch it
            if (braveProcess != null && args.Contains("--cleanup"))
            {
                try
                {
                    if (!braveProcess.HasExited)
                    {
                        braveProcess.CloseMainWindow();
                        await Task.Delay(2000);
                        if (!braveProcess.HasExited)
                        {
                            braveProcess.Kill();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ Cleanup warning: {ex.Message}");
                }
            }
        }

        Console.WriteLine($"\n🎯 SUITE 4 COMPLETE: THE DRAGON HAS BEEN FACED!");
    }
}

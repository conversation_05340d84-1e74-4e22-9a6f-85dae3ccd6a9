namespace CognitiveDrive.Core.Models;

/// <summary>
/// Represents a strongly-typed query for finding UI elements.
/// Provides a robust, validated approach to element targeting with multiple search criteria.
/// </summary>
public sealed class TargetingQuery
{
    /// <summary>
    /// Gets or sets the automation ID to search for.
    /// </summary>
    public string? AutomationId { get; init; }

    /// <summary>
    /// Gets or sets the element name to search for.
    /// </summary>
    public string? Name { get; init; }

    /// <summary>
    /// Gets or sets the control type to search for.
    /// </summary>
    public string? ControlType { get; init; }

    /// <summary>
    /// Gets or sets the bounding rectangle to search within.
    /// </summary>
    public ElementBounds? BoundingRectangle { get; init; }

    /// <summary>
    /// Gets or sets the maximum depth to search in the element tree.
    /// </summary>
    public int MaxDepth { get; init; } = 50;

    /// <summary>
    /// Gets or sets the timeout for the search operation.
    /// </summary>
    public TimeSpan Timeout { get; init; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// Gets or sets a value indicating whether the search should be case-sensitive.
    /// </summary>
    public bool CaseSensitive { get; init; } = false;

    /// <summary>
    /// Gets or sets a value indicating whether to use partial matching for text properties.
    /// </summary>
    public bool PartialMatch { get; init; } = false;

    /// <summary>
    /// Gets or sets a value indicating whether to include disabled elements in the search.
    /// </summary>
    public bool IncludeDisabled { get; init; } = false;

    /// <summary>
    /// Gets or sets a value indicating whether to include offscreen elements in the search.
    /// </summary>
    public bool IncludeOffscreen { get; init; } = false;

    /// <summary>
    /// Gets or sets additional custom properties to match.
    /// </summary>
    public Dictionary<string, object> CustomProperties { get; init; } = new();

    /// <summary>
    /// Gets a value indicating whether this query has any search criteria.
    /// </summary>
    public bool HasCriteria => 
        !string.IsNullOrWhiteSpace(AutomationId) ||
        !string.IsNullOrWhiteSpace(Name) ||
        !string.IsNullOrWhiteSpace(ControlType) ||
        BoundingRectangle != null ||
        CustomProperties.Count > 0;

    /// <summary>
    /// Validates the query for correctness and completeness.
    /// </summary>
    /// <returns>A list of validation errors, empty if valid</returns>
    public List<string> Validate()
    {
        var errors = new List<string>();

        if (!HasCriteria)
        {
            errors.Add("Query must have at least one search criterion");
        }

        if (MaxDepth <= 0)
        {
            errors.Add("MaxDepth must be greater than zero");
        }

        if (MaxDepth > 1000)
        {
            errors.Add("MaxDepth cannot exceed 1000 to prevent performance issues");
        }

        if (Timeout <= TimeSpan.Zero)
        {
            errors.Add("Timeout must be greater than zero");
        }

        if (Timeout > TimeSpan.FromMinutes(5))
        {
            errors.Add("Timeout cannot exceed 5 minutes");
        }

        return errors;
    }

    /// <summary>
    /// Creates a query builder for fluent query construction.
    /// </summary>
    /// <returns>A new TargetingQueryBuilder</returns>
    public static TargetingQueryBuilder Builder() => new();

    /// <summary>
    /// Creates a simple query by automation ID.
    /// </summary>
    /// <param name="automationId">The automation ID to search for</param>
    /// <returns>A targeting query</returns>
    public static TargetingQuery ByAutomationId(string automationId)
    {
        return new TargetingQuery { AutomationId = automationId };
    }

    /// <summary>
    /// Creates a simple query by element name.
    /// </summary>
    /// <param name="name">The name to search for</param>
    /// <returns>A targeting query</returns>
    public static TargetingQuery ByName(string name)
    {
        return new TargetingQuery { Name = name };
    }

    /// <summary>
    /// Creates a simple query by control type.
    /// </summary>
    /// <param name="controlType">The control type to search for</param>
    /// <returns>A targeting query</returns>
    public static TargetingQuery ByControlType(string controlType)
    {
        return new TargetingQuery { ControlType = controlType };
    }

    /// <summary>
    /// Determines whether the specified element matches this query.
    /// </summary>
    /// <param name="element">The element to test</param>
    /// <returns>True if the element matches, false otherwise</returns>
    public bool Matches(UiaElementNode element)
    {
        // Check automation ID
        if (!string.IsNullOrWhiteSpace(AutomationId))
        {
            if (!StringMatches(element.AutomationId, AutomationId))
                return false;
        }

        // Check name
        if (!string.IsNullOrWhiteSpace(Name))
        {
            if (!StringMatches(element.Name, Name))
                return false;
        }

        // Check control type
        if (!string.IsNullOrWhiteSpace(ControlType))
        {
            if (!StringMatches(element.ControlType, ControlType))
                return false;
        }

        // Check enabled state
        if (!IncludeDisabled && !element.IsEnabled)
            return false;

        // Check offscreen state
        if (!IncludeOffscreen && element.IsOffscreen)
            return false;

        // Check bounding rectangle
        if (BoundingRectangle != null && element.BoundingRectangle != null)
        {
            if (!IsWithinBounds(element.BoundingRectangle, BoundingRectangle))
                return false;
        }

        // Check custom properties
        foreach (var (key, expectedValue) in CustomProperties)
        {
            if (!element.Properties.TryGetValue(key, out var actualValue) || 
                !Equals(actualValue, expectedValue))
                return false;
        }

        return true;
    }

    private bool StringMatches(string actual, string expected)
    {
        if (PartialMatch)
        {
            return actual.Contains(expected, CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase);
        }
        else
        {
            return string.Equals(actual, expected, CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase);
        }
    }

    private static bool IsWithinBounds(ElementBounds actual, ElementBounds expected)
    {
        return actual.X >= expected.X &&
               actual.Y >= expected.Y &&
               actual.X + actual.Width <= expected.X + expected.Width &&
               actual.Y + actual.Height <= expected.Y + expected.Height;
    }

    public override string ToString()
    {
        var criteria = new List<string>();

        if (!string.IsNullOrWhiteSpace(AutomationId))
            criteria.Add($"AutomationId='{AutomationId}'");

        if (!string.IsNullOrWhiteSpace(Name))
            criteria.Add($"Name='{Name}'");

        if (!string.IsNullOrWhiteSpace(ControlType))
            criteria.Add($"ControlType='{ControlType}'");

        if (BoundingRectangle != null)
            criteria.Add($"Bounds={BoundingRectangle}");

        return $"TargetingQuery({string.Join(", ", criteria)})";
    }
}

/// <summary>
/// Fluent builder for creating targeting queries.
/// </summary>
public sealed class TargetingQueryBuilder
{
    private string? _automationId;
    private string? _name;
    private string? _controlType;
    private ElementBounds? _boundingRectangle;
    private int _maxDepth = 50;
    private TimeSpan _timeout = TimeSpan.FromSeconds(5);
    private bool _caseSensitive = false;
    private bool _partialMatch = false;
    private bool _includeDisabled = false;
    private bool _includeOffscreen = false;
    private readonly Dictionary<string, object> _customProperties = new();

    public TargetingQueryBuilder WithAutomationId(string automationId)
    {
        _automationId = automationId;
        return this;
    }

    public TargetingQueryBuilder WithName(string name)
    {
        _name = name;
        return this;
    }

    public TargetingQueryBuilder WithControlType(string controlType)
    {
        _controlType = controlType;
        return this;
    }

    public TargetingQueryBuilder WithBounds(ElementBounds bounds)
    {
        _boundingRectangle = bounds;
        return this;
    }

    public TargetingQueryBuilder WithMaxDepth(int maxDepth)
    {
        _maxDepth = maxDepth;
        return this;
    }

    public TargetingQueryBuilder WithTimeout(TimeSpan timeout)
    {
        _timeout = timeout;
        return this;
    }

    public TargetingQueryBuilder CaseSensitive(bool caseSensitive = true)
    {
        _caseSensitive = caseSensitive;
        return this;
    }

    public TargetingQueryBuilder PartialMatch(bool partialMatch = true)
    {
        _partialMatch = partialMatch;
        return this;
    }

    public TargetingQueryBuilder IncludeDisabled(bool includeDisabled = true)
    {
        _includeDisabled = includeDisabled;
        return this;
    }

    public TargetingQueryBuilder IncludeOffscreen(bool includeOffscreen = true)
    {
        _includeOffscreen = includeOffscreen;
        return this;
    }

    public TargetingQueryBuilder WithProperty(string key, object value)
    {
        _customProperties[key] = value;
        return this;
    }

    public TargetingQuery Build()
    {
        return new TargetingQuery
        {
            AutomationId = _automationId,
            Name = _name,
            ControlType = _controlType,
            BoundingRectangle = _boundingRectangle,
            MaxDepth = _maxDepth,
            Timeout = _timeout,
            CaseSensitive = _caseSensitive,
            PartialMatch = _partialMatch,
            IncludeDisabled = _includeDisabled,
            IncludeOffscreen = _includeOffscreen,
            CustomProperties = new Dictionary<string, object>(_customProperties)
        };
    }
}

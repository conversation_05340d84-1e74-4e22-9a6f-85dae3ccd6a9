{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Daemon\\CognitiveDrive.Daemon.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\CognitiveDrive.CoreUIA.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\CognitiveDrive.CoreUIA.csproj", "projectName": "CognitiveDrive.CoreUIA", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\CognitiveDrive.CoreUIA.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Daemon\\CognitiveDrive.Daemon.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Daemon\\CognitiveDrive.Daemon.csproj", "projectName": "CognitiveDrive.Daemon", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Daemon\\CognitiveDrive.Daemon.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Daemon\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\CognitiveDrive.CoreUIA.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\CognitiveDrive.CoreUIA.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"System.Configuration.ConfigurationManager": {"target": "Package", "version": "[8.0.1, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[8.0.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}
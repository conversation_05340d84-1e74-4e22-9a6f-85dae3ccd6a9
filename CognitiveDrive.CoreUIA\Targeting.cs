using System;
using System.Collections.Generic;
using System.Linq;

namespace CognitiveDrive.CoreUIA
{
    /// <summary>
    /// Provides multi-tier element targeting capabilities for reliable UI element identification.
    /// Implements cascading search logic: AutomationId → Name → fallback strategies.
    /// </summary>
    public static class Targeting
    {
        /// <summary>
        /// Finds an element within the HET using multi-tier targeting strategy.
        /// Searches with the following priority:
        /// 1. Tier 1 (ID): Exact match on AutomationId
        /// 2. Tier 2 (Name): Exact match on Name
        /// </summary>
        /// <param name="rootNode">The root node to search within</param>
        /// <param name="query">The search query in format "id=value" or "name=value"</param>
        /// <returns>The first matching UiaElementNode, or null if no match is found</returns>
        /// <exception cref="ArgumentNullException">Thrown when rootNode is null</exception>
        /// <exception cref="ArgumentException">Thrown when query is null, empty, or invalid format</exception>
        public static UiaElementNode? FindElement(UiaElementNode rootNode, string query)
        {
            if (rootNode == null)
                throw new ArgumentNullException(nameof(rootNode));

            if (string.IsNullOrWhiteSpace(query))
                throw new ArgumentException("Query cannot be null or empty", nameof(query));

            // Parse the query
            var (searchType, searchValue) = ParseQuery(query);

            // Execute the appropriate search strategy
            return searchType switch
            {
                SearchType.AutomationId => FindByAutomationId(rootNode, searchValue),
                SearchType.Name => FindByName(rootNode, searchValue),
                _ => throw new ArgumentException($"Unsupported query format: {query}", nameof(query))
            };
        }

        /// <summary>
        /// Finds all elements within the HET that match the specified query.
        /// </summary>
        /// <param name="rootNode">The root node to search within</param>
        /// <param name="query">The search query in format "id=value" or "name=value"</param>
        /// <returns>A list of all matching UiaElementNodes</returns>
        /// <exception cref="ArgumentNullException">Thrown when rootNode is null</exception>
        /// <exception cref="ArgumentException">Thrown when query is null, empty, or invalid format</exception>
        public static List<UiaElementNode> FindAllElements(UiaElementNode rootNode, string query)
        {
            if (rootNode == null)
                throw new ArgumentNullException(nameof(rootNode));

            if (string.IsNullOrWhiteSpace(query))
                throw new ArgumentException("Query cannot be null or empty", nameof(query));

            // Parse the query
            var (searchType, searchValue) = ParseQuery(query);

            // Execute the appropriate search strategy
            return searchType switch
            {
                SearchType.AutomationId => FindAllByAutomationId(rootNode, searchValue),
                SearchType.Name => FindAllByName(rootNode, searchValue),
                _ => throw new ArgumentException($"Unsupported query format: {query}", nameof(query))
            };
        }

        /// <summary>
        /// Finds an element by its AutomationId using depth-first search.
        /// </summary>
        /// <param name="node">The node to search within</param>
        /// <param name="automationId">The AutomationId to search for</param>
        /// <returns>The first matching node, or null if not found</returns>
        private static UiaElementNode? FindByAutomationId(UiaElementNode node, string automationId)
        {
            // Check current node
            if (string.Equals(node.AutomationId, automationId, StringComparison.Ordinal))
            {
                return node;
            }

            // Search children recursively
            foreach (var child in node.Children)
            {
                var result = FindByAutomationId(child, automationId);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }

        /// <summary>
        /// Finds an element by its Name using depth-first search.
        /// </summary>
        /// <param name="node">The node to search within</param>
        /// <param name="name">The Name to search for</param>
        /// <returns>The first matching node, or null if not found</returns>
        private static UiaElementNode? FindByName(UiaElementNode node, string name)
        {
            // Check current node
            if (string.Equals(node.Name, name, StringComparison.Ordinal))
            {
                return node;
            }

            // Search children recursively
            foreach (var child in node.Children)
            {
                var result = FindByName(child, name);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }

        /// <summary>
        /// Finds all elements by their AutomationId.
        /// </summary>
        /// <param name="node">The node to search within</param>
        /// <param name="automationId">The AutomationId to search for</param>
        /// <returns>A list of all matching nodes</returns>
        private static List<UiaElementNode> FindAllByAutomationId(UiaElementNode node, string automationId)
        {
            var results = new List<UiaElementNode>();

            // Check current node
            if (string.Equals(node.AutomationId, automationId, StringComparison.Ordinal))
            {
                results.Add(node);
            }

            // Search children recursively
            foreach (var child in node.Children)
            {
                results.AddRange(FindAllByAutomationId(child, automationId));
            }

            return results;
        }

        /// <summary>
        /// Finds all elements by their Name.
        /// </summary>
        /// <param name="node">The node to search within</param>
        /// <param name="name">The Name to search for</param>
        /// <returns>A list of all matching nodes</returns>
        private static List<UiaElementNode> FindAllByName(UiaElementNode node, string name)
        {
            var results = new List<UiaElementNode>();

            // Check current node
            if (string.Equals(node.Name, name, StringComparison.Ordinal))
            {
                results.Add(node);
            }

            // Search children recursively
            foreach (var child in node.Children)
            {
                results.AddRange(FindAllByName(child, name));
            }

            return results;
        }

        /// <summary>
        /// Parses a query string into search type and value.
        /// </summary>
        /// <param name="query">The query string to parse</param>
        /// <returns>A tuple containing the search type and search value</returns>
        /// <exception cref="ArgumentException">Thrown when query format is invalid</exception>
        private static (SearchType searchType, string searchValue) ParseQuery(string query)
        {
            var parts = query.Split('=', 2);
            if (parts.Length != 2)
            {
                throw new ArgumentException($"Invalid query format. Expected 'type=value', got: {query}", nameof(query));
            }

            var type = parts[0].Trim().ToLowerInvariant();
            var value = parts[1].Trim();

            if (string.IsNullOrEmpty(value))
            {
                throw new ArgumentException($"Search value cannot be empty in query: {query}", nameof(query));
            }

            return type switch
            {
                "id" => (SearchType.AutomationId, value),
                "name" => (SearchType.Name, value),
                _ => throw new ArgumentException($"Unsupported search type '{type}'. Supported types: 'id', 'name'", nameof(query))
            };
        }

        /// <summary>
        /// Enumeration of supported search types for element targeting.
        /// </summary>
        private enum SearchType
        {
            AutomationId,
            Name
        }
    }
}

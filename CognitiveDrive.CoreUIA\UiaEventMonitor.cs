using System;
using System.Windows.Automation;

namespace CognitiveDrive.CoreUIA
{
    /// <summary>
    /// Singleton class responsible for listening to global UI Automation events.
    /// Provides a decoupled interface between the UIA framework and application logic.
    /// </summary>
    public sealed class UiaEventMonitor
    {
        private static readonly object _lock = new object();
        private static UiaEventMonitor? _instance;
        private bool _isStarted = false;

        // Private event handlers for UIA framework
        private AutomationFocusChangedEventHandler? _focusChangedHandler;
        private StructureChangedEventHandler? _structureChangedHandler;

        /// <summary>
        /// Gets the singleton instance of UiaEventMonitor.
        /// </summary>
        public static UiaEventMonitor Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new UiaEventMonitor();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Event raised when the UI focus changes in any application.
        /// </summary>
        public event AutomationFocusChangedEventHandler? FocusChanged;

        /// <summary>
        /// Event raised when the UI structure changes in any application.
        /// </summary>
        public event StructureChangedEventHandler? StructureChanged;

        /// <summary>
        /// Private constructor to enforce singleton pattern.
        /// </summary>
        private UiaEventMonitor()
        {
            // Initialize the private event handlers
            _focusChangedHandler = new AutomationFocusChangedEventHandler(OnFocusChangedInternal);
            _structureChangedHandler = new StructureChangedEventHandler(OnStructureChangedInternal);
        }

        /// <summary>
        /// Starts monitoring global UI Automation events.
        /// Subscribes to system-wide focus and structure change events.
        /// </summary>
        public void Start()
        {
            if (_isStarted)
            {
                return; // Already started
            }

            try
            {
                // Subscribe to global automation focus changed events
                if (_focusChangedHandler != null)
                {
                    Automation.AddAutomationFocusChangedEventHandler(_focusChangedHandler);
                }

                // Subscribe to global automation structure changed events
                if (_structureChangedHandler != null)
                {
                    Automation.AddStructureChangedEventHandler(
                        AutomationElement.RootElement,
                        TreeScope.Subtree,
                        _structureChangedHandler);
                }

                _isStarted = true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to start UIA event monitoring: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Stops monitoring global UI Automation events.
        /// Unsubscribes from all system-wide events.
        /// </summary>
        public void Stop()
        {
            if (!_isStarted)
            {
                return; // Already stopped
            }

            try
            {
                // Unsubscribe from global automation focus changed events
                if (_focusChangedHandler != null)
                {
                    Automation.RemoveAutomationFocusChangedEventHandler(_focusChangedHandler);
                }

                // Unsubscribe from global automation structure changed events
                if (_structureChangedHandler != null)
                {
                    Automation.RemoveStructureChangedEventHandler(
                        AutomationElement.RootElement,
                        _structureChangedHandler);
                }

                _isStarted = false;
            }
            catch (Exception ex)
            {
                // Log the error but don't throw - we want to ensure cleanup continues
                System.Diagnostics.Debug.WriteLine($"Error stopping UIA event monitoring: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets a value indicating whether the event monitor is currently active.
        /// </summary>
        public bool IsStarted => _isStarted;

        /// <summary>
        /// Internal handler for UIA focus changed events.
        /// Safely invokes the public FocusChanged event.
        /// </summary>
        /// <param name="sender">The source of the event</param>
        /// <param name="e">Event arguments containing focus change information</param>
        private void OnFocusChangedInternal(object sender, AutomationFocusChangedEventArgs e)
        {
            try
            {
                // Invoke the public event, allowing subscribers to handle the focus change
                FocusChanged?.Invoke(sender, e);
            }
            catch (Exception ex)
            {
                // Prevent exceptions in event handlers from crashing the monitor
                System.Diagnostics.Debug.WriteLine($"Error in FocusChanged event handler: {ex.Message}");
            }
        }

        /// <summary>
        /// Internal handler for UIA structure changed events.
        /// Safely invokes the public StructureChanged event.
        /// </summary>
        /// <param name="sender">The source of the event</param>
        /// <param name="e">Event arguments containing structure change information</param>
        private void OnStructureChangedInternal(object sender, StructureChangedEventArgs e)
        {
            try
            {
                // Invoke the public event, allowing subscribers to handle the structure change
                StructureChanged?.Invoke(sender, e);
            }
            catch (Exception ex)
            {
                // Prevent exceptions in event handlers from crashing the monitor
                System.Diagnostics.Debug.WriteLine($"Error in StructureChanged event handler: {ex.Message}");
            }
        }

        /// <summary>
        /// Finalizer to ensure proper cleanup of UIA event subscriptions.
        /// </summary>
        ~UiaEventMonitor()
        {
            Stop();
        }
    }
}

using System;

namespace CognitiveDrive.Daemon
{
    /// <summary>
    /// Service installation helper for the CognitiveDrive Daemon Windows Service.
    /// For .NET Core/5+ services, use sc.exe or PowerShell for installation.
    /// </summary>
    public static class ServiceInstaller
    {
        /// <summary>
        /// Gets the service installation command for Windows Service Control Manager.
        /// </summary>
        /// <param name="executablePath">Path to the service executable</param>
        /// <returns>Command to install the service</returns>
        public static string GetInstallCommand(string executablePath)
        {
            return $"sc.exe create \"CognitiveDrive.Daemon\" " +
                   $"binPath=\"{executablePath}\" " +
                   $"DisplayName=\"CognitiveDrive Daemon\" " +
                   $"description=\"CognitiveDrive Daemon - Persistent UI monitoring service for AI agent perception\" " +
                   $"start=auto";
        }

        /// <summary>
        /// Gets the service uninstallation command for Windows Service Control Manager.
        /// </summary>
        /// <returns>Command to uninstall the service</returns>
        public static string GetUninstallCommand()
        {
            return "sc.exe delete \"CognitiveDrive.Daemon\"";
        }

        /// <summary>
        /// Gets the service start command.
        /// </summary>
        /// <returns>Command to start the service</returns>
        public static string GetStartCommand()
        {
            return "sc.exe start \"CognitiveDrive.Daemon\"";
        }

        /// <summary>
        /// Gets the service stop command.
        /// </summary>
        /// <returns>Command to stop the service</returns>
        public static string GetStopCommand()
        {
            return "sc.exe stop \"CognitiveDrive.Daemon\"";
        }
    }
}

{"version": 3, "targets": {"net8.0-windows7.0": {"System.CommandLine/2.0.0-beta4.22272.1": {"type": "package", "compile": {"lib/net6.0/System.CommandLine.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/System.CommandLine.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net6.0/cs/System.CommandLine.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.CommandLine.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.CommandLine.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.CommandLine.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.CommandLine.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.CommandLine.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.CommandLine.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.CommandLine.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.CommandLine.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.CommandLine.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.CommandLine.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.CommandLine.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.CommandLine.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Text.Json/8.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "CognitiveDrive.CoreUIA/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"bin/placeholder/CognitiveDrive.CoreUIA.dll": {}}, "runtime": {"bin/placeholder/CognitiveDrive.CoreUIA.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App", "Microsoft.WindowsDesktop.App.WindowsForms"]}}}, "libraries": {"System.CommandLine/2.0.0-beta4.22272.1": {"sha512": "1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg==", "type": "package", "path": "system.commandline/2.0.0-beta4.22272.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/System.CommandLine.dll", "lib/net6.0/System.CommandLine.pdb", "lib/net6.0/System.CommandLine.xml", "lib/net6.0/cs/System.CommandLine.resources.dll", "lib/net6.0/de/System.CommandLine.resources.dll", "lib/net6.0/es/System.CommandLine.resources.dll", "lib/net6.0/fr/System.CommandLine.resources.dll", "lib/net6.0/it/System.CommandLine.resources.dll", "lib/net6.0/ja/System.CommandLine.resources.dll", "lib/net6.0/ko/System.CommandLine.resources.dll", "lib/net6.0/pl/System.CommandLine.resources.dll", "lib/net6.0/pt-BR/System.CommandLine.resources.dll", "lib/net6.0/ru/System.CommandLine.resources.dll", "lib/net6.0/tr/System.CommandLine.resources.dll", "lib/net6.0/zh-Hans/System.CommandLine.resources.dll", "lib/net6.0/zh-Hant/System.CommandLine.resources.dll", "lib/netstandard2.0/System.CommandLine.dll", "lib/netstandard2.0/System.CommandLine.pdb", "lib/netstandard2.0/System.CommandLine.xml", "lib/netstandard2.0/cs/System.CommandLine.resources.dll", "lib/netstandard2.0/de/System.CommandLine.resources.dll", "lib/netstandard2.0/es/System.CommandLine.resources.dll", "lib/netstandard2.0/fr/System.CommandLine.resources.dll", "lib/netstandard2.0/it/System.CommandLine.resources.dll", "lib/netstandard2.0/ja/System.CommandLine.resources.dll", "lib/netstandard2.0/ko/System.CommandLine.resources.dll", "lib/netstandard2.0/pl/System.CommandLine.resources.dll", "lib/netstandard2.0/pt-BR/System.CommandLine.resources.dll", "lib/netstandard2.0/ru/System.CommandLine.resources.dll", "lib/netstandard2.0/tr/System.CommandLine.resources.dll", "lib/netstandard2.0/zh-Hans/System.CommandLine.resources.dll", "lib/netstandard2.0/zh-Hant/System.CommandLine.resources.dll", "system.commandline.2.0.0-beta4.22272.1.nupkg.sha512", "system.commandline.nuspec"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "CognitiveDrive.CoreUIA/1.0.0": {"type": "project", "path": "../CognitiveDrive.CoreUIA/CognitiveDrive.CoreUIA.csproj", "msbuildProject": "../CognitiveDrive.CoreUIA/CognitiveDrive.CoreUIA.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["CognitiveDrive.CoreUIA >= 1.0.0", "System.CommandLine >= 2.0.0-beta4.22272.1", "System.Text.Json >= 8.0.5"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Inspector\\CognitiveDrive.Inspector.csproj", "projectName": "CognitiveDrive.Inspector", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Inspector\\CognitiveDrive.Inspector.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.Inspector\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\CognitiveDrive.CoreUIA.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Project\\CognitiveDrive.CoreUIA\\CognitiveDrive.CoreUIA.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"System.CommandLine": {"target": "Package", "version": "[2.0.0-beta4.22272.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}